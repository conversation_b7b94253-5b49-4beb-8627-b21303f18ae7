using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;

namespace ProjectApp.Infrastructure.Repositories
{
    public class DatabaseConnectionRepository : BaseRepository, IDatabaseConnectionRepository
    {
        public DatabaseConnectionRepository(IConfiguration configuration) : base(configuration)
        {
        }

        public async Task<List<DatabaseConnectionDto>> GetAllAsync()
        {
            const string sql = @"
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                ORDER BY Name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<DatabaseConnectionDto>(sql);
            return result.ToList();
        }

        public async Task<DatabaseConnectionDto?> GetByIdAsync(Guid id)
        {
            const string sql = @"
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                WHERE Id = @Id";

            using var connection = CreateConnection();
            return await connection.QueryFirstOrDefaultAsync<DatabaseConnectionDto>(sql, new { Id = id });
        }

        public async Task<DatabaseConnectionDto?> GetByNameAsync(string name)
        {
            const string sql = @"
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                WHERE Name = @Name";

            using var connection = CreateConnection();
            return await connection.QueryFirstOrDefaultAsync<DatabaseConnectionDto>(sql, new { Name = name });
        }

        public async Task<DatabaseConnectionDto> CreateAsync(CreateDatabaseConnectionDto dto)
        {
            const string sql = @"
                INSERT INTO DatabaseConnections (Id, Name, DatabaseType, ConnectionString, Description, IsActive, CreatedAt, IsSchemaExtracted, SchemaExtractionStatus)
                VALUES (@Id, @Name, @DatabaseType, @ConnectionString, @Description, @IsActive, @CreatedAt, @IsSchemaExtracted, @SchemaExtractionStatus);
                
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                WHERE Id = @Id";

            var id = Guid.NewGuid();
            var parameters = new
            {
                Id = id,
                dto.Name,
                dto.DatabaseType,
                dto.ConnectionString,
                dto.Description,
                dto.IsActive,
                CreatedAt = DateTime.UtcNow,
                IsSchemaExtracted = false,
                SchemaExtractionStatus = "NotStarted"
            };

            using var connection = CreateConnection();
            return await connection.QueryFirstAsync<DatabaseConnectionDto>(sql, parameters);
        }

        public async Task<DatabaseConnectionDto> UpdateAsync(UpdateDatabaseConnectionDto dto)
        {
            const string sql = @"
                UPDATE DatabaseConnections 
                SET Name = @Name, DatabaseType = @DatabaseType, ConnectionString = @ConnectionString, 
                    Description = @Description, IsActive = @IsActive, UpdatedAt = @UpdatedAt
                WHERE Id = @Id;
                
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                WHERE Id = @Id";

            var parameters = new
            {
                dto.Id,
                dto.Name,
                dto.DatabaseType,
                dto.ConnectionString,
                dto.Description,
                dto.IsActive,
                UpdatedAt = DateTime.UtcNow
            };

            using var connection = CreateConnection();
            return await connection.QueryFirstAsync<DatabaseConnectionDto>(sql, parameters);
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            const string sql = "DELETE FROM DatabaseConnections WHERE Id = @Id";

            using var connection = CreateConnection();
            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }

        public async Task<ResponseMessage> TestConnectionAsync(TestConnectionDto dto)
        {
            var response = new ResponseMessage();

            try
            {
                using var connection = new SqlConnection(dto.ConnectionString);
                await connection.OpenAsync();

                // Test with a simple query
                await connection.QueryFirstOrDefaultAsync<int>("SELECT 1");

                response.IsError = false;
                response.Message = "Connection successful!";
            }
            catch (Exception ex)
            {
                response.IsError = true;
                response.Message = $"Connection failed: {ex.Message}";
            }

            return response;
        }

        public async Task<bool> UpdateSchemaExtractionStatusAsync(Guid id, string status, string? error = null)
        {
            const string sql = @"
                UPDATE DatabaseConnections 
                SET SchemaExtractionStatus = @Status, 
                    SchemaExtractionError = @Error,
                    LastSchemaExtraction = @LastExtraction,
                    IsSchemaExtracted = @IsExtracted
                WHERE Id = @Id";

            var parameters = new
            {
                Id = id,
                Status = status,
                Error = error,
                LastExtraction = DateTime.UtcNow,
                IsExtracted = status == "Success"
            };

            using var connection = CreateConnection();
            var rowsAffected = await connection.ExecuteAsync(sql, parameters);
            return rowsAffected > 0;
        }

        public async Task<List<DatabaseConnectionDto>> GetActiveConnectionsAsync()
        {
            const string sql = @"
                SELECT Id, Name, DatabaseType, ConnectionString, Description, IsActive, 
                       CreatedAt, UpdatedAt, CreatedBy, UpdatedBy, IsSchemaExtracted, 
                       LastSchemaExtraction, SchemaExtractionStatus, SchemaExtractionError
                FROM DatabaseConnections 
                WHERE IsActive = 1
                ORDER BY Name";

            using var connection = CreateConnection();
            var result = await connection.QueryAsync<DatabaseConnectionDto>(sql);
            return result.ToList();
        }

        public async Task<bool> ExistsAsync(string name)
        {
            const string sql = "SELECT COUNT(1) FROM DatabaseConnections WHERE Name = @Name";

            using var connection = CreateConnection();
            var count = await connection.QueryFirstAsync<int>(sql, new { Name = name });
            return count > 0;
        }


    }
}
