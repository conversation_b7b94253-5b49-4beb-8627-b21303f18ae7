﻿using Microsoft.SemanticKernel;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Services
{
    /// <summary>
    /// Function invocation filter to perform logging and track function invocations for chat sources.
    /// </summary>
    public sealed class LoggingFilter : IFunctionInvocationFilter
    {
        private readonly IChatSourceService _chatSourceService;
        private readonly ILogger<LoggingFilter> _logger;

        public LoggingFilter(IChatSourceService chatSourceService, ILogger<LoggingFilter> logger)
        {
            _chatSourceService = chatSourceService;
            _logger = logger;
        }

        public async Task OnFunctionInvocationAsync(FunctionInvocationContext context, Func<FunctionInvocationContext, Task> next)
        {
            var pluginName = context.Function.PluginName;
            var functionName = context.Function.Name;
            var pluginDescription = context.Function.Description;

            // Log before execution
            _logger.LogInformation("🔧 Executing {PluginName}.{FunctionName}: {Description}",
                pluginName, functionName, pluginDescription);

            // Add step announcement to chat sources for tracking
            var stepMessage = GetStepMessage(pluginName, functionName);
            if (!string.IsNullOrEmpty(stepMessage))
            {
                _chatSourceService.TrackFunctionInvocation(pluginName, stepMessage, functionName, "Step started");
            }

            await next(context);

            var result = context.Result.ToString();

            // Log the result with formatting
            var formattedResult = FormatPluginResult(pluginName, functionName, result);
            _logger.LogInformation("✅ {PluginName}.{FunctionName} completed: {Result}",
                pluginName, functionName, formattedResult);

            // Track the function invocation for chat sources
            if (!string.IsNullOrEmpty(pluginName) && !string.IsNullOrEmpty(functionName) && !string.IsNullOrEmpty(result))
            {
                _chatSourceService.TrackFunctionInvocation(pluginName, pluginDescription, functionName, formattedResult);
            }
        }

        private string GetStepMessage(string pluginName, string functionName)
        {
            return (pluginName, functionName) switch
            {
                ("DatabaseSqlGeneratorPlugin", "GetSchemaContext") => "\n🔍 **Step 1: Getting database schema...**\n",
                ("DatabaseSqlGeneratorPlugin", "GenerateSqlQuery") => "\n🧠 **Step 2: Generating SQL query...**\n",
                ("SqlExecutorPlugin", "ExecuteSqlQuery") => "\n⚡ **Step 3: Executing database query...**\n",
                _ => ""
            };
        }

        private string FormatPluginResult(string pluginName, string functionName, string result)
        {
            try
            {
                // Try to parse as JSON for better formatting
                var jsonDoc = JsonDocument.Parse(result);

                return (pluginName, functionName) switch
                {
                    ("DatabaseSqlGeneratorPlugin", "GetSchemaContext") =>
                        "✅ Schema retrieved successfully",
                    ("DatabaseSqlGeneratorPlugin", "GenerateSqlQuery") =>
                        FormatSqlQueryResult(jsonDoc),
                    ("SqlExecutorPlugin", "ExecuteSqlQuery") =>
                        FormatSqlExecutionResult(jsonDoc),
                    _ => result
                };
            }
            catch
            {
                return result;
            }
        }

        private string FormatSqlQueryResult(JsonDocument jsonDoc)
        {
            try
            {
                if (jsonDoc.RootElement.TryGetProperty("sql_query", out var sqlQuery) ||
                    jsonDoc.RootElement.TryGetProperty("query", out sqlQuery))
                {
                    var sql = sqlQuery.GetString();
                    return $"\n✅ **Generated SQL Query:**\n```sql\n{sql}\n```\n";
                }
            }
            catch { }
            return "\n✅ SQL query generated successfully\n";
        }

        private string FormatSqlExecutionResult(JsonDocument jsonDoc)
        {
            try
            {
                if (jsonDoc.RootElement.TryGetProperty("Success", out var success) && success.GetBoolean())
                {
                    if (jsonDoc.RootElement.TryGetProperty("RowCount", out var rowCount))
                    {
                        var executionTime = "";
                        if (jsonDoc.RootElement.TryGetProperty("ExecutionTimeMs", out var timeMs))
                        {
                            executionTime = $" in {timeMs.GetDouble():F1}ms";
                        }
                        return $"\n✅ **Query executed successfully!**\n📊 **Results:** {rowCount.GetInt32()} rows returned{executionTime}\n";
                    }
                }
                else if (jsonDoc.RootElement.TryGetProperty("success", out var successLower) && successLower.GetBoolean())
                {
                    if (jsonDoc.RootElement.TryGetProperty("row_count", out var rowCountLower))
                    {
                        return $"\n✅ **Query executed successfully!**\n📊 **Results:** {rowCountLower.GetInt32()} rows returned\n";
                    }
                }
            }
            catch { }
            return "\n✅ Query executed successfully\n";
        }
    }
}
