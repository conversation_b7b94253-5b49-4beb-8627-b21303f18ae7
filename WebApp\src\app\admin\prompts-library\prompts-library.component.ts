import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { PromptLibraryServiceProxy, PromptLibraryResponseDto } from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AddOrEditPromptLibraryComponent } from './add-or-edit-prompt-library/add-or-edit-prompt-library.component';
import { AuthService } from '../../../shared/services/auth.service';
import { Router } from '@angular/router';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
@Component({
  selector: 'app-prompts-library',
  standalone: true,
  imports: [CommonModule, FormsModule, ServiceProxyModule, NzModalModule, SpinnerComponent],
  templateUrl: './prompts-library.component.html',
  styleUrls: ['./prompts-library.component.css']
})
export class PromptsLibraryComponent implements OnInit {
  prompts: PromptLibraryResponseDto[] = [];
  filteredPrompts: PromptLibraryResponseDto[] = [];
  paginatedPrompts: PromptLibraryResponseDto[] = [];
  isUpdating = false;
  isLoading = false; // Loading state for spinner
  searchTerm = '';
  selectedFilter = 'all';
  prompt: any = { id: 0, prompt: '', isGlobal: false, workspaceName: '', userEmail: '' };
  workspaceName: string | undefined = '';

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  previousPageSize: number = 10;

  // Make Math available to the template
  Math = Math;

  constructor(
    private promptsService: PromptLibraryServiceProxy,
    public modal: NzModalService,
    private message: NzMessageService,
    public authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    const routerSegments = this.router.url.split('/');

    // Only allow access from settings page
    if (!routerSegments.includes('settings') || !routerSegments.includes('prompt-library')) {
      // Redirect to settings if not accessed from settings page
      this.router.navigate(['/settings', 'prompt-library']);
      return;
    }

    // Ensure pageSize is a number and set previousPageSize
    this.pageSize = Number(this.pageSize);
    this.previousPageSize = this.pageSize;

    this.getPrompts();
  }


  get isAdmin() {
    return this.authService.isAdmin();
  }
  getPrompts() {
    this.isLoading = true; // Show spinner
    var userEmail = this.authService.getUser().email;
    var isAdmin = this.authService.isAdmin();
    var email = isAdmin ? undefined : userEmail;
    // Get all prompts without workspace filtering since we're in settings
    this.promptsService.getAll(undefined, undefined, email).subscribe({
      next: (res: any) => {
        if (res) {
          this.prompts = res;
          console.log(this.prompts);
          this.filterPrompts();
        }
        this.isLoading = false; // Hide spinner
      },
      error: (error) => {
        console.error('Error fetching prompts:', error);
        this.message.error('Failed to load prompts');
        this.isLoading = false; // Hide spinner even on error
      }
    });
  }

  filterPrompts() {
    let filtered = [...this.prompts];

    // Apply search filter if there's a search term
    if (this.searchTerm?.trim()) {
      const searchLower = this.searchTerm.trim().toLowerCase();
      filtered = filtered.filter(prompt =>
        (prompt.prompt?.toLowerCase() || '').includes(searchLower) ||
        (prompt.shortMessage?.toLowerCase() || '').includes(searchLower) ||
        (prompt.workspaceName?.toLowerCase() || '').includes(searchLower) ||
        (prompt.agentName?.toLowerCase() || '').includes(searchLower)
      );
    }

    // Apply type filter
    switch (this.selectedFilter) {
      case 'workspace':
        filtered = filtered.filter(prompt => prompt.workspaceName && !prompt.agentName);
        break;
      case 'agent':
        filtered = filtered.filter(prompt => prompt.agentName && !prompt.workspaceName);
        break;
      // 'all' case - no filtering needed
    }

    this.filteredPrompts = filtered;
    this.updatePagination();
  }

  /**
   * Update pagination based on current page and page size
   */
  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(1, Math.ceil(this.filteredPrompts.length / this.pageSize));

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Get current page of prompts
    if (this.filteredPrompts.length === 0) {
      this.paginatedPrompts = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredPrompts.length);
      this.paginatedPrompts = this.filteredPrompts.slice(startIndex, endIndex);
    }

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredPrompts.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedPrompts.length
    });
  }

  /**
   * Go to a specific page
   */
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  /**
   * Go to the previous page
   */
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  /**
   * Go to the next page
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  // Update search term and trigger filtering
  onSearchChange(value: string) {
    this.searchTerm = value;
    this.filterPrompts();
  }

  // Clear search
  clearSearch() {
    this.searchTerm = '';
    this.filterPrompts();
  }

  resetForm() {
    this.prompt = {
      id: 0,
      prompt: '',
      shortMessage: '',
      workspaceName: '',
      agentName: '',
      userEmail: ''
    };
  }

  openAddEditModal(prompt?: any) {
    this.isUpdating = !!prompt;
    if (prompt) {
      this.prompt = { ...prompt };
    } else {
      this.resetForm();
    }

    const modalRef = this.modal.create({
      nzTitle: this.isUpdating ? 'Update Prompt' : 'Add Prompt',
      nzContent: AddOrEditPromptLibraryComponent,
      nzData: {
        isUpdating: this.isUpdating,
        promptData: this.prompt
      },
      nzFooter: null,
      nzWidth: 500,
      nzClassName: 'prompt-modal',
    });

    modalRef.afterClose.subscribe((result) => {
      if (result) {
        if (this.isUpdating) {
          this.updatePrompt(result);
        } else {
          this.createPrompt(result);
        }
      }
    });
  }

  createPrompt(prompt: any) {
    this.isLoading = true; // Show spinner
    prompt.userEmail = this.authService.getUser().email;

    console.log('Creating prompt with data:', prompt);

    this.promptsService.createOrUpdate(prompt).subscribe({
      next: () => {
        this.message.success('Prompt created successfully');
        this.getPrompts(); // This will handle hiding the spinner
        this.modal.closeAll();
      },
      error: (error: any) => {
        if (error.isError && error.message) {
          this.message.error(error.message);
        } else {
          this.message.error('Failed to create prompt');
          console.error('Failed to create prompt:', error);
        }
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  updatePrompt(prompt: any) {
    this.isLoading = true; // Show spinner

    console.log('Updating prompt with data:', prompt);

    this.promptsService.createOrUpdate(prompt).subscribe({
      next: () => {
        this.message.success('Prompt updated successfully');
        this.getPrompts(); // This will handle hiding the spinner
        this.modal.closeAll();
      },
      error: (error: any) => {
        if (error.isError && error.message) {
          this.message.error(error.message);
        } else {
          this.message.error('Failed to update prompt');
          console.error('Failed to update prompt:', error);
        }
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  deletePrompt(prompt: any) {
    this.modal.confirm({
      nzTitle: 'Are you sure you want to delete this prompt?',
      nzContent: 'This action cannot be undone.',
      nzOkText: 'Yes',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzWrapClassName :'custom-delete-modal',
      nzOnOk: () => {
        this.isLoading = true; // Show spinner
        var isAdmin = this.authService.isAdmin();
        var email = isAdmin ? undefined : this.authService.getUser().email;
        this.promptsService.delete(prompt.id, email).subscribe({
          next: () => {
            this.message.success('Prompt deleted successfully');
            this.getPrompts(); // This will handle hiding the spinner
          },
          error: (error: any) => {
            if (error.isError && error.message) {
              this.message.error(error.message);
            } else {
              this.message.error('Failed to delete prompt');
              console.error('Failed to delete prompt:', error);
            }
            this.isLoading = false; // Hide spinner on error
          }
        });
      },
      nzCancelText: 'No',
    });
  }
}
