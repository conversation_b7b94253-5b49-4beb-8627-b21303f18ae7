using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Infrastructure;
using Dapper;
using ProjectApp.Core.Models;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NavigationController : ControllerBase
    {
        private readonly AIService _aiService;

        public NavigationController(AIService aiService)
        {
            _aiService = aiService;
        }

        [HttpPost("SeedDefaultNavigation")]
        public async Task<IActionResult> SeedDefaultNavigation()
        {
            try
            {
                var navigationEntries = GetCoreNavigationEntries();
                List<string> addedEntries = new List<string>();

                // Add all core navigation entries to kernel memory
                foreach (var entry in navigationEntries)
                {
                    await _aiService.AddNavigationEntry(entry);
                    addedEntries.Add(entry.Title);
                }

                return Ok(new
                {
                    message = $"Successfully added {navigationEntries.Count()} default navigation entries",
                    entries = addedEntries
                });
            }
            catch (Exception ex)
            {
                return StatusCode(
                    500,
                    new
                    {
                        message = $"Error initializing default navigation: {ex.Message}",
                        details = ex.StackTrace
                    });
            }
        }

        [HttpPost("AddNavigationEntry")]
        public async Task<IActionResult> AddNavigationEntry(
            [FromBody] NavigationEntryDto navigationEntry)
        {
            try
            {
                await _aiService.AddNavigationEntry(navigationEntry);
                return Ok(new { message = "Navigation entry added successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost("SearchNavigation")]
        public async Task<ActionResult<List<NavigationSearchResult>>> SearchNavigation(string query)
        {
            try
            {
                var results = await _aiService.SearchNavigationMemory(query);

                var searchResults = results
                    .Select(
                        r => new NavigationSearchResult
                        {
                            Id = r.Id,
                            Title = r.Title,
                            Description = r.Description,
                            NavigationType = r.NavigationType,
                            Route = r.Route,
                            Icon = r.Icon
                        })
                    .ToList();

                return Ok(searchResults);
            }
            catch (Exception ex)
            {
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPost("SeedAgentNavigation")]
        public async Task<ActionResult<ResponseMessage>> SeedAgentNavigation()
        {
            try
            {
                // Get access to the agent repository
                var dbConnection = _aiService.GetDbConnection();
                // We need to get all agents from the database
                string sql = @"SELECT * FROM AgentDefinitions";
                var agents = dbConnection.Query<AgentDefinition>(sql).ToList();

                int successCount = 0;
                int errorCount = 0;
                List<string> errorMessages = new List<string>();

                foreach (var agent in agents)
                {
                    try
                    {
                        // Create chat navigation entry (for chat page)
                        var chatEntry = new NavigationEntryDto
                        {
                            Id = "Navigation-Chat-" + agent.AgentName,
                            Title = agent.AgentName,
                            Description = $"Chat with {agent.AgentName}" + (!string.IsNullOrEmpty(agent.UserInstructions) ? $" - {agent.UserInstructions}" : ""),
                            NavigationType = "Agent",
                            Route = $"/chat/agent/{agent.AgentName}",
                            Icon = "ri-chat-3-line",
                        };

                        // Create config navigation entry (for settings page)
                        var configEntry = new NavigationEntryDto
                        {
                            Id = "Navigation-Config-" + agent.AgentName,
                            Title = $"{agent.AgentName} (Config)",
                            Description = $"Configure and edit the {agent.AgentName} agent",
                            NavigationType = "Settings",
                            Route = $"/settings/agents/{agent.AgentName}",
                            Icon = "ri-settings-line",
                        };

                        // Add both entries to Kernel Memory
                        await _aiService.AddNavigationEntry(chatEntry);
                        await _aiService.AddNavigationEntry(configEntry);

                        successCount += 2; // Two entries per agent
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        errorMessages.Add($"Error seeding navigation for agent {agent.AgentName}: {ex.Message}");
                    }
                }

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Navigation seeding complete. Added {successCount} entries. {errorCount} errors. {string.Join(", ", errorMessages)}"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Failed to seed agent navigation: {ex.Message}" });
            }
        }

        [HttpPost("SeedWorkspaceNavigation")]
        public async Task<ActionResult<ResponseMessage>> SeedWorkspaceNavigation()
        {
            try
            {
                // Get access to the database connection
                var dbConnection = _aiService.GetDbConnection();

                // Get all workspaces from the database
                string sql = @"SELECT * FROM Workspaces";
                var workspaces = dbConnection.Query<Workspace>(sql).ToList();

                int successCount = 0;
                int errorCount = 0;
                List<string> errorMessages = new List<string>();

                foreach (var workspace in workspaces)
                {
                    try
                    {
                        // Create workspace navigation entry
                        var entry = new NavigationEntryDto
                        {
                            Id = "Navigation-Chat" + workspace.Id,
                            Title = workspace.Title,
                            Description = workspace.Description ?? $"Chat with {workspace.Title} workspace",
                            NavigationType = "Workspace",
                            Route = $"/chat/workspace/{workspace.Title}",
                            Icon = "ri-folder-line",
                        };

                        // Add entry to Kernel Memory
                        await _aiService.AddNavigationEntry(entry);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        errorMessages.Add($"Error seeding navigation for workspace {workspace.Title}: {ex.Message}");
                    }
                }

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Workspace navigation seeding complete. Added {successCount} entries. {errorCount} errors. {string.Join(", ", errorMessages)}"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Failed to seed workspace navigation: {ex.Message}" });
            }
        }

        [HttpPost("SeedPluginNavigation")]
        public async Task<ActionResult<ResponseMessage>> SeedPluginNavigation()
        {
            try
            {
                // Get access to the database connection
                var dbConnection = _aiService.GetDbConnection();                // Get all plugins from the database
                string sql = @"SELECT * FROM Plugins ORDER BY PluginName";
                var plugins = dbConnection.Query<PluginRecord>(sql).ToList();

                int successCount = 0;
                int errorCount = 0;
                List<string> errorMessages = new List<string>();

                // First, delete all existing plugin navigation entries
                try
                {
                    // Search for existing plugin navigation entries
                    Console.WriteLine("Searching for existing plugin navigation entries...");
                    var searchResults = await _aiService.SearchNavigationMemory("plugin settings", 0.1);

                    var existingIds = new HashSet<string>();
                    foreach (var result in searchResults)
                    {
                        if (!string.IsNullOrEmpty(result.Id) && result.NavigationType == "Plugin")
                        {
                            existingIds.Add(result.Id);
                        }
                    }

                    Console.WriteLine($"Found {existingIds.Count} existing plugin navigation entries to delete");
                    foreach (var id in existingIds)
                    {
                        await _aiService.DeleteSingleMemory(id, "Navigation");
                    }
                    Console.WriteLine("Deleted existing plugin navigation entries");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error while trying to delete existing plugin entries: {ex.Message}");
                }

                foreach (var plugin in plugins)
                {
                    try
                    {                        // Create plugin navigation entry
                        var entry = new NavigationEntryDto
                        {
                            Id = "Navigation-Plugin-" + plugin.Id,
                            Title = plugin.PluginName,
                            Description = $"Configure {plugin.PluginName} plugin settings",
                            NavigationType = "Plugin",
                            Route = $"/settings/plugins/{plugin.PluginName}",
                            Icon = "ri-plug-line",
                        };

                        // Add entry to Kernel Memory
                        await _aiService.AddNavigationEntry(entry);
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        errorMessages.Add($"Error seeding navigation for plugin {plugin.PluginName}: {ex.Message}");
                    }
                }

                return Ok(new ResponseMessage
                {
                    IsError = false,
                    Message = $"Plugin navigation seeding complete. Added {successCount} entries. {errorCount} errors. {string.Join(", ", errorMessages)}"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = $"Failed to seed plugin navigation: {ex.Message}" });
            }
        }

        #region Private Methods

        private IEnumerable<NavigationEntryDto> GetCoreNavigationEntries()
        {
            var entries = new List<NavigationEntryDto>
            {
                // Daily Insight (Home)
                new NavigationEntryDto
                {
                    Id = "nav-home",
                    Title = "Home",
                    Description = "Daily insights and overview dashboard",
                    NavigationType = "page",
                    Route = "/",
                    Icon = "ri-home-line"
                },
                // Notes and Documents
                new NavigationEntryDto
                {
                    Id = "nav-notes",
                    Title = "Notes & Documents",
                    Description = "Manage personal notes and documents",
                    NavigationType = "notes",
                    Route = "/notes",
                    Icon = "ri-file-text-line"
                },
                // Chat (Testing)
                new NavigationEntryDto
                {
                    Id = "nav-chat",
                    Title = "Chat",
                    Description = "AI chat interface",
                    NavigationType = "chat",
                    Route = "/chat",
                    Icon = "ri-chat-3-line"
                },
                // Workspaces
                new NavigationEntryDto
                {
                    Id = "nav-workspaces",
                    Title = "Workspaces",
                    Description = "Browse and manage your workspaces",
                    NavigationType = "workspace",
                    Route = "/workspaces",
                    Icon = "ri-folder-line"
                },
                // Settings - User Management
                new NavigationEntryDto
                {
                    Id = "nav-settings-users",
                    Title = "User Management",
                    Description = "Manage users and permissions",
                    NavigationType = "admin",
                    Route = "/settings/user-management",
                    Icon = "ri-user-settings-line"
                },
                // Settings - Connections
                new NavigationEntryDto
                {
                    Id = "nav-settings-connections",
                    Title = "Connections",
                    Description = "Manage system connections and integrations",
                    NavigationType = "settings",
                    Route = "/settings/connections",
                    Icon = "ri-link-m"
                },
                // Settings - AI Models
                new NavigationEntryDto
                {
                    Id = "nav-settings-models",
                    Title = "AI Models",
                    Description = "Configure and manage AI models",
                    NavigationType = "settings",
                    Route = "/settings/models",
                    Icon = "ri-brain-line"
                },
                // Settings - Agents
                new NavigationEntryDto
                {
                    Id = "nav-settings-agents",
                    Title = "Agent Management",
                    Description = "Create and configure AI agents",
                    NavigationType = "settings",
                    Route = "/settings/agents",
                    Icon = "ri-robot-line"
                },
                // Settings - Prompt Library
                new NavigationEntryDto
                {
                    Id = "nav-settings-prompts",
                    Title = "Prompt Library",
                    Description = "Manage prompt templates and library",
                    NavigationType = "settings",
                    Route = "/settings/prompt-library",
                    Icon = "ri-chat-quote-line"
                },
                // Settings - Plugins
                new NavigationEntryDto
                {
                    Id = "nav-settings-plugins",
                    Title = "Plugin Management",
                    Description = "Install and configure plugins and extensions",
                    NavigationType = "settings",
                    Route = "/settings/plugins",
                    Icon = "ri-plug-line"
                },
                // Settings - Memory
                new NavigationEntryDto
                {
                    Id = "nav-settings-memory",
                    Title = "Memory Management",
                    Description = "Manage system memory and embeddings",
                    NavigationType = "settings",
                    Route = "/settings/memory",
                    Icon = "ri-database-2-line"
                },
                // Settings - Files
                new NavigationEntryDto
                {
                    Id = "nav-settings-files",
                    Title = "Files Management",
                    Description = "Upload and manage system files",
                    NavigationType = "settings",
                    Route = "/settings/files",
                    Icon = "ri-file-line"
                }
            };

            return entries;
        }

        #endregion

        #region DTOs

        public class NavigationSearchRequest
        {
            public string Query { get; set; } = string.Empty;
            public double? Threshold { get; set; }
        }

        public class PluginRecord
        {
            public Guid Id { get; set; }
            public string PluginName { get; set; } = string.Empty;
            public string Type { get; set; } = string.Empty;
            public string Functions { get; set; } = string.Empty;
            public string Url { get; set; } = string.Empty;
            public DateTime CreatedDate { get; set; }
            public DateTime? LastModifiedDate { get; set; }
            public string RequiredParameters { get; set; } = string.Empty;
            public string EnvironmentVariables { get; set; } = string.Empty;
        }

        #endregion
    }
}
