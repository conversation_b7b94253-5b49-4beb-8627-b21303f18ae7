{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-tooltip.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i3 from '@angular/cdk/overlay';\nimport { OverlayModule } from '@angular/cdk/overlay';\nimport { isPlatformBrowser, NgClass, NgStyle } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, ElementRef, ViewContainerRef, Renderer2, PLATFORM_ID, Directive, Optional, ViewChild, TemplateRef, Input, Output, Component, ChangeDetectionStrategy, ViewEncapsulation, Host, NgModule } from '@angular/core';\nimport { zoomBigMotion } from 'ng-zorro-antd/core/animation';\nimport { isPresetColor } from 'ng-zorro-antd/core/color';\nimport * as i2 from 'ng-zorro-antd/core/no-animation';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i5 from 'ng-zorro-antd/core/overlay';\nimport { POSITION_MAP, DEFAULT_TOOLTIP_POSITIONS, getPlacementName, NzOverlayModule } from 'ng-zorro-antd/core/overlay';\nimport { toBoolean, isNotNil, InputBoolean } from 'ng-zorro-antd/core/util';\nimport { Subject, asapScheduler } from 'rxjs';\nimport { distinctUntilChanged, takeUntil, filter, delay } from 'rxjs/operators';\nimport { NzConfigService } from 'ng-zorro-antd/core/config';\nimport * as i1 from '@angular/cdk/bidi';\nconst _c0 = [\"overlay\"];\nfunction NzToolTipComponent_ng_template_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.nzTitle);\n  }\n}\nfunction NzToolTipComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4);\n    i0.ɵɵelement(3, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 6);\n    i0.ɵɵtemplate(5, NzToolTipComponent_ng_template_0_ng_container_5_Template, 2, 1, \"ng-container\", 7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-tooltip-rtl\", ctx_r1.dir === \"rtl\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r1._classMap)(\"ngStyle\", ctx_r1.nzOverlayStyle)(\"@.disabled\", !!(ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation))(\"nzNoAnimation\", ctx_r1.noAnimation == null ? null : ctx_r1.noAnimation.nzNoAnimation)(\"@zoomBigMotion\", \"active\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1._contentStyleMap);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzTitle)(\"nzStringTemplateOutletContext\", ctx_r1.nzTitleContext);\n  }\n}\nclass NzTooltipBaseDirective {\n  /**\n   * This true title that would be used in other parts on this component.\n   */\n  get _title() {\n    return this.title || this.directiveTitle || null;\n  }\n  get _content() {\n    return this.content || this.directiveContent || null;\n  }\n  get _trigger() {\n    return typeof this.trigger !== 'undefined' ? this.trigger : 'hover';\n  }\n  get _placement() {\n    const p = this.placement;\n    return Array.isArray(p) && p.length > 0 ? p : typeof p === 'string' && p ? [p] : ['top'];\n  }\n  get _visible() {\n    return (typeof this.visible !== 'undefined' ? this.visible : this.internalVisible) || false;\n  }\n  get _mouseEnterDelay() {\n    return this.mouseEnterDelay || 0.15;\n  }\n  get _mouseLeaveDelay() {\n    return this.mouseLeaveDelay || 0.1;\n  }\n  get _overlayClassName() {\n    return this.overlayClassName || null;\n  }\n  get _overlayStyle() {\n    return this.overlayStyle || null;\n  }\n  getProxyPropertyMap() {\n    return {\n      noAnimation: ['noAnimation', () => !!this.noAnimation]\n    };\n  }\n  constructor(componentType) {\n    this.componentType = componentType;\n    this.visibleChange = new EventEmitter();\n    this.internalVisible = false;\n    this.destroy$ = new Subject();\n    this.triggerDisposables = [];\n    this.elementRef = inject(ElementRef);\n    this.hostView = inject(ViewContainerRef);\n    this.renderer = inject(Renderer2);\n    this.noAnimation = inject(NzNoAnimationDirective, {\n      host: true,\n      optional: true\n    });\n    this.nzConfigService = inject(NzConfigService);\n    this.platformId = inject(PLATFORM_ID);\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      this.createComponent();\n      this.registerTriggers();\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      trigger\n    } = changes;\n    if (trigger && !trigger.isFirstChange()) {\n      this.registerTriggers();\n    }\n    if (this.component) {\n      this.updatePropertiesByChanges(changes);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    // Clear toggling timer. Issue #3875 #4317 #4386\n    this.clearTogglingTimer();\n    this.removeTriggerListeners();\n  }\n  show() {\n    this.component?.show();\n  }\n  hide() {\n    this.component?.hide();\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.component) {\n      this.component.updatePosition();\n    }\n  }\n  /**\n   * Create a dynamic tooltip component. This method can be override.\n   */\n  createComponent() {\n    const componentRef = this.hostView.createComponent(this.componentType);\n    this.component = componentRef.instance;\n    // Remove the component's DOM because it should be in the overlay container.\n    this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), componentRef.location.nativeElement);\n    this.component.setOverlayOrigin(this.origin || this.elementRef);\n    this.initProperties();\n    const ngVisibleChange$ = this.component.nzVisibleChange.pipe(distinctUntilChanged());\n    ngVisibleChange$.pipe(takeUntil(this.destroy$)).subscribe(visible => {\n      this.internalVisible = visible;\n      this.visibleChange.emit(visible);\n    });\n    // In some cases, the rendering takes into account the height at which the `arrow` is in wrong place,\n    // so `cdk` sets the container position incorrectly.\n    // To avoid this, after placing the `arrow` in the correct position, we should `re-calculate` the position of the `overlay`.\n    ngVisibleChange$.pipe(filter(visible => visible), delay(0, asapScheduler), filter(() => Boolean(this.component?.overlay?.overlayRef)), takeUntil(this.destroy$)).subscribe(() => {\n      this.component?.updatePosition();\n    });\n  }\n  registerTriggers() {\n    // When the method gets invoked, all properties has been synced to the dynamic component.\n    // After removing the old API, we can just check the directive's own `nzTrigger`.\n    const el = this.elementRef.nativeElement;\n    const trigger = this.trigger;\n    this.removeTriggerListeners();\n    if (trigger === 'hover') {\n      let overlayElement;\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseenter', () => {\n        this.delayEnterLeave(true, true, this._mouseEnterDelay);\n      }));\n      this.triggerDisposables.push(this.renderer.listen(el, 'mouseleave', () => {\n        this.delayEnterLeave(true, false, this._mouseLeaveDelay);\n        if (this.component?.overlay.overlayRef && !overlayElement) {\n          overlayElement = this.component.overlay.overlayRef.overlayElement;\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseenter', () => {\n            this.delayEnterLeave(false, true, this._mouseEnterDelay);\n          }));\n          this.triggerDisposables.push(this.renderer.listen(overlayElement, 'mouseleave', () => {\n            this.delayEnterLeave(false, false, this._mouseLeaveDelay);\n          }));\n        }\n      }));\n    } else if (trigger === 'focus') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusin', () => this.show()));\n      this.triggerDisposables.push(this.renderer.listen(el, 'focusout', () => this.hide()));\n    } else if (trigger === 'click') {\n      this.triggerDisposables.push(this.renderer.listen(el, 'click', e => {\n        e.preventDefault();\n        this.show();\n      }));\n    }\n    // Else do nothing because user wants to control the visibility programmatically.\n  }\n  updatePropertiesByChanges(changes) {\n    this.updatePropertiesByKeys(Object.keys(changes));\n  }\n  updatePropertiesByKeys(keys) {\n    const mappingProperties = {\n      // common mappings\n      title: ['nzTitle', () => this._title],\n      directiveTitle: ['nzTitle', () => this._title],\n      content: ['nzContent', () => this._content],\n      directiveContent: ['nzContent', () => this._content],\n      trigger: ['nzTrigger', () => this._trigger],\n      placement: ['nzPlacement', () => this._placement],\n      visible: ['nzVisible', () => this._visible],\n      mouseEnterDelay: ['nzMouseEnterDelay', () => this._mouseEnterDelay],\n      mouseLeaveDelay: ['nzMouseLeaveDelay', () => this._mouseLeaveDelay],\n      overlayClassName: ['nzOverlayClassName', () => this._overlayClassName],\n      overlayStyle: ['nzOverlayStyle', () => this._overlayStyle],\n      arrowPointAtCenter: ['nzArrowPointAtCenter', () => this.arrowPointAtCenter],\n      cdkConnectedOverlayPush: ['cdkConnectedOverlayPush', () => this.cdkConnectedOverlayPush],\n      ...this.getProxyPropertyMap()\n    };\n    (keys || Object.keys(mappingProperties).filter(key => !key.startsWith('directive'))).forEach(property => {\n      if (mappingProperties[property]) {\n        const [name, valueFn] = mappingProperties[property];\n        this.updateComponentValue(name, valueFn());\n      }\n    });\n    this.component?.updateByDirective();\n  }\n  initProperties() {\n    this.updatePropertiesByKeys();\n  }\n  updateComponentValue(key, value) {\n    if (typeof value !== 'undefined') {\n      // @ts-ignore\n      this.component[key] = value;\n    }\n  }\n  delayEnterLeave(isOrigin, isEnter, delay = -1) {\n    if (this.delayTimer) {\n      this.clearTogglingTimer();\n    } else if (delay > 0) {\n      this.delayTimer = setTimeout(() => {\n        this.delayTimer = undefined;\n        isEnter ? this.show() : this.hide();\n      }, delay * 1000);\n    } else {\n      // `isOrigin` is used due to the tooltip will not hide immediately\n      // (may caused by the fade-out animation).\n      isEnter && isOrigin ? this.show() : this.hide();\n    }\n  }\n  removeTriggerListeners() {\n    this.triggerDisposables.forEach(dispose => dispose());\n    this.triggerDisposables.length = 0;\n  }\n  clearTogglingTimer() {\n    if (this.delayTimer) {\n      clearTimeout(this.delayTimer);\n      this.delayTimer = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function NzTooltipBaseDirective_Factory(t) {\n      return new (t || NzTooltipBaseDirective)(i0.ɵɵdirectiveInject(i0.Type));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTooltipBaseDirective,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseDirective, [{\n    type: Directive\n  }], () => [{\n    type: i0.Type\n  }], null);\n})();\n// eslint-disable-next-line @angular-eslint/directive-class-suffix\nclass NzTooltipBaseComponent {\n  set nzVisible(value) {\n    const visible = toBoolean(value);\n    if (this._visible !== visible) {\n      this._visible = visible;\n      this.nzVisibleChange.next(visible);\n    }\n  }\n  get nzVisible() {\n    return this._visible;\n  }\n  set nzTrigger(value) {\n    this._trigger = value;\n  }\n  get nzTrigger() {\n    return this._trigger;\n  }\n  set nzPlacement(value) {\n    const preferredPosition = value.map(placement => POSITION_MAP[placement]);\n    this._positions = [...preferredPosition, ...DEFAULT_TOOLTIP_POSITIONS];\n  }\n  constructor(cdr, directionality, noAnimation) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.noAnimation = noAnimation;\n    this.nzTitle = null;\n    this.nzContent = null;\n    this.nzArrowPointAtCenter = false;\n    this.nzOverlayStyle = {};\n    this.nzBackdrop = false;\n    this.cdkConnectedOverlayPush = true;\n    this.nzVisibleChange = new Subject();\n    this._visible = false;\n    this._trigger = 'hover';\n    this.preferredPlacement = 'top';\n    this.dir = 'ltr';\n    this._classMap = {};\n    this._prefix = 'ant-tooltip';\n    this._positions = [...DEFAULT_TOOLTIP_POSITIONS];\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.nzVisibleChange.complete();\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  show() {\n    if (this.nzVisible) {\n      return;\n    }\n    if (!this.isEmpty()) {\n      this.nzVisible = true;\n      this.nzVisibleChange.next(true);\n      this.cdr.detectChanges();\n    }\n    // for ltr for overlay to display tooltip in correct placement in rtl direction.\n    if (this.origin && this.overlay && this.overlay.overlayRef && this.overlay.overlayRef.getDirection() === 'rtl') {\n      this.overlay.overlayRef.setDirection('ltr');\n    }\n  }\n  hide() {\n    if (!this.nzVisible) {\n      return;\n    }\n    this.nzVisible = false;\n    this.nzVisibleChange.next(false);\n    this.cdr.detectChanges();\n  }\n  updateByDirective() {\n    this.updateStyles();\n    this.cdr.detectChanges();\n    Promise.resolve().then(() => {\n      this.updatePosition();\n      this.updateVisibilityByTitle();\n    });\n  }\n  /**\n   * Force the component to update its position.\n   */\n  updatePosition() {\n    if (this.origin && this.overlay && this.overlay.overlayRef) {\n      this.overlay.overlayRef.updatePosition();\n    }\n  }\n  onPositionChange(position) {\n    this.preferredPlacement = getPlacementName(position);\n    this.updateStyles();\n    // We have to trigger immediate change detection or the element would blink.\n    this.cdr.detectChanges();\n  }\n  setOverlayOrigin(origin) {\n    this.origin = origin;\n    this.cdr.markForCheck();\n  }\n  onClickOutside(event) {\n    if (!this.origin.nativeElement.contains(event.target) && this.nzTrigger !== null) {\n      this.hide();\n    }\n  }\n  /**\n   * Hide the component while the content is empty.\n   */\n  updateVisibilityByTitle() {\n    if (this.isEmpty()) {\n      this.hide();\n    }\n  }\n  updateStyles() {\n    this._classMap = {\n      [this.nzOverlayClassName]: true,\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true\n    };\n  }\n  static {\n    this.ɵfac = function NzTooltipBaseComponent_Factory(t) {\n      return new (t || NzTooltipBaseComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i2.NzNoAnimationDirective));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTooltipBaseComponent,\n      viewQuery: function NzTooltipBaseComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlay = _t.first);\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipBaseComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.NzNoAnimationDirective\n  }], {\n    overlay: [{\n      type: ViewChild,\n      args: ['overlay', {\n        static: false\n      }]\n    }]\n  });\n})();\nfunction isTooltipEmpty(value) {\n  return value instanceof TemplateRef ? false : value === '' || !isNotNil(value);\n}\nclass NzTooltipDirective extends NzTooltipBaseDirective {\n  constructor() {\n    super(NzToolTipComponent);\n    this.titleContext = null;\n    this.trigger = 'hover';\n    this.placement = 'top';\n    this.cdkConnectedOverlayPush = true;\n    // eslint-disable-next-line @angular-eslint/no-output-rename\n    this.visibleChange = new EventEmitter();\n  }\n  getProxyPropertyMap() {\n    return {\n      ...super.getProxyPropertyMap(),\n      nzTooltipColor: ['nzColor', () => this.nzTooltipColor],\n      titleContext: ['nzTitleContext', () => this.titleContext]\n    };\n  }\n  static {\n    this.ɵfac = function NzTooltipDirective_Factory(t) {\n      return new (t || NzTooltipDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTooltipDirective,\n      selectors: [[\"\", \"nz-tooltip\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzTooltipDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-tooltip-open\", ctx.visible);\n        }\n      },\n      inputs: {\n        title: [i0.ɵɵInputFlags.None, \"nzTooltipTitle\", \"title\"],\n        titleContext: [i0.ɵɵInputFlags.None, \"nzTooltipTitleContext\", \"titleContext\"],\n        directiveTitle: [i0.ɵɵInputFlags.None, \"nz-tooltip\", \"directiveTitle\"],\n        trigger: [i0.ɵɵInputFlags.None, \"nzTooltipTrigger\", \"trigger\"],\n        placement: [i0.ɵɵInputFlags.None, \"nzTooltipPlacement\", \"placement\"],\n        origin: [i0.ɵɵInputFlags.None, \"nzTooltipOrigin\", \"origin\"],\n        visible: [i0.ɵɵInputFlags.None, \"nzTooltipVisible\", \"visible\"],\n        mouseEnterDelay: [i0.ɵɵInputFlags.None, \"nzTooltipMouseEnterDelay\", \"mouseEnterDelay\"],\n        mouseLeaveDelay: [i0.ɵɵInputFlags.None, \"nzTooltipMouseLeaveDelay\", \"mouseLeaveDelay\"],\n        overlayClassName: [i0.ɵɵInputFlags.None, \"nzTooltipOverlayClassName\", \"overlayClassName\"],\n        overlayStyle: [i0.ɵɵInputFlags.None, \"nzTooltipOverlayStyle\", \"overlayStyle\"],\n        arrowPointAtCenter: [i0.ɵɵInputFlags.None, \"nzTooltipArrowPointAtCenter\", \"arrowPointAtCenter\"],\n        cdkConnectedOverlayPush: \"cdkConnectedOverlayPush\",\n        nzTooltipColor: \"nzTooltipColor\"\n      },\n      outputs: {\n        visibleChange: \"nzTooltipVisibleChange\"\n      },\n      exportAs: [\"nzTooltip\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzTooltipDirective.prototype, \"arrowPointAtCenter\", void 0);\n__decorate([InputBoolean()], NzTooltipDirective.prototype, \"cdkConnectedOverlayPush\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTooltipDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-tooltip]',\n      exportAs: 'nzTooltip',\n      host: {\n        '[class.ant-tooltip-open]': 'visible'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    title: [{\n      type: Input,\n      args: ['nzTooltipTitle']\n    }],\n    titleContext: [{\n      type: Input,\n      args: ['nzTooltipTitleContext']\n    }],\n    directiveTitle: [{\n      type: Input,\n      args: ['nz-tooltip']\n    }],\n    trigger: [{\n      type: Input,\n      args: ['nzTooltipTrigger']\n    }],\n    placement: [{\n      type: Input,\n      args: ['nzTooltipPlacement']\n    }],\n    origin: [{\n      type: Input,\n      args: ['nzTooltipOrigin']\n    }],\n    visible: [{\n      type: Input,\n      args: ['nzTooltipVisible']\n    }],\n    mouseEnterDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseEnterDelay']\n    }],\n    mouseLeaveDelay: [{\n      type: Input,\n      args: ['nzTooltipMouseLeaveDelay']\n    }],\n    overlayClassName: [{\n      type: Input,\n      args: ['nzTooltipOverlayClassName']\n    }],\n    overlayStyle: [{\n      type: Input,\n      args: ['nzTooltipOverlayStyle']\n    }],\n    arrowPointAtCenter: [{\n      type: Input,\n      args: ['nzTooltipArrowPointAtCenter']\n    }],\n    cdkConnectedOverlayPush: [{\n      type: Input\n    }],\n    nzTooltipColor: [{\n      type: Input\n    }],\n    visibleChange: [{\n      type: Output,\n      args: ['nzTooltipVisibleChange']\n    }]\n  });\n})();\nclass NzToolTipComponent extends NzTooltipBaseComponent {\n  constructor(cdr, directionality, noAnimation) {\n    super(cdr, directionality, noAnimation);\n    this.nzTitle = null;\n    this.nzTitleContext = null;\n    this._contentStyleMap = {};\n  }\n  isEmpty() {\n    return isTooltipEmpty(this.nzTitle);\n  }\n  updateStyles() {\n    const isColorPreset = this.nzColor && isPresetColor(this.nzColor);\n    this._classMap = {\n      [this.nzOverlayClassName]: true,\n      [`${this._prefix}-placement-${this.preferredPlacement}`]: true,\n      [`${this._prefix}-${this.nzColor}`]: isColorPreset\n    };\n    this._contentStyleMap = {\n      backgroundColor: !!this.nzColor && !isColorPreset ? this.nzColor : null,\n      '--color': this.nzColor\n    };\n  }\n  static {\n    this.ɵfac = function NzToolTipComponent_Factory(t) {\n      return new (t || NzToolTipComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i2.NzNoAnimationDirective, 9));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzToolTipComponent,\n      selectors: [[\"nz-tooltip\"]],\n      exportAs: [\"nzTooltipComponent\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 5,\n      consts: [[\"overlay\", \"cdkConnectedOverlay\"], [\"cdkConnectedOverlay\", \"\", \"nzConnectedOverlay\", \"\", 3, \"overlayOutsideClick\", \"detach\", \"positionChange\", \"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPush\", \"nzArrowPointAtCenter\"], [1, \"ant-tooltip\", 3, \"ngClass\", \"ngStyle\", \"nzNoAnimation\"], [1, \"ant-tooltip-content\"], [1, \"ant-tooltip-arrow\"], [1, \"ant-tooltip-arrow-content\", 3, \"ngStyle\"], [1, \"ant-tooltip-inner\", 3, \"ngStyle\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"]],\n      template: function NzToolTipComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, NzToolTipComponent_ng_template_0_Template, 6, 11, \"ng-template\", 1, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵlistener(\"overlayOutsideClick\", function NzToolTipComponent_Template_ng_template_overlayOutsideClick_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onClickOutside($event));\n          })(\"detach\", function NzToolTipComponent_Template_ng_template_detach_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.hide());\n          })(\"positionChange\", function NzToolTipComponent_Template_ng_template_positionChange_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPositionChange($event));\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"cdkConnectedOverlayOrigin\", ctx.origin)(\"cdkConnectedOverlayOpen\", ctx._visible)(\"cdkConnectedOverlayPositions\", ctx._positions)(\"cdkConnectedOverlayPush\", ctx.cdkConnectedOverlayPush)(\"nzArrowPointAtCenter\", ctx.nzArrowPointAtCenter);\n        }\n      },\n      dependencies: [OverlayModule, i3.CdkConnectedOverlay, NgClass, NgStyle, NzNoAnimationDirective, NzOutletModule, i4.NzStringTemplateOutletDirective, NzOverlayModule, i5.NzConnectedOverlayDirective],\n      encapsulation: 2,\n      data: {\n        animation: [zoomBigMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-tooltip',\n      exportAs: 'nzTooltipComponent',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      animations: [zoomBigMotion],\n      template: `\n    <ng-template\n      #overlay=\"cdkConnectedOverlay\"\n      cdkConnectedOverlay\n      nzConnectedOverlay\n      [cdkConnectedOverlayOrigin]=\"origin\"\n      [cdkConnectedOverlayOpen]=\"_visible\"\n      [cdkConnectedOverlayPositions]=\"_positions\"\n      [cdkConnectedOverlayPush]=\"cdkConnectedOverlayPush\"\n      [nzArrowPointAtCenter]=\"nzArrowPointAtCenter\"\n      (overlayOutsideClick)=\"onClickOutside($event)\"\n      (detach)=\"hide()\"\n      (positionChange)=\"onPositionChange($event)\"\n    >\n      <div\n        class=\"ant-tooltip\"\n        [class.ant-tooltip-rtl]=\"dir === 'rtl'\"\n        [ngClass]=\"_classMap\"\n        [ngStyle]=\"nzOverlayStyle\"\n        [@.disabled]=\"!!noAnimation?.nzNoAnimation\"\n        [nzNoAnimation]=\"noAnimation?.nzNoAnimation\"\n        [@zoomBigMotion]=\"'active'\"\n      >\n        <div class=\"ant-tooltip-content\">\n          <div class=\"ant-tooltip-arrow\">\n            <span class=\"ant-tooltip-arrow-content\" [ngStyle]=\"_contentStyleMap\"></span>\n          </div>\n          <div class=\"ant-tooltip-inner\" [ngStyle]=\"_contentStyleMap\">\n            <ng-container *nzStringTemplateOutlet=\"nzTitle; context: nzTitleContext\">{{ nzTitle }}</ng-container>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      imports: [OverlayModule, NgClass, NgStyle, NzNoAnimationDirective, NzOutletModule, NzOverlayModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.NzNoAnimationDirective,\n    decorators: [{\n      type: Host\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzToolTipModule {\n  static {\n    this.ɵfac = function NzToolTipModule_Factory(t) {\n      return new (t || NzToolTipModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzToolTipModule,\n      imports: [NzToolTipComponent, NzTooltipDirective],\n      exports: [NzToolTipComponent, NzTooltipDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzToolTipComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzToolTipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzToolTipComponent, NzTooltipDirective],\n      exports: [NzToolTipComponent, NzTooltipDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzToolTipComponent, NzToolTipModule, NzTooltipBaseComponent, NzTooltipBaseDirective, NzTooltipDirective, isTooltipEmpty };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,MAAM,CAAC,SAAS;AACtB,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACvD,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,aAAa;AAChB,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,aAAa,EAAE,EAAE;AAAA,EACtB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,mBAAmB,OAAO,QAAQ,KAAK;AACtD,IAAG,WAAW,WAAW,OAAO,SAAS,EAAE,WAAW,OAAO,cAAc,EAAE,cAAc,CAAC,EAAE,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,cAAc,EAAE,iBAAiB,OAAO,eAAe,OAAO,OAAO,OAAO,YAAY,aAAa,EAAE,kBAAkB,QAAQ;AACtR,IAAG,UAAU,CAAC;AACd,IAAG,WAAW,WAAW,OAAO,gBAAgB;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,gBAAgB;AAChD,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO,EAAE,iCAAiC,OAAO,cAAc;AAAA,EAChH;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA;AAAA;AAAA;AAAA,EAI3B,IAAI,SAAS;AACX,WAAO,KAAK,SAAS,KAAK,kBAAkB;AAAA,EAC9C;AAAA,EACA,IAAI,WAAW;AACb,WAAO,KAAK,WAAW,KAAK,oBAAoB;AAAA,EAClD;AAAA,EACA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU;AAAA,EAC9D;AAAA,EACA,IAAI,aAAa;AACf,UAAM,IAAI,KAAK;AACf,WAAO,MAAM,QAAQ,CAAC,KAAK,EAAE,SAAS,IAAI,IAAI,OAAO,MAAM,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK;AAAA,EACzF;AAAA,EACA,IAAI,WAAW;AACb,YAAQ,OAAO,KAAK,YAAY,cAAc,KAAK,UAAU,KAAK,oBAAoB;AAAA,EACxF;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,mBAAmB;AACrB,WAAO,KAAK,mBAAmB;AAAA,EACjC;AAAA,EACA,IAAI,oBAAoB;AACtB,WAAO,KAAK,oBAAoB;AAAA,EAClC;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA,EACA,sBAAsB;AACpB,WAAO;AAAA,MACL,aAAa,CAAC,eAAe,MAAM,CAAC,CAAC,KAAK,WAAW;AAAA,IACvD;AAAA,EACF;AAAA,EACA,YAAY,eAAe;AACzB,SAAK,gBAAgB;AACrB,SAAK,gBAAgB,IAAI,aAAa;AACtC,SAAK,kBAAkB;AACvB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,qBAAqB,CAAC;AAC3B,SAAK,aAAa,OAAO,UAAU;AACnC,SAAK,WAAW,OAAO,gBAAgB;AACvC,SAAK,WAAW,OAAO,SAAS;AAChC,SAAK,cAAc,OAAO,wBAAwB;AAAA,MAChD,MAAM;AAAA,MACN,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,kBAAkB,OAAO,eAAe;AAC7C,SAAK,aAAa,OAAO,WAAW;AAAA,EACtC;AAAA,EACA,kBAAkB;AAChB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAAA,IACxB;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,WAAW,CAAC,QAAQ,cAAc,GAAG;AACvC,WAAK,iBAAiB;AAAA,IACxB;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,0BAA0B,OAAO;AAAA,IACxC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAEvB,SAAK,mBAAmB;AACxB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,WAAW,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,eAAe;AAAA,IAChC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AAChB,UAAM,eAAe,KAAK,SAAS,gBAAgB,KAAK,aAAa;AACrE,SAAK,YAAY,aAAa;AAE9B,SAAK,SAAS,YAAY,KAAK,SAAS,WAAW,KAAK,WAAW,aAAa,GAAG,aAAa,SAAS,aAAa;AACtH,SAAK,UAAU,iBAAiB,KAAK,UAAU,KAAK,UAAU;AAC9D,SAAK,eAAe;AACpB,UAAM,mBAAmB,KAAK,UAAU,gBAAgB,KAAK,qBAAqB,CAAC;AACnF,qBAAiB,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,aAAW;AACnE,WAAK,kBAAkB;AACvB,WAAK,cAAc,KAAK,OAAO;AAAA,IACjC,CAAC;AAID,qBAAiB,KAAK,OAAO,aAAW,OAAO,GAAG,MAAM,GAAG,aAAa,GAAG,OAAO,MAAM,QAAQ,KAAK,WAAW,SAAS,UAAU,CAAC,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC/K,WAAK,WAAW,eAAe;AAAA,IACjC,CAAC;AAAA,EACH;AAAA,EACA,mBAAmB;AAGjB,UAAM,KAAK,KAAK,WAAW;AAC3B,UAAM,UAAU,KAAK;AACrB,SAAK,uBAAuB;AAC5B,QAAI,YAAY,SAAS;AACvB,UAAI;AACJ,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,MAAM,KAAK,gBAAgB;AAAA,MACxD,CAAC,CAAC;AACF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,cAAc,MAAM;AACxE,aAAK,gBAAgB,MAAM,OAAO,KAAK,gBAAgB;AACvD,YAAI,KAAK,WAAW,QAAQ,cAAc,CAAC,gBAAgB;AACzD,2BAAiB,KAAK,UAAU,QAAQ,WAAW;AACnD,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,MAAM,KAAK,gBAAgB;AAAA,UACzD,CAAC,CAAC;AACF,eAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,gBAAgB,cAAc,MAAM;AACpF,iBAAK,gBAAgB,OAAO,OAAO,KAAK,gBAAgB;AAAA,UAC1D,CAAC,CAAC;AAAA,QACJ;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,WAAW,MAAM,KAAK,KAAK,CAAC,CAAC;AACnF,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,YAAY,MAAM,KAAK,KAAK,CAAC,CAAC;AAAA,IACtF,WAAW,YAAY,SAAS;AAC9B,WAAK,mBAAmB,KAAK,KAAK,SAAS,OAAO,IAAI,SAAS,OAAK;AAClE,UAAE,eAAe;AACjB,aAAK,KAAK;AAAA,MACZ,CAAC,CAAC;AAAA,IACJ;AAAA,EAEF;AAAA,EACA,0BAA0B,SAAS;AACjC,SAAK,uBAAuB,OAAO,KAAK,OAAO,CAAC;AAAA,EAClD;AAAA,EACA,uBAAuB,MAAM;AAC3B,UAAM,oBAAoB;AAAA;AAAA,MAExB,OAAO,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MACpC,gBAAgB,CAAC,WAAW,MAAM,KAAK,MAAM;AAAA,MAC7C,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,kBAAkB,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MACnD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,WAAW,CAAC,eAAe,MAAM,KAAK,UAAU;AAAA,MAChD,SAAS,CAAC,aAAa,MAAM,KAAK,QAAQ;AAAA,MAC1C,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,iBAAiB,CAAC,qBAAqB,MAAM,KAAK,gBAAgB;AAAA,MAClE,kBAAkB,CAAC,sBAAsB,MAAM,KAAK,iBAAiB;AAAA,MACrE,cAAc,CAAC,kBAAkB,MAAM,KAAK,aAAa;AAAA,MACzD,oBAAoB,CAAC,wBAAwB,MAAM,KAAK,kBAAkB;AAAA,MAC1E,yBAAyB,CAAC,2BAA2B,MAAM,KAAK,uBAAuB;AAAA,OACpF,KAAK,oBAAoB;AAE9B,KAAC,QAAQ,OAAO,KAAK,iBAAiB,EAAE,OAAO,SAAO,CAAC,IAAI,WAAW,WAAW,CAAC,GAAG,QAAQ,cAAY;AACvG,UAAI,kBAAkB,QAAQ,GAAG;AAC/B,cAAM,CAAC,MAAM,OAAO,IAAI,kBAAkB,QAAQ;AAClD,aAAK,qBAAqB,MAAM,QAAQ,CAAC;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,SAAK,WAAW,kBAAkB;AAAA,EACpC;AAAA,EACA,iBAAiB;AACf,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EACA,qBAAqB,KAAK,OAAO;AAC/B,QAAI,OAAO,UAAU,aAAa;AAEhC,WAAK,UAAU,GAAG,IAAI;AAAA,IACxB;AAAA,EACF;AAAA,EACA,gBAAgB,UAAU,SAASA,SAAQ,IAAI;AAC7C,QAAI,KAAK,YAAY;AACnB,WAAK,mBAAmB;AAAA,IAC1B,WAAWA,SAAQ,GAAG;AACpB,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,aAAa;AAClB,kBAAU,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,MACpC,GAAGA,SAAQ,GAAI;AAAA,IACjB,OAAO;AAGL,iBAAW,WAAW,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,IAChD;AAAA,EACF;AAAA,EACA,yBAAyB;AACvB,SAAK,mBAAmB,QAAQ,aAAW,QAAQ,CAAC;AACpD,SAAK,mBAAmB,SAAS;AAAA,EACnC;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,YAAY;AACnB,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAqB,IAAI,CAAC;AAAA,IACxE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAEH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,IAAI,UAAU,OAAO;AACnB,UAAM,UAAU,UAAU,KAAK;AAC/B,QAAI,KAAK,aAAa,SAAS;AAC7B,WAAK,WAAW;AAChB,WAAK,gBAAgB,KAAK,OAAO;AAAA,IACnC;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,UAAU,OAAO;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,UAAM,oBAAoB,MAAM,IAAI,eAAa,aAAa,SAAS,CAAC;AACxE,SAAK,aAAa,CAAC,GAAG,mBAAmB,GAAG,yBAAyB;AAAA,EACvE;AAAA,EACA,YAAY,KAAK,gBAAgB,aAAa;AAC5C,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,uBAAuB;AAC5B,SAAK,iBAAiB,CAAC;AACvB,SAAK,aAAa;AAClB,SAAK,0BAA0B;AAC/B,SAAK,kBAAkB,IAAI,QAAQ;AACnC,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,qBAAqB;AAC1B,SAAK,MAAM;AACX,SAAK,YAAY,CAAC;AAClB,SAAK,UAAU;AACf,SAAK,aAAa,CAAC,GAAG,yBAAyB;AAC/C,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,gBAAgB,SAAS;AAC9B,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,QAAI,KAAK,WAAW;AAClB;AAAA,IACF;AACA,QAAI,CAAC,KAAK,QAAQ,GAAG;AACnB,WAAK,YAAY;AACjB,WAAK,gBAAgB,KAAK,IAAI;AAC9B,WAAK,IAAI,cAAc;AAAA,IACzB;AAEA,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,cAAc,KAAK,QAAQ,WAAW,aAAa,MAAM,OAAO;AAC9G,WAAK,QAAQ,WAAW,aAAa,KAAK;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,QAAI,CAAC,KAAK,WAAW;AACnB;AAAA,IACF;AACA,SAAK,YAAY;AACjB,SAAK,gBAAgB,KAAK,KAAK;AAC/B,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,oBAAoB;AAClB,SAAK,aAAa;AAClB,SAAK,IAAI,cAAc;AACvB,YAAQ,QAAQ,EAAE,KAAK,MAAM;AAC3B,WAAK,eAAe;AACpB,WAAK,wBAAwB;AAAA,IAC/B,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB;AACf,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,YAAY;AAC1D,WAAK,QAAQ,WAAW,eAAe;AAAA,IACzC;AAAA,EACF;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,qBAAqB,iBAAiB,QAAQ;AACnD,SAAK,aAAa;AAElB,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,iBAAiB,QAAQ;AACvB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,OAAO;AACpB,QAAI,CAAC,KAAK,OAAO,cAAc,SAAS,MAAM,MAAM,KAAK,KAAK,cAAc,MAAM;AAChF,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,0BAA0B;AACxB,QAAI,KAAK,QAAQ,GAAG;AAClB,WAAK,KAAK;AAAA,IACZ;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,YAAY;AAAA,MACf,CAAC,KAAK,kBAAkB,GAAG;AAAA,MAC3B,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,IAC5D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,sBAAsB,CAAC;AAAA,IAClL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,SAAS,6BAA6B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,UAAU,GAAG;AAAA,QAChE;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,eAAe,OAAO;AAC7B,SAAO,iBAAiB,cAAc,QAAQ,UAAU,MAAM,CAAC,SAAS,KAAK;AAC/E;AACA,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,cAAc;AACZ,UAAM,kBAAkB;AACxB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,0BAA0B;AAE/B,SAAK,gBAAgB,IAAI,aAAa;AAAA,EACxC;AAAA,EACA,sBAAsB;AACpB,WAAO,iCACF,MAAM,oBAAoB,IADxB;AAAA,MAEL,gBAAgB,CAAC,WAAW,MAAM,KAAK,cAAc;AAAA,MACrD,cAAc,CAAC,kBAAkB,MAAM,KAAK,YAAY;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,cAAc,EAAE,CAAC;AAAA,MAClC,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,oBAAoB,IAAI,OAAO;AAAA,QAChD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO,CAAI,WAAa,MAAM,kBAAkB,OAAO;AAAA,QACvD,cAAc,CAAI,WAAa,MAAM,yBAAyB,cAAc;AAAA,QAC5E,gBAAgB,CAAI,WAAa,MAAM,cAAc,gBAAgB;AAAA,QACrE,SAAS,CAAI,WAAa,MAAM,oBAAoB,SAAS;AAAA,QAC7D,WAAW,CAAI,WAAa,MAAM,sBAAsB,WAAW;AAAA,QACnE,QAAQ,CAAI,WAAa,MAAM,mBAAmB,QAAQ;AAAA,QAC1D,SAAS,CAAI,WAAa,MAAM,oBAAoB,SAAS;AAAA,QAC7D,iBAAiB,CAAI,WAAa,MAAM,4BAA4B,iBAAiB;AAAA,QACrF,iBAAiB,CAAI,WAAa,MAAM,4BAA4B,iBAAiB;AAAA,QACrF,kBAAkB,CAAI,WAAa,MAAM,6BAA6B,kBAAkB;AAAA,QACxF,cAAc,CAAI,WAAa,MAAM,yBAAyB,cAAc;AAAA,QAC5E,oBAAoB,CAAI,WAAa,MAAM,+BAA+B,oBAAoB;AAAA,QAC9F,yBAAyB;AAAA,QACzB,gBAAgB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,QACP,eAAe;AAAA,MACjB;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,0BAA0B;AAAA,IAC1C,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,sBAAsB,MAAM;AACvF,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,2BAA2B,MAAM;AAAA,CAC3F,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,4BAA4B;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,2BAA2B;AAAA,IACpC,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,uBAAuB;AAAA,IAChC,CAAC;AAAA,IACD,oBAAoB,CAAC;AAAA,MACnB,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC,wBAAwB;AAAA,IACjC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,qBAAN,MAAM,4BAA2B,uBAAuB;AAAA,EACtD,YAAY,KAAK,gBAAgB,aAAa;AAC5C,UAAM,KAAK,gBAAgB,WAAW;AACtC,SAAK,UAAU;AACf,SAAK,iBAAiB;AACtB,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,UAAU;AACR,WAAO,eAAe,KAAK,OAAO;AAAA,EACpC;AAAA,EACA,eAAe;AACb,UAAM,gBAAgB,KAAK,WAAW,cAAc,KAAK,OAAO;AAChE,SAAK,YAAY;AAAA,MACf,CAAC,KAAK,kBAAkB,GAAG;AAAA,MAC3B,CAAC,GAAG,KAAK,OAAO,cAAc,KAAK,kBAAkB,EAAE,GAAG;AAAA,MAC1D,CAAC,GAAG,KAAK,OAAO,IAAI,KAAK,OAAO,EAAE,GAAG;AAAA,IACvC;AACA,SAAK,mBAAmB;AAAA,MACtB,iBAAiB,CAAC,CAAC,KAAK,WAAW,CAAC,gBAAgB,KAAK,UAAU;AAAA,MACnE,WAAW,KAAK;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAqB,wBAAwB,CAAC,CAAC;AAAA,IACjL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,UAAU,CAAC,oBAAoB;AAAA,MAC/B,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,WAAW,qBAAqB,GAAG,CAAC,uBAAuB,IAAI,sBAAsB,IAAI,GAAG,uBAAuB,UAAU,kBAAkB,6BAA6B,2BAA2B,gCAAgC,2BAA2B,sBAAsB,GAAG,CAAC,GAAG,eAAe,GAAG,WAAW,WAAW,eAAe,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,6BAA6B,GAAG,SAAS,GAAG,CAAC,GAAG,qBAAqB,GAAG,SAAS,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,CAAC;AAAA,MAC/iB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,WAAW,GAAG,2CAA2C,GAAG,IAAI,eAAe,GAAG,GAAM,sBAAsB;AACjH,UAAG,WAAW,uBAAuB,SAAS,uEAAuE,QAAQ;AAC3H,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,eAAe,MAAM,CAAC;AAAA,UAClD,CAAC,EAAE,UAAU,SAAS,4DAA4D;AAChF,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,KAAK,CAAC;AAAA,UAClC,CAAC,EAAE,kBAAkB,SAAS,kEAAkE,QAAQ;AACtG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,iBAAiB,MAAM,CAAC;AAAA,UACpD,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,6BAA6B,IAAI,MAAM,EAAE,2BAA2B,IAAI,QAAQ,EAAE,gCAAgC,IAAI,UAAU,EAAE,2BAA2B,IAAI,uBAAuB,EAAE,wBAAwB,IAAI,oBAAoB;AAAA,QAC1P;AAAA,MACF;AAAA,MACA,cAAc,CAAC,eAAkB,qBAAqB,SAAS,SAAS,wBAAwB,gBAAmB,iCAAiC,iBAAoB,2BAA2B;AAAA,MACnM,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,aAAa;AAAA,MAC3B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,YAAY,CAAC,aAAa;AAAA,MAC1B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkCV,qBAAqB;AAAA,MACrB,SAAS,CAAC,eAAe,SAAS,SAAS,wBAAwB,gBAAgB,eAAe;AAAA,MAClG,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,MAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,MAChD,SAAS,CAAC,oBAAoB,kBAAkB;AAAA,IAClD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": ["delay"]}