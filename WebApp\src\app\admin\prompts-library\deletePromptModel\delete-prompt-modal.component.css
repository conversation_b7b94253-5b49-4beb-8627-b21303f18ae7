.custom-delete-modal .ant-modal-content {
  background-color: var(--background-white) !important;
  border-radius: var(--border-radius-large) !important;
  box-shadow: var(--box-shadow) !important;
  padding: var(--padding-medium) !important;
}

.custom-delete-modal .ant-modal-header {
  background-color: var(--background-white) !important;
  border-bottom: none !important;
  padding: 0 !important;
  margin-bottom: var(--margin-small) !important;
}

.custom-delete-modal .ant-modal-title {
  color: var(--text-dark) !important;
  font-family: var(--font-family) !important;
  font-size: var(--font-size-header) !important;
  font-weight: var(--font-weight-bold) !important;
  display: flex !important;
  align-items: center !important;
}

.custom-delete-modal .ant-modal-body {
  color: var(--text-medium-gray) !important;
  font-family: var(--font-family) !important;
  font-size: var(--font-size-body) !important;
  line-height: var(--line-height) !important;
  padding: 0 !important;
}

.custom-delete-modal .ant-modal-confirm-btns {
  margin-top: var(--margin-small) !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: var(--padding-small) !important;
}

.custom-delete-modal .ant-btn {
  border-radius: var(--border-radius-small) !important;
  font-family: var(--font-family) !important;
  font-size: var(--font-size-body) !important;
  transition: var(--transition-default) !important;
  padding: var(--padding-small) var(--padding-medium) !important;
  height: auto !important;
}

.custom-delete-modal .ant-btn-default {
  background-color: var(--background-light-gray) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

.custom-delete-modal .ant-btn-default:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

.custom-delete-modal .ant-btn-primary {
  background-color: var(--primary-purple) !important;
  border-color: var(--primary-purple) !important;
  color: var(--background-white) !important;
}

.custom-delete-modal .ant-btn-primary:hover {
  background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple)) !important;
  border-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple)) !important;
}

.custom-delete-modal .ant-modal-confirm-btns .ant-btn span {
  background-color: transparent !important;
}

:host-context(.dark) .custom-delete-modal .ant-modal-content {
  background-color: var(--background-white) !important;
}

:host-context(.dark) .custom-delete-modal .ant-modal-title {
  color: var(--text-dark) !important;
}

:host-context(.dark) .custom-delete-modal .ant-modal-body {
  color: var(--text-medium-gray) !important;
}

:host-context(.dark) .custom-delete-modal .ant-btn-default {
  background-color: var(--background-light-gray) !important;
  border-color: var(--hover-blue-gray) !important;
  color: var(--text-dark) !important;
}

:host-context(.dark) .custom-delete-modal .ant-btn-default:hover {
  background-color: var(--hover-blue-gray) !important;
  color: var(--primary-purple) !important;
}

:host-context(.dark) .custom-delete-modal .ant-btn-primary {
  background-color: var(--primary-purple) !important;
  border-color: var(--primary-purple) !important;
  color: var(--background-white) !important;
}

:host-context(.dark) .custom-delete-modal .ant-btn-primary:hover {
  background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple)) !important;
  border-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple)) !important;
}
