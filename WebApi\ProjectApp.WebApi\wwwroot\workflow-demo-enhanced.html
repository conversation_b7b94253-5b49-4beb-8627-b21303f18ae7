<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Semantic Kernel Process Framework - Enhanced Demo</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .section {
            margin-bottom: 30px;
            padding: 25px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #34495e;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        input,
        textarea,
        select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        textarea {
            height: 120px;
            resize: vertical;
        }

        button {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin-right: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }

        button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }

        .test-button {
            background: linear-gradient(145deg, #27ae60, #229954);
        }

        .info-button {
            background: linear-gradient(145deg, #f39c12, #e67e22);
        }

        .danger-button {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
        }

        /* Progress Tracking Styles */
        .progress-container {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            font-weight: 600;
            color: #495057;
            margin-bottom: 10px;
        }

        /* Step Visualization */
        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            min-width: 150px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .step.pending {
            border-color: #6c757d;
            color: #6c757d;
        }

        .step.running {
            border-color: #007bff;
            background: #e3f2fd;
            color: #007bff;
            animation: pulse 2s infinite;
        }

        .step.completed {
            border-color: #28a745;
            background: #d4edda;
            color: #28a745;
        }

        .step.failed {
            border-color: #dc3545;
            background: #f8d7da;
            color: #dc3545;
        }

        .step-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .step-name {
            font-weight: 600;
            text-align: center;
            font-size: 12px;
        }

        .step-duration {
            font-size: 10px;
            opacity: 0.7;
            margin-top: 4px;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        /* Real-time Updates */
        .updates-container {
            max-height: 300px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .update-item {
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            background: white;
            font-size: 14px;
            animation: slideInRight 0.3s ease;
        }

        .update-item.success {
            border-left-color: #28a745;
        }

        .update-item.error {
            border-left-color: #dc3545;
        }

        .update-item.warning {
            border-left-color: #ffc107;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .loading {
            text-align: center;
            color: #666;
        }

        .feature-list {
            columns: 2;
            column-gap: 30px;
        }

        .feature-list li {
            margin-bottom: 8px;
            break-inside: avoid;
        }

        /* Connection Status */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            font-size: 12px;
            z-index: 1000;
        }

        .connection-status.connected {
            background: #28a745;
        }

        .connection-status.disconnected {
            background: #dc3545;
        }

        .connection-status.connecting {
            background: #ffc107;
            color: #212529;
        }
    </style>
</head>

<body>
    <div class="connection-status" id="connectionStatus">Connecting...</div>

    <div class="container">
        <h1>🚀 Semantic Kernel Process Framework</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Step-wise Work Completion with AI Agents - Real-time Demo
        </p>

        <div class="section">
            <h2>📋 Project Registration Workflow</h2>
            <p>Experience the complete Microsoft Semantic Kernel Process Framework with real-time step tracking:</p>
            <ul class="feature-list">
                <li>✅ Input validation and sanitization</li>
                <li>🤖 AI-powered project analysis</li>
                <li>🎯 Intelligent workspace assignment</li>
                <li>💾 Automated project creation</li>
                <li>📧 Multi-party notifications</li>
                <li>🔄 Event-driven step execution</li>
                <li>⚡ Real-time progress tracking</li>
                <li>🛡️ Built-in error handling</li>
            </ul>
        </div>

        <div class="section">
            <h2>🧪 Quick Actions</h2>
            <button class="test-button" onclick="runTestWorkflow()">
                <i class="fas fa-play"></i> Run Test Workflow
            </button>
            <button class="info-button" onclick="getProcessInfo()">
                <i class="fas fa-info-circle"></i> Get Process Info
            </button>
            <button class="danger-button" onclick="clearUpdates()">
                <i class="fas fa-trash"></i> Clear Updates
            </button>
        </div>

        <!-- Progress Tracking Section -->
        <div class="section" id="progressSection" style="display: none;">
            <h2>📊 Workflow Progress</h2>
            <div class="progress-text" id="progressText">Initializing workflow...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>

            <!-- Step Visualization -->
            <div class="steps-container" id="stepsContainer">
                <!-- Steps will be dynamically added here -->
            </div>

            <!-- Real-time Updates -->
            <div class="updates-container" id="updatesContainer">
                <div style="text-align: center; color: #666; font-style: italic;">
                    Real-time updates will appear here...
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📝 Custom Project Registration</h2>
            <form id="projectForm">
                <div class="form-group">
                    <label for="subject">Project Subject *</label>
                    <input type="text" id="subject" name="subject" required
                        placeholder="Enter project title (e.g., 'E-commerce Website Development')">
                </div>

                <div class="form-group">
                    <label for="message">Project Description *</label>
                    <textarea id="message" name="message" required
                        placeholder="Describe your project requirements, goals, and any specific details..."></textarea>
                </div>

                <div class="form-group">
                    <label for="userEmail">Your Email *</label>
                    <input type="email" id="userEmail" name="userEmail" required placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="assignedEmail">Preferred Developer (Optional)</label>
                    <input type="email" id="assignedEmail" name="assignedEmail" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="priority">Priority</label>
                    <select id="priority" name="priority">
                        <option value="Low">Low</option>
                        <option value="Medium" selected>Medium</option>
                        <option value="High">High</option>
                        <option value="Critical">Critical</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fileNames">File Names (Optional)</label>
                    <input type="text" id="fileNames" name="fileNames"
                        placeholder="requirements.pdf, mockups.png, specs.docx (comma-separated)">
                </div>

                <button type="submit">
                    <i class="fas fa-rocket"></i> Execute Workflow
                </button>
            </form>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const baseUrl = window.location.origin.replace(/:\d+/, ':7257');
        let connection = null;
        let currentProcessId = null;
        let processSteps = [];

        // Initialize SignalR connection
        async function initializeSignalR() {
            try {
                connection = new signalR.HubConnectionBuilder()
                    .withUrl(`${baseUrl}/chathub`)
                    .build();

                connection.on("ProcessUpdate", function (update) {
                    handleProcessUpdate(update);
                });

                connection.on("ReceiveMessage", function (message) {
                    handleRealtimeMessage(message);
                });

                await connection.start();
                updateConnectionStatus('connected');
                console.log("SignalR Connected");
            } catch (err) {
                console.error("SignalR Connection Error: ", err);
                updateConnectionStatus('disconnected');
                // Retry connection after 5 seconds
                setTimeout(initializeSignalR, 5000);
            }
        }

        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `connection-status ${status}`;
            statusElement.textContent = status === 'connected' ? 'Connected' :
                status === 'connecting' ? 'Connecting...' : 'Disconnected';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            updateConnectionStatus('connecting');
            initializeSignalR();
        });

        // Default workflow steps
        const defaultSteps = [
            { name: 'Input Validation', icon: 'fas fa-check-circle', order: 1 },
            { name: 'AI Analysis', icon: 'fas fa-brain', order: 2 },
            { name: 'Workspace Assignment', icon: 'fas fa-sitemap', order: 3 },
            { name: 'Project Creation', icon: 'fas fa-plus-circle', order: 4 },
            { name: 'Send Notifications', icon: 'fas fa-envelope', order: 5 }
        ];

        function initializeSteps(steps = defaultSteps) {
            processSteps = steps;
            const container = document.getElementById('stepsContainer');
            container.innerHTML = '';

            steps.forEach(step => {
                const stepElement = document.createElement('div');
                stepElement.className = 'step pending';
                stepElement.id = `step-${step.order}`;
                stepElement.innerHTML = `
                    <div class="step-icon"><i class="${step.icon}"></i></div>
                    <div class="step-name">${step.name}</div>
                    <div class="step-duration" id="duration-${step.order}"></div>
                `;
                container.appendChild(stepElement);
            });
        }

        function updateStepStatus(stepOrder, status, duration = null) {
            const stepElement = document.getElementById(`step-${stepOrder}`);
            if (stepElement) {
                stepElement.className = `step ${status}`;
                if (duration) {
                    document.getElementById(`duration-${stepOrder}`).textContent = duration;
                }
            }
        }

        function updateProgress(percentage, text) {
            document.getElementById('progressFill').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent = text;
        }

        function addRealtimeUpdate(message, type = 'info') {
            const container = document.getElementById('updatesContainer');
            const updateElement = document.createElement('div');
            updateElement.className = `update-item ${type}`;
            updateElement.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}</strong> - ${message}
            `;

            // Remove placeholder text if it exists
            if (container.children.length === 1 && container.children[0].style.textAlign === 'center') {
                container.innerHTML = '';
            }

            container.appendChild(updateElement);
            container.scrollTop = container.scrollHeight;
        }

        function handleProcessUpdate(update) {
            if (update.ProcessId) {
                currentProcessId = update.ProcessId;
            }

            if (update.Progress !== undefined) {
                updateProgress(update.Progress, update.Message || 'Processing...');
            }

            if (update.StepId && update.Message) {
                addRealtimeUpdate(update.Message, 'info');
            }

            addRealtimeUpdate(update.Message || 'Process update received', 'info');
        }

        function handleRealtimeMessage(message) {
            if (message && message.Message) {
                addRealtimeUpdate(message.Message, message.IsError ? 'error' : 'success');
            }
        }

        function showProgressSection() {
            document.getElementById('progressSection').style.display = 'block';
            initializeSteps();
            updateProgress(0, 'Initializing workflow...');
        }

        function clearUpdates() {
            const container = document.getElementById('updatesContainer');
            container.innerHTML = `
                <div style="text-align: center; color: #666; font-style: italic;">
                    Real-time updates will appear here...
                </div>
            `;
            updateProgress(0, 'Ready to start workflow...');
            processSteps.forEach((step, index) => {
                updateStepStatus(index + 1, 'pending');
            });
        }

        async function runTestWorkflow() {
            showProgressSection();
            addRealtimeUpdate('Starting test workflow...', 'info');
            updateProgress(5, 'Initializing test workflow...');

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/test-workflow`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    currentProcessId = result.processId;
                    addRealtimeUpdate(`Test workflow started successfully! Process ID: ${result.processId}`, 'success');

                    // Simulate step progression for demo
                    simulateStepProgression(result);

                    displayResult(JSON.stringify(result, null, 2), 'success');
                } else {
                    const error = await response.text();
                    addRealtimeUpdate(`Test workflow failed: ${error}`, 'error');
                    displayResult(`Error: ${error}`, 'error');
                }
            } catch (error) {
                addRealtimeUpdate(`Network error: ${error.message}`, 'error');
                displayResult(`Network Error: ${error.message}`, 'error');
            }
        }

        async function getProcessInfo() {
            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/process-info?take=10&includeSteps=true`);

                if (response.ok) {
                    const result = await response.json();
                    addRealtimeUpdate(`Retrieved ${result.processes.length} processes`, 'success');
                    displayResult(JSON.stringify(result, null, 2), 'info');
                } else {
                    const error = await response.text();
                    addRealtimeUpdate(`Failed to get process info: ${error}`, 'error');
                    displayResult(`Error: ${error}`, 'error');
                }
            } catch (error) {
                addRealtimeUpdate(`Network error: ${error.message}`, 'error');
                displayResult(`Network Error: ${error.message}`, 'error');
            }
        }

        function simulateStepProgression(processResult) {
            let currentStep = 1;
            const totalSteps = 5;

            const progressInterval = setInterval(() => {
                if (currentStep <= totalSteps) {
                    updateStepStatus(currentStep, 'running');
                    updateProgress((currentStep / totalSteps) * 100, `Executing step ${currentStep} of ${totalSteps}...`);
                    addRealtimeUpdate(`Step ${currentStep}: ${defaultSteps[currentStep - 1].name} is running...`, 'info');

                    setTimeout(() => {
                        updateStepStatus(currentStep, 'completed', `${Math.floor(Math.random() * 3000 + 500)}ms`);
                        addRealtimeUpdate(`Step ${currentStep}: ${defaultSteps[currentStep - 1].name} completed successfully`, 'success');
                        currentStep++;

                        if (currentStep > totalSteps) {
                            clearInterval(progressInterval);
                            updateProgress(100, 'Workflow completed successfully!');
                            addRealtimeUpdate('🎉 All steps completed! Workflow finished successfully.', 'success');
                        }
                    }, Math.random() * 2000 + 1000);
                }
            }, 3000);
        }

        // Form submission handler
        document.getElementById('projectForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            showProgressSection();
            addRealtimeUpdate('Starting custom project registration workflow...', 'info');

            const formData = new FormData(e.target);
            const projectData = {
                subject: formData.get('subject'),
                message: formData.get('message'),
                userEmail: formData.get('userEmail'),
                assignedEmail: formData.get('assignedEmail') || null,
                priority: formData.get('priority'),
                fileNames: formData.get('fileNames') ?
                    formData.get('fileNames').split(',').map(f => f.trim()).filter(f => f) : [],
                additionalRequirements: null
            };

            try {
                updateProgress(10, 'Submitting project registration...');

                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/execute-project-registration`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(projectData)
                });

                if (response.ok) {
                    const result = await response.json();
                    currentProcessId = result.processId;
                    addRealtimeUpdate(`Project registration started! Process ID: ${result.processId}`, 'success');

                    // Start monitoring the process
                    monitorProcess(result.processId);

                    displayResult(JSON.stringify(result, null, 2), 'success');
                } else {
                    const error = await response.text();
                    addRealtimeUpdate(`Project registration failed: ${error}`, 'error');
                    displayResult(`Error: ${error}`, 'error');
                }
            } catch (error) {
                addRealtimeUpdate(`Network error: ${error.message}`, 'error');
                displayResult(`Network Error: ${error.message}`, 'error');
            }
        });

        async function monitorProcess(processId) {
            const monitorInterval = setInterval(async () => {
                try {
                    const response = await fetch(`${baseUrl}/api/ProcessWorkflow/process/${processId}?includeSteps=true`);

                    if (response.ok) {
                        const processInfo = await response.json();

                        // Update progress
                        updateProgress(processInfo.progressPercentage,
                            `${processInfo.currentStepName || 'Processing'} (${Math.round(processInfo.progressPercentage)}%)`);

                        // Update step statuses
                        if (processInfo.steps) {
                            processInfo.steps.forEach(step => {
                                const stepStatus = step.state.toLowerCase();
                                updateStepStatus(step.stepOrder, stepStatus,
                                    step.executionDurationMs ? `${step.executionDurationMs}ms` : null);
                            });
                        }

                        // Check if process is complete
                        if (processInfo.isCompleted) {
                            clearInterval(monitorInterval);
                            if (processInfo.state === 'Completed') {
                                addRealtimeUpdate('🎉 Project registration completed successfully!', 'success');
                            } else {
                                addRealtimeUpdate(`Process ended with status: ${processInfo.state}`, 'warning');
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error monitoring process:', error);
                }
            }, 2000);

            // Stop monitoring after 5 minutes
            setTimeout(() => clearInterval(monitorInterval), 300000);
        }

        function displayResult(content, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = content;
            resultDiv.className = `result ${type}`;
            resultDiv.style.display = 'block';
        }
    </script>
</body>

</html>