/* Agent Details Component Styles */

:host {
  display: block;
  height: 100%;
  width: 100%;
}

/* Card styling for consistent theme support */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

:host-context(.dark) .ant-card {
  background-color: var(--background-white);
  border-color: var(--hover-blue-gray);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Table styling */
.ant-table {
  background: transparent;
}

:host-context(.dark) .ant-table {
  background-color: var(--background-white);
  color: var(--text-dark);
}

:host-context(.dark) .ant-table-thead > tr > th {
  background-color: var(--hover-blue-gray);
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

:host-context(.dark) .ant-table-tbody > tr > td {
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

/* Progress bar styling */
.ant-progress-line {
  margin-bottom: 0;
}

.ant-progress-bg {
  border-radius: 4px;
}

/* Button styling */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  background-color: var(--primary-purple);
  border-color: var(--primary-purple);
}

.ant-btn-primary:hover {
  background-color: var(--secondary-purple);
  border-color: var(--secondary-purple);
}

:host-context(.dark) .ant-btn-default {
  background-color: var(--hover-blue-gray);
  border-color: var(--hover-blue-gray);
  color: var(--text-dark);
}

:host-context(.dark) .ant-btn-default:hover {
  background-color: var(--background-light-gray);
  border-color: var(--background-light-gray);
}

/* Tag styling */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
  border: none;
}

/* Alert styling */
.ant-alert {
  border-radius: 6px;
  border: none;
}

:host-context(.dark) .ant-alert-info {
  background-color: rgba(16, 163, 127, 0.1);
  border: 1px solid rgba(16, 163, 127, 0.3);
}

:host-context(.dark) .ant-alert-info .ant-alert-message,
:host-context(.dark) .ant-alert-info .ant-alert-description {
  color: var(--text-dark);
}

:host-context(.dark) .ant-alert-success {
  background-color: rgba(82, 196, 26, 0.1);
  border: 1px solid rgba(82, 196, 26, 0.3);
}

:host-context(.dark) .ant-alert-warning {
  background-color: rgba(250, 173, 20, 0.1);
  border: 1px solid rgba(250, 173, 20, 0.3);
}

/* Statistic styling */
:host-context(.dark) .ant-statistic-title {
  color: var(--text-medium-gray);
}

:host-context(.dark) .ant-statistic-content {
  color: var(--text-dark);
}

/* Breadcrumb styling */
:host-context(.dark) .ant-breadcrumb {
  color: var(--text-medium-gray);
}

:host-context(.dark) .ant-breadcrumb a {
  color: var(--primary-purple);
}

:host-context(.dark) .ant-breadcrumb a:hover {
  color: var(--secondary-purple);
}

/* Agent avatar styling */
.agent-avatar {
  position: relative;
  overflow: hidden;
}

.agent-avatar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  border-radius: inherit;
}

/* Quality metrics styling */
.quality-metric {
  padding: 0.75rem;
  border-radius: 6px;
  background: rgba(16, 163, 127, 0.05);
  border: 1px solid rgba(16, 163, 127, 0.2);
  transition: all 0.3s ease;
}

.quality-metric:hover {
  background: rgba(16, 163, 127, 0.1);
  transform: translateY(-1px);
}

:host-context(.dark) .quality-metric {
  background: rgba(16, 163, 127, 0.1);
  border-color: rgba(16, 163, 127, 0.3);
}

/* Activity period cards */
.activity-period {
  position: relative;
  overflow: hidden;
}

.activity-period::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, var(--primary-purple), var(--secondary-purple));
}

/* Failed task styling */
.failed-task-id {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  background-color: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

:host-context(.dark) .failed-task-id {
  background-color: rgba(239, 68, 68, 0.2);
  color: #fca5a5;
  border-color: rgba(239, 68, 68, 0.4);
}

/* Performance indicators */
.performance-excellent {
  color: #52c41a !important;
}

.performance-good {
  color: #faad14 !important;
}

.performance-poor {
  color: #ff4d4f !important;
}

/* Custom scrollbar */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: var(--secondary-purple);
}

/* Loading state */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

:host-context(.dark) .loading-overlay {
  background: rgba(52, 53, 65, 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .grid-cols-4,
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .flex-shrink-0 {
    padding: 1rem;
  }
}
