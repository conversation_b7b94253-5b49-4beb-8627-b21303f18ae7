using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IDatabaseConnectionRepository
    {
        Task<List<DatabaseConnectionDto>> GetAllAsync();
        
        Task<DatabaseConnectionDto?> GetByIdAsync(Guid id);
        
        Task<DatabaseConnectionDto?> GetByNameAsync(string name);
        
        Task<DatabaseConnectionDto> CreateAsync(CreateDatabaseConnectionDto dto);
        
        Task<DatabaseConnectionDto> UpdateAsync(UpdateDatabaseConnectionDto dto);
        
        Task<bool> DeleteAsync(Guid id);
        
        Task<ResponseMessage> TestConnectionAsync(TestConnectionDto dto);
        
        Task<bool> UpdateSchemaExtractionStatusAsync(Guid id, string status, string? error = null);
        
        Task<List<DatabaseConnectionDto>> GetActiveConnectionsAsync();
        
        Task<bool> ExistsAsync(string name);
    }
}
