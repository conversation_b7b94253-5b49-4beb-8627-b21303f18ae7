{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-form.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Directive, Optional, Input, Host, ContentChild, SkipSelf, NgModule } from '@angular/core';\nimport { NzGridModule } from 'ng-zorro-antd/grid';\nimport { NgClass } from '@angular/common';\nimport { AbstractControl, NgModel, FormControlName, FormControlDirective, NgControl } from '@angular/forms';\nimport { Subject, Subscription } from 'rxjs';\nimport { filter, map, takeUntil, startWith, tap } from 'rxjs/operators';\nimport { helpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i4 from 'ng-zorro-antd/core/form';\nimport { NzFormStatusService } from 'ng-zorro-antd/core/form';\nimport * as i5 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean, toBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2$1 from 'ng-zorro-antd/i18n';\nimport { __decorate } from 'tslib';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzTooltipDirective } from 'ng-zorro-antd/tooltip';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/** should add nz-row directive to host, track https://github.com/angular/angular/issues/8785 **/\nconst _c0 = [\"*\"];\nconst _c1 = a0 => [a0];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nfunction NzFormControlComponent_Conditional_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.innerTip);\n  }\n}\nfunction NzFormControlComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 4);\n    i0.ɵɵtemplate(2, NzFormControlComponent_Conditional_3_ng_container_2_Template, 2, 1, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@helpMotion\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c1, \"ant-form-item-explain-\" + ctx_r0.status));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.innerTip)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(6, _c2, ctx_r0.validateControl));\n  }\n}\nfunction NzFormControlComponent_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzExtra);\n  }\n}\nfunction NzFormControlComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, NzFormControlComponent_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzExtra);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tooltipIconType_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", tooltipIconType_r1)(\"nzTheme\", ctx_r1.tooltipIcon.theme);\n  }\n}\nfunction NzFormLabelComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 0);\n    i0.ɵɵtemplate(1, NzFormLabelComponent_Conditional_2_ng_container_1_Template, 2, 2, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzTooltipTitle\", ctx_r1.nzTooltipTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.tooltipIcon.type);\n  }\n}\nclass NzFormItemComponent {\n  setWithHelpViaTips(value) {\n    this.withHelpClass = value;\n    this.cdr.markForCheck();\n  }\n  setStatus(status) {\n    this.status = status;\n    this.cdr.markForCheck();\n  }\n  setHasFeedback(hasFeedback) {\n    this.hasFeedback = hasFeedback;\n    this.cdr.markForCheck();\n  }\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.status = '';\n    this.hasFeedback = false;\n    this.withHelpClass = false;\n    this.destroy$ = new Subject();\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormItemComponent_Factory(t) {\n      return new (t || NzFormItemComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormItemComponent,\n      selectors: [[\"nz-form-item\"]],\n      hostAttrs: [1, \"ant-form-item\"],\n      hostVars: 12,\n      hostBindings: function NzFormItemComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-has-success\", ctx.status === \"success\")(\"ant-form-item-has-warning\", ctx.status === \"warning\")(\"ant-form-item-has-error\", ctx.status === \"error\")(\"ant-form-item-is-validating\", ctx.status === \"validating\")(\"ant-form-item-has-feedback\", ctx.hasFeedback && ctx.status)(\"ant-form-item-with-help\", ctx.withHelpClass);\n        }\n      },\n      exportAs: [\"nzFormItem\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-item',\n      exportAs: 'nzFormItem',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'ant-form-item',\n        '[class.ant-form-item-has-success]': 'status === \"success\"',\n        '[class.ant-form-item-has-warning]': 'status === \"warning\"',\n        '[class.ant-form-item-has-error]': 'status === \"error\"',\n        '[class.ant-form-item-is-validating]': 'status === \"validating\"',\n        '[class.ant-form-item-has-feedback]': 'hasFeedback && status',\n        '[class.ant-form-item-with-help]': 'withHelpClass'\n      },\n      template: ` <ng-content></ng-content> `,\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nconst NZ_CONFIG_MODULE_NAME = 'form';\nconst DefaultTooltipIcon = {\n  type: 'question-circle',\n  theme: 'outline'\n};\nclass NzFormDirective {\n  getInputObservable(changeType) {\n    return this.inputChanges$.pipe(filter(changes => changeType in changes), map(value => value[changeType]));\n  }\n  constructor(nzConfigService, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzLayout = 'horizontal';\n    this.nzNoColon = false;\n    this.nzAutoTips = {};\n    this.nzDisableAutoTips = false;\n    this.nzTooltipIcon = DefaultTooltipIcon;\n    this.nzLabelAlign = 'right';\n    this.nzLabelWrap = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.inputChanges$ = new Subject();\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n    });\n  }\n  ngOnChanges(changes) {\n    this.inputChanges$.next(changes);\n  }\n  ngOnDestroy() {\n    this.inputChanges$.complete();\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormDirective_Factory(t) {\n      return new (t || NzFormDirective)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzFormDirective,\n      selectors: [[\"\", \"nz-form\", \"\"]],\n      hostAttrs: [1, \"ant-form\"],\n      hostVars: 8,\n      hostBindings: function NzFormDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-horizontal\", ctx.nzLayout === \"horizontal\")(\"ant-form-vertical\", ctx.nzLayout === \"vertical\")(\"ant-form-inline\", ctx.nzLayout === \"inline\")(\"ant-form-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzLayout: \"nzLayout\",\n        nzNoColon: \"nzNoColon\",\n        nzAutoTips: \"nzAutoTips\",\n        nzDisableAutoTips: \"nzDisableAutoTips\",\n        nzTooltipIcon: \"nzTooltipIcon\",\n        nzLabelAlign: \"nzLabelAlign\",\n        nzLabelWrap: \"nzLabelWrap\"\n      },\n      exportAs: [\"nzForm\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzFormDirective.prototype, \"nzNoColon\", void 0);\n__decorate([WithConfig()], NzFormDirective.prototype, \"nzAutoTips\", void 0);\n__decorate([InputBoolean()], NzFormDirective.prototype, \"nzDisableAutoTips\", void 0);\n__decorate([WithConfig()], NzFormDirective.prototype, \"nzTooltipIcon\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzFormDirective.prototype, \"nzLabelWrap\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-form]',\n      exportAs: 'nzForm',\n      host: {\n        class: 'ant-form',\n        '[class.ant-form-horizontal]': `nzLayout === 'horizontal'`,\n        '[class.ant-form-vertical]': `nzLayout === 'vertical'`,\n        '[class.ant-form-inline]': `nzLayout === 'inline'`,\n        '[class.ant-form-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzLayout: [{\n      type: Input\n    }],\n    nzNoColon: [{\n      type: Input\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormControlComponent {\n  get disableAutoTips() {\n    return this.nzDisableAutoTips !== 'default' ? toBoolean(this.nzDisableAutoTips) : this.nzFormDirective?.nzDisableAutoTips;\n  }\n  set nzHasFeedback(value) {\n    this._hasFeedback = toBoolean(value);\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this._hasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setHasFeedback(this._hasFeedback);\n    }\n  }\n  get nzHasFeedback() {\n    return this._hasFeedback;\n  }\n  set nzValidateStatus(value) {\n    if (value instanceof AbstractControl || value instanceof NgModel) {\n      this.validateControl = value;\n      this.validateString = null;\n      this.watchControl();\n    } else if (value instanceof FormControlName) {\n      this.validateControl = value.control;\n      this.validateString = null;\n      this.watchControl();\n    } else {\n      this.validateString = value;\n      this.validateControl = null;\n      this.setStatus();\n    }\n  }\n  watchControl() {\n    this.validateChanges.unsubscribe();\n    /** miss detect https://github.com/angular/angular/issues/10887 **/\n    if (this.validateControl && this.validateControl.statusChanges) {\n      this.validateChanges = this.validateControl.statusChanges.pipe(startWith(null), takeUntil(this.destroyed$)).subscribe(() => {\n        if (!this.disableAutoTips) {\n          this.updateAutoErrorTip();\n        }\n        this.setStatus();\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  setStatus() {\n    this.status = this.getControlStatus(this.validateString);\n    this.innerTip = this.getInnerTip(this.status);\n    this.nzFormStatusService.formStatusChanges.next({\n      status: this.status,\n      hasFeedback: this.nzHasFeedback\n    });\n    if (this.nzFormItemComponent) {\n      this.nzFormItemComponent.setWithHelpViaTips(!!this.innerTip);\n      this.nzFormItemComponent.setStatus(this.status);\n    }\n  }\n  getControlStatus(validateString) {\n    let status;\n    if (validateString === 'warning' || this.validateControlStatus('INVALID', 'warning')) {\n      status = 'warning';\n    } else if (validateString === 'error' || this.validateControlStatus('INVALID')) {\n      status = 'error';\n    } else if (validateString === 'validating' || validateString === 'pending' || this.validateControlStatus('PENDING')) {\n      status = 'validating';\n    } else if (validateString === 'success' || this.validateControlStatus('VALID')) {\n      status = 'success';\n    } else {\n      status = '';\n    }\n    return status;\n  }\n  validateControlStatus(validStatus, statusType) {\n    if (!this.validateControl) {\n      return false;\n    } else {\n      const {\n        dirty,\n        touched,\n        status\n      } = this.validateControl;\n      return (!!dirty || !!touched) && (statusType ? this.validateControl.hasError(statusType) : status === validStatus);\n    }\n  }\n  getInnerTip(status) {\n    switch (status) {\n      case 'error':\n        return !this.disableAutoTips && this.autoErrorTip || this.nzErrorTip || null;\n      case 'validating':\n        return this.nzValidatingTip || null;\n      case 'success':\n        return this.nzSuccessTip || null;\n      case 'warning':\n        return this.nzWarningTip || null;\n      default:\n        return null;\n    }\n  }\n  updateAutoErrorTip() {\n    if (this.validateControl) {\n      const errors = this.validateControl.errors || {};\n      let autoErrorTip = '';\n      for (const key in errors) {\n        if (errors.hasOwnProperty(key)) {\n          autoErrorTip = errors[key]?.[this.localeId] ?? this.nzAutoTips?.[this.localeId]?.[key] ?? this.nzAutoTips.default?.[key] ?? this.nzFormDirective?.nzAutoTips?.[this.localeId]?.[key] ?? this.nzFormDirective?.nzAutoTips.default?.[key];\n        }\n        if (!!autoErrorTip) {\n          break;\n        }\n      }\n      this.autoErrorTip = autoErrorTip;\n    }\n  }\n  subscribeAutoTips(observable) {\n    observable?.pipe(takeUntil(this.destroyed$)).subscribe(() => {\n      if (!this.disableAutoTips) {\n        this.updateAutoErrorTip();\n        this.setStatus();\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  constructor(nzFormItemComponent, cdr, i18n, nzFormDirective, nzFormStatusService) {\n    this.nzFormItemComponent = nzFormItemComponent;\n    this.cdr = cdr;\n    this.nzFormDirective = nzFormDirective;\n    this.nzFormStatusService = nzFormStatusService;\n    this._hasFeedback = false;\n    this.validateChanges = Subscription.EMPTY;\n    this.validateString = null;\n    this.destroyed$ = new Subject();\n    this.status = '';\n    this.validateControl = null;\n    this.innerTip = null;\n    this.nzAutoTips = {};\n    this.nzDisableAutoTips = 'default';\n    this.subscribeAutoTips(i18n.localeChange.pipe(tap(locale => this.localeId = locale.locale)));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzAutoTips'));\n    this.subscribeAutoTips(this.nzFormDirective?.getInputObservable('nzDisableAutoTips').pipe(filter(() => this.nzDisableAutoTips === 'default')));\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisableAutoTips,\n      nzAutoTips,\n      nzSuccessTip,\n      nzWarningTip,\n      nzErrorTip,\n      nzValidatingTip\n    } = changes;\n    if (nzDisableAutoTips || nzAutoTips) {\n      this.updateAutoErrorTip();\n      this.setStatus();\n    } else if (nzSuccessTip || nzWarningTip || nzErrorTip || nzValidatingTip) {\n      this.setStatus();\n    }\n  }\n  ngOnInit() {\n    this.setStatus();\n  }\n  ngOnDestroy() {\n    this.destroyed$.next();\n    this.destroyed$.complete();\n  }\n  ngAfterContentInit() {\n    if (!this.validateControl && !this.validateString) {\n      if (this.defaultValidateControl instanceof FormControlDirective) {\n        this.nzValidateStatus = this.defaultValidateControl.control;\n      } else {\n        this.nzValidateStatus = this.defaultValidateControl;\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzFormControlComponent_Factory(t) {\n      return new (t || NzFormControlComponent)(i0.ɵɵdirectiveInject(NzFormItemComponent, 9), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.NzI18nService), i0.ɵɵdirectiveInject(NzFormDirective, 8), i0.ɵɵdirectiveInject(i4.NzFormStatusService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormControlComponent,\n      selectors: [[\"nz-form-control\"]],\n      contentQueries: function NzFormControlComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NgControl, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultValidateControl = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-form-item-control\"],\n      inputs: {\n        nzSuccessTip: \"nzSuccessTip\",\n        nzWarningTip: \"nzWarningTip\",\n        nzErrorTip: \"nzErrorTip\",\n        nzValidatingTip: \"nzValidatingTip\",\n        nzExtra: \"nzExtra\",\n        nzAutoTips: \"nzAutoTips\",\n        nzDisableAutoTips: \"nzDisableAutoTips\",\n        nzHasFeedback: \"nzHasFeedback\",\n        nzValidateStatus: \"nzValidateStatus\"\n      },\n      exportAs: [\"nzFormControl\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzFormStatusService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 2,\n      consts: [[1, \"ant-form-item-control-input\"], [1, \"ant-form-item-control-input-content\"], [1, \"ant-form-item-explain\", \"ant-form-item-explain-connected\"], [1, \"ant-form-item-extra\"], [\"role\", \"alert\", 3, \"ngClass\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzFormControlComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(3, NzFormControlComponent_Conditional_3_Template, 3, 8, \"div\", 2)(4, NzFormControlComponent_Conditional_4_Template, 2, 1, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵconditional(3, ctx.innerTip ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.nzExtra ? 4 : -1);\n        }\n      },\n      dependencies: [NgClass, NzOutletModule, i5.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [helpMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormControlComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-control',\n      exportAs: 'nzFormControl',\n      preserveWhitespaces: false,\n      animations: [helpMotion],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <div class=\"ant-form-item-control-input\">\n      <div class=\"ant-form-item-control-input-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n    @if (innerTip) {\n      <div @helpMotion class=\"ant-form-item-explain ant-form-item-explain-connected\">\n        <div role=\"alert\" [ngClass]=\"['ant-form-item-explain-' + status]\">\n          <ng-container *nzStringTemplateOutlet=\"innerTip; context: { $implicit: validateControl }\">{{\n            innerTip\n          }}</ng-container>\n        </div>\n      </div>\n    }\n\n    @if (nzExtra) {\n      <div class=\"ant-form-item-extra\">\n        <ng-container *nzStringTemplateOutlet=\"nzExtra\">{{ nzExtra }}</ng-container>\n      </div>\n    }\n  `,\n      providers: [NzFormStatusService],\n      host: {\n        class: 'ant-form-item-control'\n      },\n      imports: [NgClass, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzFormItemComponent,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Host\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.NzI18nService\n  }, {\n    type: NzFormDirective,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NzFormStatusService\n  }], {\n    defaultValidateControl: [{\n      type: ContentChild,\n      args: [NgControl, {\n        static: false\n      }]\n    }],\n    nzSuccessTip: [{\n      type: Input\n    }],\n    nzWarningTip: [{\n      type: Input\n    }],\n    nzErrorTip: [{\n      type: Input\n    }],\n    nzValidatingTip: [{\n      type: Input\n    }],\n    nzExtra: [{\n      type: Input\n    }],\n    nzAutoTips: [{\n      type: Input\n    }],\n    nzDisableAutoTips: [{\n      type: Input\n    }],\n    nzHasFeedback: [{\n      type: Input\n    }],\n    nzValidateStatus: [{\n      type: Input\n    }]\n  });\n})();\nfunction toTooltipIcon(value) {\n  const icon = typeof value === 'string' ? {\n    type: value\n  } : value;\n  return {\n    ...DefaultTooltipIcon,\n    ...icon\n  };\n}\nclass NzFormLabelComponent {\n  set nzNoColon(value) {\n    this.noColon = toBoolean(value);\n  }\n  get nzNoColon() {\n    return this.noColon !== 'default' ? this.noColon : this.nzFormDirective?.nzNoColon;\n  }\n  set nzTooltipIcon(value) {\n    this._tooltipIcon = toTooltipIcon(value);\n  }\n  // due to 'get' and 'set' accessor must have the same type, so it was renamed to `tooltipIcon`\n  get tooltipIcon() {\n    return this._tooltipIcon !== 'default' ? this._tooltipIcon : toTooltipIcon(this.nzFormDirective?.nzTooltipIcon || DefaultTooltipIcon);\n  }\n  set nzLabelAlign(value) {\n    this.labelAlign = value;\n  }\n  get nzLabelAlign() {\n    return this.labelAlign !== 'default' ? this.labelAlign : this.nzFormDirective?.nzLabelAlign || 'right';\n  }\n  set nzLabelWrap(value) {\n    this.labelWrap = toBoolean(value);\n  }\n  get nzLabelWrap() {\n    return this.labelWrap !== 'default' ? this.labelWrap : this.nzFormDirective?.nzLabelWrap;\n  }\n  constructor(cdr, nzFormDirective) {\n    this.cdr = cdr;\n    this.nzFormDirective = nzFormDirective;\n    this.nzRequired = false;\n    this.noColon = 'default';\n    this._tooltipIcon = 'default';\n    this.labelAlign = 'default';\n    this.labelWrap = 'default';\n    this.destroy$ = new Subject();\n    if (this.nzFormDirective) {\n      this.nzFormDirective.getInputObservable('nzNoColon').pipe(filter(() => this.noColon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzTooltipIcon').pipe(filter(() => this._tooltipIcon === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelAlign').pipe(filter(() => this.labelAlign === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n      this.nzFormDirective.getInputObservable('nzLabelWrap').pipe(filter(() => this.labelWrap === 'default'), takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzFormLabelComponent_Factory(t) {\n      return new (t || NzFormLabelComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzFormDirective, 12));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormLabelComponent,\n      selectors: [[\"nz-form-label\"]],\n      hostAttrs: [1, \"ant-form-item-label\"],\n      hostVars: 4,\n      hostBindings: function NzFormLabelComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-label-left\", ctx.nzLabelAlign === \"left\")(\"ant-form-item-label-wrap\", ctx.nzLabelWrap);\n        }\n      },\n      inputs: {\n        nzFor: \"nzFor\",\n        nzRequired: \"nzRequired\",\n        nzNoColon: \"nzNoColon\",\n        nzTooltipTitle: \"nzTooltipTitle\",\n        nzTooltipIcon: \"nzTooltipIcon\",\n        nzLabelAlign: \"nzLabelAlign\",\n        nzLabelWrap: \"nzLabelWrap\"\n      },\n      exportAs: [\"nzFormLabel\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 6,\n      consts: [[\"nz-tooltip\", \"\", 1, \"ant-form-item-tooltip\", 3, \"nzTooltipTitle\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", 3, \"nzType\", \"nzTheme\"]],\n      template: function NzFormLabelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"label\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵtemplate(2, NzFormLabelComponent_Conditional_2_Template, 2, 2, \"span\", 0);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-form-item-no-colon\", ctx.nzNoColon)(\"ant-form-item-required\", ctx.nzRequired);\n          i0.ɵɵattribute(\"for\", ctx.nzFor);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(2, ctx.nzTooltipTitle ? 2 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i5.NzStringTemplateOutletDirective, NzTooltipDirective, NzIconModule, i3.NzIconDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzFormLabelComponent.prototype, \"nzRequired\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormLabelComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-label',\n      exportAs: 'nzFormLabel',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <label [attr.for]=\"nzFor\" [class.ant-form-item-no-colon]=\"nzNoColon\" [class.ant-form-item-required]=\"nzRequired\">\n      <ng-content></ng-content>\n      @if (nzTooltipTitle) {\n        <span class=\"ant-form-item-tooltip\" nz-tooltip [nzTooltipTitle]=\"nzTooltipTitle\">\n          <ng-container *nzStringTemplateOutlet=\"tooltipIcon.type; let tooltipIconType\">\n            <span nz-icon [nzType]=\"tooltipIconType\" [nzTheme]=\"tooltipIcon.theme\"></span>\n          </ng-container>\n        </span>\n      }\n    </label>\n  `,\n      host: {\n        class: 'ant-form-item-label',\n        '[class.ant-form-item-label-left]': `nzLabelAlign === 'left'`,\n        '[class.ant-form-item-label-wrap]': `nzLabelWrap`\n      },\n      imports: [NzOutletModule, NzTooltipDirective, NzIconModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzFormDirective,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }], {\n    nzFor: [{\n      type: Input\n    }],\n    nzRequired: [{\n      type: Input\n    }],\n    nzNoColon: [{\n      type: Input\n    }],\n    nzTooltipTitle: [{\n      type: Input\n    }],\n    nzTooltipIcon: [{\n      type: Input\n    }],\n    nzLabelAlign: [{\n      type: Input\n    }],\n    nzLabelWrap: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormSplitComponent {\n  static {\n    this.ɵfac = function NzFormSplitComponent_Factory(t) {\n      return new (t || NzFormSplitComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormSplitComponent,\n      selectors: [[\"nz-form-split\"]],\n      hostAttrs: [1, \"ant-form-split\"],\n      exportAs: [\"nzFormSplit\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormSplitComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormSplitComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-split',\n      exportAs: 'nzFormSplit',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-split'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormTextComponent {\n  static {\n    this.ɵfac = function NzFormTextComponent_Factory(t) {\n      return new (t || NzFormTextComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFormTextComponent,\n      selectors: [[\"nz-form-text\"]],\n      hostAttrs: [1, \"ant-form-text\"],\n      exportAs: [\"nzFormText\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzFormTextComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormTextComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-form-text',\n      exportAs: 'nzFormText',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-form-text'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzFormModule {\n  static {\n    this.ɵfac = function NzFormModule_Factory(t) {\n      return new (t || NzFormModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzFormModule,\n      imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n      exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzFormLabelComponent, NzFormControlComponent, NzGridModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFormModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent],\n      exports: [NzGridModule, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormControlComponent, NzFormTextComponent, NzFormSplitComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DefaultTooltipIcon, NzFormControlComponent, NzFormDirective, NzFormItemComponent, NzFormLabelComponent, NzFormModule, NzFormSplitComponent, NzFormTextComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,QAAM,CAAC,EAAE;AACrB,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa,EAAE;AAAA,EACpB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,eAAe,MAAS;AACtC,IAAG,UAAU;AACb,IAAG,WAAW,WAAc,gBAAgB,GAAG,KAAK,2BAA2B,OAAO,MAAM,CAAC;AAC7F,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,QAAQ,EAAE,iCAAoC,gBAAgB,GAAG,KAAK,OAAO,eAAe,CAAC;AAAA,EAC9I;AACF;AACA,SAAS,6DAA6D,IAAI,KAAK;AAC7E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,8CAA8C,IAAI,KAAK;AAC9D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,8DAA8D,GAAG,GAAG,gBAAgB,CAAC;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,2DAA2D,IAAI,KAAK;AAC3E,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,CAAC;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,qBAAqB,IAAI;AAC/B,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,kBAAkB,EAAE,WAAW,OAAO,YAAY,KAAK;AAAA,EACjF;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,4DAA4D,GAAG,GAAG,gBAAgB,CAAC;AACpG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,kBAAkB,OAAO,cAAc;AACrD,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,YAAY,IAAI;AAAA,EACjE;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,mBAAmB,OAAO;AACxB,SAAK,gBAAgB;AACrB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,UAAU,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,eAAe,aAAa;AAC1B,SAAK,cAAc;AACnB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAwB,kBAAqB,iBAAiB,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,iCAAiC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,6BAA6B,IAAI,WAAW,SAAS,EAAE,6BAA6B,IAAI,WAAW,SAAS,EAAE,2BAA2B,IAAI,WAAW,OAAO,EAAE,+BAA+B,IAAI,WAAW,YAAY,EAAE,8BAA8B,IAAI,eAAe,IAAI,MAAM,EAAE,2BAA2B,IAAI,aAAa;AAAA,QACvV;AAAA,MACF;AAAA,MACA,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,qCAAqC;AAAA,QACrC,qCAAqC;AAAA,QACrC,mCAAmC;AAAA,QACnC,uCAAuC;AAAA,QACvC,sCAAsC;AAAA,QACtC,mCAAmC;AAAA,MACrC;AAAA,MACA,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAwB;AAC9B,IAAM,qBAAqB;AAAA,EACzB,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,mBAAmB,YAAY;AAC7B,WAAO,KAAK,cAAc,KAAK,OAAO,aAAW,cAAc,OAAO,GAAG,IAAI,WAAS,MAAM,UAAU,CAAC,CAAC;AAAA,EAC1G;AAAA,EACA,YAAY,iBAAiB,gBAAgB;AAC3C,SAAK,kBAAkB;AACvB,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,aAAa,CAAC;AACnB,SAAK,oBAAoB;AACzB,SAAK,gBAAgB;AACrB,SAAK,eAAe;AACpB,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AAAA,IACb,CAAC;AAAA,EACH;AAAA,EACA,YAAY,SAAS;AACnB,SAAK,cAAc,KAAK,OAAO;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,cAAc,SAAS;AAC5B,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,kBAAqB,eAAe,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC;AAAA,MAC/B,WAAW,CAAC,GAAG,UAAU;AAAA,MACzB,UAAU;AAAA,MACV,cAAc,SAAS,6BAA6B,IAAI,KAAK;AAC3D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,uBAAuB,IAAI,aAAa,YAAY,EAAE,qBAAqB,IAAI,aAAa,UAAU,EAAE,mBAAmB,IAAI,aAAa,QAAQ,EAAE,gBAAgB,IAAI,QAAQ,KAAK;AAAA,QACxM;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,QAAQ;AAAA,MACnB,YAAY;AAAA,MACZ,UAAU,CAAI,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,gBAAgB,WAAW,aAAa,MAAM;AACzF,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAgB,WAAW,cAAc,MAAM;AAC1E,WAAW,CAAC,aAAa,CAAC,GAAG,gBAAgB,WAAW,qBAAqB,MAAM;AACnF,WAAW,CAAC,WAAW,CAAC,GAAG,gBAAgB,WAAW,iBAAiB,MAAM;AAC7E,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,gBAAgB,WAAW,eAAe,MAAM;AAAA,CAC1F,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,+BAA+B;AAAA,QAC/B,6BAA6B;AAAA,QAC7B,2BAA2B;AAAA,QAC3B,wBAAwB;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,IAAI,kBAAkB;AACpB,WAAO,KAAK,sBAAsB,YAAY,UAAU,KAAK,iBAAiB,IAAI,KAAK,iBAAiB;AAAA,EAC1G;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,UAAU,KAAK;AACnC,SAAK,oBAAoB,kBAAkB,KAAK;AAAA,MAC9C,QAAQ,KAAK;AAAA,MACb,aAAa,KAAK;AAAA,IACpB,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,eAAe,KAAK,YAAY;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,iBAAiB,OAAO;AAC1B,QAAI,iBAAiB,mBAAmB,iBAAiB,SAAS;AAChE,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IACpB,WAAW,iBAAiB,iBAAiB;AAC3C,WAAK,kBAAkB,MAAM;AAC7B,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IACpB,OAAO;AACL,WAAK,iBAAiB;AACtB,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,eAAe;AACb,SAAK,gBAAgB,YAAY;AAEjC,QAAI,KAAK,mBAAmB,KAAK,gBAAgB,eAAe;AAC9D,WAAK,kBAAkB,KAAK,gBAAgB,cAAc,KAAK,UAAU,IAAI,GAAG,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC1H,YAAI,CAAC,KAAK,iBAAiB;AACzB,eAAK,mBAAmB;AAAA,QAC1B;AACA,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,SAAS,KAAK,iBAAiB,KAAK,cAAc;AACvD,SAAK,WAAW,KAAK,YAAY,KAAK,MAAM;AAC5C,SAAK,oBAAoB,kBAAkB,KAAK;AAAA,MAC9C,QAAQ,KAAK;AAAA,MACb,aAAa,KAAK;AAAA,IACpB,CAAC;AACD,QAAI,KAAK,qBAAqB;AAC5B,WAAK,oBAAoB,mBAAmB,CAAC,CAAC,KAAK,QAAQ;AAC3D,WAAK,oBAAoB,UAAU,KAAK,MAAM;AAAA,IAChD;AAAA,EACF;AAAA,EACA,iBAAiB,gBAAgB;AAC/B,QAAI;AACJ,QAAI,mBAAmB,aAAa,KAAK,sBAAsB,WAAW,SAAS,GAAG;AACpF,eAAS;AAAA,IACX,WAAW,mBAAmB,WAAW,KAAK,sBAAsB,SAAS,GAAG;AAC9E,eAAS;AAAA,IACX,WAAW,mBAAmB,gBAAgB,mBAAmB,aAAa,KAAK,sBAAsB,SAAS,GAAG;AACnH,eAAS;AAAA,IACX,WAAW,mBAAmB,aAAa,KAAK,sBAAsB,OAAO,GAAG;AAC9E,eAAS;AAAA,IACX,OAAO;AACL,eAAS;AAAA,IACX;AACA,WAAO;AAAA,EACT;AAAA,EACA,sBAAsB,aAAa,YAAY;AAC7C,QAAI,CAAC,KAAK,iBAAiB;AACzB,aAAO;AAAA,IACT,OAAO;AACL,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,cAAQ,CAAC,CAAC,SAAS,CAAC,CAAC,aAAa,aAAa,KAAK,gBAAgB,SAAS,UAAU,IAAI,WAAW;AAAA,IACxG;AAAA,EACF;AAAA,EACA,YAAY,QAAQ;AAClB,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,eAAO,CAAC,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,cAAc;AAAA,MAC1E,KAAK;AACH,eAAO,KAAK,mBAAmB;AAAA,MACjC,KAAK;AACH,eAAO,KAAK,gBAAgB;AAAA,MAC9B,KAAK;AACH,eAAO,KAAK,gBAAgB;AAAA,MAC9B;AACE,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,iBAAiB;AACxB,YAAM,SAAS,KAAK,gBAAgB,UAAU,CAAC;AAC/C,UAAI,eAAe;AACnB,iBAAW,OAAO,QAAQ;AACxB,YAAI,OAAO,eAAe,GAAG,GAAG;AAC9B,yBAAe,OAAO,GAAG,IAAI,KAAK,QAAQ,KAAK,KAAK,aAAa,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,WAAW,UAAU,GAAG,KAAK,KAAK,iBAAiB,aAAa,KAAK,QAAQ,IAAI,GAAG,KAAK,KAAK,iBAAiB,WAAW,UAAU,GAAG;AAAA,QACxO;AACA,YAAI,CAAC,CAAC,cAAc;AAClB;AAAA,QACF;AAAA,MACF;AACA,WAAK,eAAe;AAAA,IACtB;AAAA,EACF;AAAA,EACA,kBAAkB,YAAY;AAC5B,gBAAY,KAAK,UAAU,KAAK,UAAU,CAAC,EAAE,UAAU,MAAM;AAC3D,UAAI,CAAC,KAAK,iBAAiB;AACzB,aAAK,mBAAmB;AACxB,aAAK,UAAU;AACf,aAAK,IAAI,aAAa;AAAA,MACxB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,YAAY,qBAAqB,KAAK,MAAM,iBAAiB,qBAAqB;AAChF,SAAK,sBAAsB;AAC3B,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,sBAAsB;AAC3B,SAAK,eAAe;AACpB,SAAK,kBAAkB,aAAa;AACpC,SAAK,iBAAiB;AACtB,SAAK,aAAa,IAAI,QAAQ;AAC9B,SAAK,SAAS;AACd,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,aAAa,CAAC;AACnB,SAAK,oBAAoB;AACzB,SAAK,kBAAkB,KAAK,aAAa,KAAK,IAAI,YAAU,KAAK,WAAW,OAAO,MAAM,CAAC,CAAC;AAC3F,SAAK,kBAAkB,KAAK,iBAAiB,mBAAmB,YAAY,CAAC;AAC7E,SAAK,kBAAkB,KAAK,iBAAiB,mBAAmB,mBAAmB,EAAE,KAAK,OAAO,MAAM,KAAK,sBAAsB,SAAS,CAAC,CAAC;AAAA,EAC/I;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,qBAAqB,YAAY;AACnC,WAAK,mBAAmB;AACxB,WAAK,UAAU;AAAA,IACjB,WAAW,gBAAgB,gBAAgB,cAAc,iBAAiB;AACxE,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,WAAW;AACT,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AAAA,EAC3B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,mBAAmB,CAAC,KAAK,gBAAgB;AACjD,UAAI,KAAK,kCAAkC,sBAAsB;AAC/D,aAAK,mBAAmB,KAAK,uBAAuB;AAAA,MACtD,OAAO;AACL,aAAK,mBAAmB,KAAK;AAAA,MAC/B;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAkB,qBAAqB,CAAC,GAAM,kBAAqB,iBAAiB,GAAM,kBAAuB,aAAa,GAAM,kBAAkB,iBAAiB,CAAC,GAAM,kBAAqB,mBAAmB,CAAC;AAAA,IACrQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,iBAAiB,CAAC;AAAA,MAC/B,gBAAgB,SAAS,sCAAsC,IAAI,KAAK,UAAU;AAChF,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,WAAW,CAAC;AAAA,QAC1C;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,yBAAyB,GAAG;AAAA,QAC/E;AAAA,MACF;AAAA,MACA,WAAW,CAAC,GAAG,uBAAuB;AAAA,MACtC,QAAQ;AAAA,QACN,cAAc;AAAA,QACd,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,eAAe;AAAA,QACf,kBAAkB;AAAA,MACpB;AAAA,MACA,UAAU,CAAC,eAAe;AAAA,MAC1B,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,mBAAmB,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACxG,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,qCAAqC,GAAG,CAAC,GAAG,yBAAyB,iCAAiC,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,QAAQ,SAAS,GAAG,SAAS,GAAG,CAAC,GAAG,0BAA0B,+BAA+B,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACpT,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa,EAAE;AAClB,UAAG,WAAW,GAAG,+CAA+C,GAAG,GAAG,OAAO,CAAC,EAAE,GAAG,+CAA+C,GAAG,GAAG,OAAO,CAAC;AAAA,QAClJ;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,WAAW,IAAI,EAAE;AACzC,UAAG,UAAU;AACb,UAAG,cAAc,GAAG,IAAI,UAAU,IAAI,EAAE;AAAA,QAC1C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,gBAAmB,+BAA+B;AAAA,MAC1E,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,UAAU;AAAA,MACxB;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY,CAAC,UAAU;AAAA,MACvB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAsBV,WAAW,CAAC,mBAAmB;AAAA,MAC/B,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,SAAS,CAAC,SAAS,cAAc;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,wBAAwB,CAAC;AAAA,MACvB,MAAM;AAAA,MACN,MAAM,CAAC,WAAW;AAAA,QAChB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,cAAc,OAAO;AAC5B,QAAM,OAAO,OAAO,UAAU,WAAW;AAAA,IACvC,MAAM;AAAA,EACR,IAAI;AACJ,SAAO,kCACF,qBACA;AAEP;AACA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,IAAI,UAAU,OAAO;AACnB,SAAK,UAAU,UAAU,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,YAAY,YAAY,KAAK,UAAU,KAAK,iBAAiB;AAAA,EAC3E;AAAA,EACA,IAAI,cAAc,OAAO;AACvB,SAAK,eAAe,cAAc,KAAK;AAAA,EACzC;AAAA;AAAA,EAEA,IAAI,cAAc;AAChB,WAAO,KAAK,iBAAiB,YAAY,KAAK,eAAe,cAAc,KAAK,iBAAiB,iBAAiB,kBAAkB;AAAA,EACtI;AAAA,EACA,IAAI,aAAa,OAAO;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,IAAI,eAAe;AACjB,WAAO,KAAK,eAAe,YAAY,KAAK,aAAa,KAAK,iBAAiB,gBAAgB;AAAA,EACjG;AAAA,EACA,IAAI,YAAY,OAAO;AACrB,SAAK,YAAY,UAAU,KAAK;AAAA,EAClC;AAAA,EACA,IAAI,cAAc;AAChB,WAAO,KAAK,cAAc,YAAY,KAAK,YAAY,KAAK,iBAAiB;AAAA,EAC/E;AAAA,EACA,YAAY,KAAK,iBAAiB;AAChC,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,aAAa;AAClB,SAAK,UAAU;AACf,SAAK,eAAe;AACpB,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW,IAAI,QAAQ;AAC5B,QAAI,KAAK,iBAAiB;AACxB,WAAK,gBAAgB,mBAAmB,WAAW,EAAE,KAAK,OAAO,MAAM,KAAK,YAAY,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AACrK,WAAK,gBAAgB,mBAAmB,eAAe,EAAE,KAAK,OAAO,MAAM,KAAK,iBAAiB,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAC9K,WAAK,gBAAgB,mBAAmB,cAAc,EAAE,KAAK,OAAO,MAAM,KAAK,eAAe,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAC3K,WAAK,gBAAgB,mBAAmB,aAAa,EAAE,KAAK,OAAO,MAAM,KAAK,cAAc,SAAS,GAAG,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM,KAAK,IAAI,aAAa,CAAC;AAAA,IAC3K;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,iBAAiB,GAAM,kBAAkB,iBAAiB,EAAE,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,qBAAqB;AAAA,MACpC,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,4BAA4B,IAAI,iBAAiB,MAAM,EAAE,4BAA4B,IAAI,WAAW;AAAA,QACrH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,cAAc;AAAA,QACd,aAAa;AAAA,MACf;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,cAAc,IAAI,GAAG,yBAAyB,GAAG,gBAAgB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,GAAG,UAAU,SAAS,CAAC;AAAA,MACpJ,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,OAAO;AAC5B,UAAG,aAAa,CAAC;AACjB,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,QAAQ,CAAC;AAC7E,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,SAAS,EAAE,0BAA0B,IAAI,UAAU;AAChG,UAAG,YAAY,OAAO,IAAI,KAAK;AAC/B,UAAG,UAAU,CAAC;AACd,UAAG,cAAc,GAAG,IAAI,iBAAiB,IAAI,EAAE;AAAA,QACjD;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,iCAAiC,oBAAoB,cAAiB,eAAe;AAAA,MACvH,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,qBAAqB,WAAW,cAAc,MAAM;AAAA,CAChF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,oCAAoC;AAAA,QACpC,oCAAoC;AAAA,MACtC;AAAA,MACA,SAAS,CAAC,gBAAgB,oBAAoB,YAAY;AAAA,MAC1D,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAsB;AAAA,IACzC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,eAAe,CAAC;AAAA,MAC7B,WAAW,CAAC,GAAG,gBAAgB;AAAA,MAC/B,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,GAAG;AAClD,aAAO,KAAK,KAAK,sBAAqB;AAAA,IACxC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU,CAAC,YAAY;AAAA,MACvB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,6BAA6B,IAAI,KAAK;AACvD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,OAAO;AAAA,MACT;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,GAAG;AAC3C,aAAO,KAAK,KAAK,eAAc;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,MACvI,SAAS,CAAC,cAAc,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,IACvJ,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,sBAAsB,wBAAwB,YAAY;AAAA,IACtE,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,MACvI,SAAS,CAAC,cAAc,iBAAiB,qBAAqB,sBAAsB,wBAAwB,qBAAqB,oBAAoB;AAAA,IACvJ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}