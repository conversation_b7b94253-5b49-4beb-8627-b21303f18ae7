<!-- Agent Details Container -->
<div class="h-full flex flex-col" [ngClass]="isDarkMode ? 'bg-[var(--background-light-gray)]' : 'bg-gray-50'">
  
  <!-- Header Section -->
  <div class="flex-shrink-0 p-6 border-b" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)] bg-[var(--background-white)]' : 'border-gray-200 bg-white'">
    <!-- Breadcrumb -->
    <nz-breadcrumb class="mb-4">
      <nz-breadcrumb-item>
        <a (click)="goBack()" class="cursor-pointer" [ngClass]="isDarkMode ? 'text-[var(--primary-purple)]' : 'text-[var(--primary-purple)]'">
          Agent Analytics
        </a>
      </nz-breadcrumb-item>
      <nz-breadcrumb-item>
        <span [ngClass]="textClass">{{ agent?.agentName || 'Agent Details' }}</span>
      </nz-breadcrumb-item>
    </nz-breadcrumb>

    <div class="flex items-center justify-between">
      <div class="flex items-center gap-4">
        <div class="w-12 h-12 rounded-full flex items-center justify-center text-xl" 
             [ngClass]="agent?.agentType === 'AI' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'">
          <i [class]="agent?.agentType === 'AI' ? 'ri-robot-line' : 'ri-user-line'"></i>
        </div>
        <div>
          <h1 class="text-2xl font-semibold" [ngClass]="textClass">{{ agent?.agentName }}</h1>
          <p class="mt-1" [ngClass]="secondaryTextClass">{{ agent?.workArea }}</p>
          <div class="flex items-center gap-2 mt-2">
            <nz-tag [nzColor]="agent?.agentType === 'AI' ? 'blue' : 'green'">{{ agent?.agentType }}</nz-tag>
            <nz-tag [nzColor]="getStatusColor(agent?.status || 'inactive')">{{ agent?.status | titlecase }}</nz-tag>
          </div>
        </div>
      </div>
      
      <div class="flex items-center gap-3">
        <button nz-button nzType="default" (click)="goBack()">
          <i class="ri-arrow-left-line mr-2"></i>
          Back to Analytics
        </button>
      </div>
    </div>
  </div>

  <!-- Content Area -->
  <div class="flex-1 p-6 overflow-auto">
    <div *ngIf="loading" class="flex justify-center items-center h-64">
      <nz-spin nzSize="large"></nz-spin>
    </div>

    <div *ngIf="!loading && agent" class="space-y-6">
      <!-- Performance Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic 
            nzTitle="Total Tasks" 
            [nzValue]="agent.metrics.totalTasks"
            [nzValueStyle]="{'color': isDarkMode ? 'var(--text-dark)' : '#1f2937'}">
          </nz-statistic>
        </nz-card>
        
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic 
            nzTitle="Success Rate" 
            [nzValue]="agent.metrics.successRate"
            nzSuffix="%"
            [nzValueStyle]="{'color': getPerformanceColor(agent.metrics.successRate)}">
          </nz-statistic>
        </nz-card>
        
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic 
            nzTitle="Average Time" 
            [nzValue]="formatDuration(agent.metrics.averageTime)"
            [nzValueStyle]="{'color': '#6B46C1'}">
          </nz-statistic>
        </nz-card>
        
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic 
            nzTitle="Failed Tasks" 
            [nzValue]="agent.metrics.failedAttempts"
            [nzValueStyle]="{'color': '#ff4d4f'}">
          </nz-statistic>
        </nz-card>
      </div>

      <!-- Task Steps Breakdown -->
      <nz-card nzTitle="Task Steps Performance" [ngClass]="cardClass">
        <nz-table [nzData]="agent.taskSteps" [nzShowPagination]="false">
          <thead>
            <tr>
              <th>Step Name</th>
              <th>Average Time</th>
              <th>Performance</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let step of agent.taskSteps">
              <td>
                <span class="font-medium" [ngClass]="textClass">{{ step.stepName }}</span>
              </td>
              <td>{{ formatDuration(step.averageTime) }}</td>
              <td>
                <div class="flex items-center gap-2">
                  <nz-progress 
                    [nzPercent]="(step.averageTime / agent.metrics.averageTime) * 100" 
                    nzStrokeColor="#6B46C1"
                    nzSize="small" 
                    [nzShowInfo]="false"
                    class="flex-1">
                  </nz-progress>
                  <span class="text-sm" [ngClass]="secondaryTextClass">
                    {{ ((step.averageTime / agent.metrics.averageTime) * 100).toFixed(1) }}%
                  </span>
                </div>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- Quality Metrics -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <nz-card nzTitle="Quality Metrics" [ngClass]="cardClass">
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span [ngClass]="secondaryTextClass">Grammar Quality:</span>
              <div class="flex items-center gap-2">
                <nz-progress 
                  [nzPercent]="agent.quality.grammarQuality" 
                  nzStrokeColor="#52c41a"
                  nzSize="small" 
                  [nzShowInfo]="false"
                  class="w-24">
                </nz-progress>
                <span class="font-semibold text-green-600">{{ agent.quality.grammarQuality }}%</span>
              </div>
            </div>
            
            <div class="flex items-center justify-between">
              <span [ngClass]="secondaryTextClass">Readability Score:</span>
              <div class="flex items-center gap-2">
                <nz-progress 
                  [nzPercent]="agent.quality.readabilityScore" 
                  nzStrokeColor="#1890ff"
                  nzSize="small" 
                  [nzShowInfo]="false"
                  class="w-24">
                </nz-progress>
                <span class="font-semibold text-blue-600">{{ agent.quality.readabilityScore }}</span>
              </div>
            </div>
            
            <div class="flex items-center justify-between">
              <span [ngClass]="secondaryTextClass">Copied Content:</span>
              <div class="flex items-center gap-2">
                <nz-progress 
                  [nzPercent]="agent.quality.copiedContent" 
                  nzStrokeColor="#ff4d4f"
                  nzSize="small" 
                  [nzShowInfo]="false"
                  class="w-24">
                </nz-progress>
                <span class="font-semibold text-red-600">{{ agent.quality.copiedContent }}%</span>
              </div>
            </div>
            
            <nz-divider></nz-divider>
            
            <div class="flex items-center justify-between">
              <span class="font-medium" [ngClass]="textClass">Overall Quality:</span>
              <span class="text-lg font-bold" [ngClass]="getPerformanceColor(agent.quality.overallQuality)">
                {{ agent.quality.overallQuality }}%
              </span>
            </div>
          </div>
        </nz-card>

        <nz-card nzTitle="Activity Periods" [ngClass]="cardClass">
          <div class="space-y-4">
            <div *ngFor="let period of agent.activityPeriods" 
                 class="p-3 rounded-lg border" 
                 [ngClass]="isDarkMode ? 'bg-[var(--hover-blue-gray)] border-[var(--hover-blue-gray)]' : 'bg-gray-50 border-gray-200'">
              <div class="flex items-center justify-between mb-2">
                <span class="font-medium" [ngClass]="textClass">{{ period.period }}</span>
                <nz-tag [nzColor]="getPerformanceColor(period.successRate)">{{ period.successRate }}%</nz-tag>
              </div>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span [ngClass]="secondaryTextClass">Tasks: </span>
                  <span class="font-medium" [ngClass]="textClass">{{ period.taskCount }}</span>
                </div>
                <div>
                  <span [ngClass]="secondaryTextClass">Avg Time: </span>
                  <span class="font-medium" [ngClass]="textClass">{{ formatDuration(period.averageTime) }}</span>
                </div>
              </div>
            </div>
          </div>
        </nz-card>
      </div>

      <!-- Failed Tasks -->
      <nz-card nzTitle="Failed Tasks" [ngClass]="cardClass" *ngIf="agent.failedTasks.length > 0">
        <nz-table [nzData]="agent.failedTasks" [nzShowPagination]="false">
          <thead>
            <tr>
              <th>Task ID</th>
              <th>Reason</th>
              <th>Timestamp</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let task of agent.failedTasks">
              <td>
                <span class="font-mono text-sm bg-red-100 text-red-800 px-2 py-1 rounded">{{ task.taskId }}</span>
              </td>
              <td>{{ task.reason }}</td>
              <td>{{ task.timestamp.toLocaleString() }}</td>
            </tr>
          </tbody>
        </nz-table>
      </nz-card>

      <!-- Performance Insights -->
      <nz-card nzTitle="Performance Insights" [ngClass]="cardClass">
        <div class="space-y-3">
          <nz-alert 
            *ngFor="let insight of agent.insights"
            [nzMessage]="insight.title"
            [nzDescription]="insight.description + (insight.suggestion ? ' - ' + insight.suggestion : '')"
            [nzType]="insight.type === 'positive' ? 'success' : insight.type === 'negative' ? 'warning' : 'info'"
            nzShowIcon>
          </nz-alert>
        </div>
      </nz-card>
    </div>

    <!-- Error State -->
    <div *ngIf="!loading && !agent" class="text-center py-12">
      <i class="ri-error-warning-line text-4xl mb-4" [ngClass]="secondaryTextClass"></i>
      <h3 class="text-lg font-medium mb-2" [ngClass]="textClass">Agent Not Found</h3>
      <p [ngClass]="secondaryTextClass">The requested agent could not be found.</p>
      <button nz-button nzType="primary" class="mt-4" (click)="goBack()">
        Back to Analytics
      </button>
    </div>
  </div>
</div>
