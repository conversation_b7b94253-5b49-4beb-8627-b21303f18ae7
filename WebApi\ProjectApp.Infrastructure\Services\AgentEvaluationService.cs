using Microsoft.SemanticKernel.ChatCompletion;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Services
{
    public class AgentEvaluationService
    {
        private readonly IAgentEvaluationRepository _agentEvaluationRepository;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly IChatCompletionService _chatCompletionService;
        private readonly AIAgentFactory _aiAgentFactory;

        public AgentEvaluationService(
            IAgentEvaluationRepository agentEvaluationRepository, 
            IAgentDefinitionRepository agentDefinitionRepository,
            IChatCompletionService chatCompletionService,
            AIAgentFactory aiAgentFactory)
        {
            _agentEvaluationRepository = agentEvaluationRepository;
            _agentDefinitionRepository = agentDefinitionRepository;
            _chatCompletionService = chatCompletionService;
            _aiAgentFactory = aiAgentFactory;
        }

        public async Task<IEnumerable<AgentEvaluationDto>> GetAllEvaluationsAsync()
        {
            var evaluations = await _agentEvaluationRepository.GetAllAsync();
            return evaluations.Select(MapToDto);
        }

        public async Task<IEnumerable<AgentEvaluationDto>> GetEvaluationsByAgentNameAsync(string agentName)
        {
            var evaluations = await _agentEvaluationRepository.GetByAgentNameAsync(agentName);
            return evaluations.Select(MapToDto);
        }

        public async Task<AgentEvaluationDto> RunEvaluationAsync(CreateAgentEvaluationDto evaluationDto)
        {
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(evaluationDto.AgentName);
            if (agentDefinition == null)
            {
                throw new Exception($"Agent {evaluationDto.AgentName} not found");
            }
            
            // Create and save the initial evaluation
            var evaluation = new AgentEvaluation
            {
                AgentName = evaluationDto.AgentName,
                Prompt = evaluationDto.Prompt,
                ExpectedOutput = evaluationDto.ExpectedOutput,
                CreatedAt = DateTime.Now
            };
            
            // Save the evaluation
            await _agentEvaluationRepository.CreateAsync(evaluation);
            
            return MapToDto(evaluation);
        }

        /// <summary>
        /// Execute all evaluations for a specific agent
        /// </summary>
        public async Task<IEnumerable<AgentEvaluationDto>> ExecuteAllEvaluationsForAgentAsync(string agentName)
        {
            // Validate agent exists
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
            if (agentDefinition == null)
            {
                throw new Exception($"Agent {agentName} not found");
            }
            
            // Get all evaluations for this agent
            var evaluations = await _agentEvaluationRepository.GetByAgentNameAsync(agentName);
            var results = new List<AgentEvaluationDto>();
            
            foreach (var evaluation in evaluations)
            {
                try
                {
                    // Process the evaluation through the AI Agent Factory
                    var actualOutput = await _aiAgentFactory.CallAIAgentAsync(evaluation.AgentName, evaluation.Prompt);
                    evaluation.Output = actualOutput;
                    
                    // Update the evaluation with the output
                    await _agentEvaluationRepository.UpdateAsync(evaluation);
                    
                    // Get score via evaluation by comparing expected vs actual output
                    var evalResult = await EvaluateResponseAsync(evaluation.Id);
                    
                    // Map to DTO and add to results
                    results.Add(MapToDto(evaluation));
                }
                catch (Exception ex)
                {
                    evaluation.Output = $"Error: {ex.Message}";
                    await _agentEvaluationRepository.UpdateAsync(evaluation);
                    results.Add(MapToDto(evaluation));
                }
            }
            
            return results;
        }

        /// <summary>
        /// Re-evaluate an agent based on all executed evaluations
        /// </summary>
        public async Task<EvaluationResultDto> ReEvaluateAgentAsync(string agentName, IEnumerable<Guid> evaluationIds)
        {
            // Get all specified evaluations for this agent that have outputs
            var evaluations = new List<AgentEvaluation>();
            
            foreach (var id in evaluationIds)
            {
                var evaluation = await _agentEvaluationRepository.GetByIdAsync(id);
                if (evaluation != null && 
                    evaluation.AgentName == agentName && 
                    !string.IsNullOrEmpty(evaluation.Output))
                {
                    evaluations.Add(evaluation);
                }
            }
            
            if (!evaluations.Any())
            {
                throw new Exception($"No executed evaluations found for agent {agentName}");
            }
            
            // Create a system prompt for the overall agent evaluation
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(@"
You are an expert AI assistant evaluator. Your task is to evaluate the overall quality and effectiveness of an AI agent based on multiple prompt-response pairs.
Analyze all the given prompt-response pairs, then provide a consolidated evaluation focusing on how to improve the agent's prompt/instructions.

Your evaluation MUST be in valid JSON format with these properties:
- score: a number from 0.0 to 10.0 representing the overall agent quality
- evaluation: a detailed evaluation explaining strengths and weaknesses, patterns, and overall performance
- improvementSuggestions: specific suggestions for modifying the agent's prompt/instructions
- updatedPrompt: a complete rewrite of the agent's instructions that incorporates your improvements

IMPORTANT: Your entire response must ONLY contain valid JSON. Do not include any explanatory text, markdown code blocks, or formatting outside of the JSON object. The JSON must be properly formatted and directly parseable.
");

            // Get the agent definition to include current instructions
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
            var currentInstructions = agentDefinition?.Instructions ?? "No instructions available";

            var promptsAndResponses = string.Join("\n\n---\n\n", evaluations.Select((e, i) => 
                $"PAIR {i + 1}:\nPrompt: {e.Prompt}\nExpected Output (provided by user): {e.ExpectedOutput}\nActual Response: {e.Output}" +
                (e.Score.HasValue ? $"\nIndividual Score: {e.Score}/10" : "")
            ));

            chatHistory.AddUserMessage($@"
Current Agent Instructions:
{currentInstructions}

I need a comprehensive evaluation of an AI agent based on the following {evaluations.Count} prompt-response pairs:

{promptsAndResponses}

Please evaluate the agent's overall performance on a scale from 0.0 to 10.0 using these criteria:
1. Accuracy and Correctness (How accurate and correct are the responses?)
2. Completeness (How completely do they address the requirements?)
3. Relevance (How relevant are the responses to the prompts?)
4. Clarity and Coherence (How clear and coherent are the responses?)
5. Consistency (How consistent is the agent across different prompts?)

Provide a detailed analysis highlighting:
- Patterns in the agent's responses
- Common strengths across responses
- Common weaknesses or failure modes

Based on this evaluation:
1. Suggest specific modifications to the agent's instructions/prompt that would help it perform better on similar queries.
2. Provide a complete rewrite of the agent's instructions that incorporates your suggested improvements.

Provide your evaluation in the following JSON format:
{{
  ""score"": [score between 0.0 and 10.0],
  ""evaluation"": ""[detailed evaluation explaining strengths, weaknesses, and patterns]"",
  ""improvementSuggestions"": ""[specific suggestions for modifying the agent's prompt/instructions]"",
  ""updatedPrompt"": ""[complete rewritten instructions including all your improvements]""
}}
");

            // Get evaluation using chat completion service
            var response = await _chatCompletionService.GetChatMessageContentAsync(chatHistory);
            var evaluationResult = response.Content;
            
            try
            {
                // Sanitize and extract clean JSON from the response
                string jsonContent = SanitizeJsonResponse(evaluationResult);
                
                // Attempt to deserialize the JSON
                var result = JsonSerializer.Deserialize<EvaluationResultDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                // Generate a new ID for this aggregate evaluation
                result.EvaluationId = Guid.NewGuid();
                
                return result;
            }
            catch (JsonException ex)
            {
                // Log the exception details for debugging
                Console.WriteLine($"JSON parsing error in re-evaluation: {ex.Message}");
                
                // If the AI didn't return proper JSON, create a more basic evaluation
                return new EvaluationResultDto
                {
                    EvaluationId = Guid.NewGuid(),
                    Score = evaluations.Where(e => e.Score.HasValue).Average(e => e.Score.Value), // Average of scores
                    Evaluation = "Could not parse AI evaluation. This is the raw response from the evaluation model.",
                    ImprovementSuggestions = evaluationResult, // Use the raw response as suggestions
                    UpdatedPrompt = currentInstructions // Keep original instructions as fallback
                };
            }
        }

        public async Task<EvaluationResultDto> EvaluateResponseAsync(Guid evaluationId)
        {
            var evaluation = await _agentEvaluationRepository.GetByIdAsync(evaluationId);
            if (evaluation == null)
            {
                throw new Exception($"Evaluation with ID {evaluationId} not found");
            }

            if (string.IsNullOrEmpty(evaluation.Output))
            {
                throw new Exception("No output to evaluate");
            }

            // Create a system prompt for evaluating the response
            var chatHistory = new ChatHistory();
            chatHistory.AddSystemMessage(@"
You are an expert AI assistant evaluator. Your task is to evaluate the quality and effectiveness of an AI agent's response
and suggest improvements to the agent's prompt/instructions.
Analyze the given prompt, expected output, and the agent's actual response, then provide an evaluation.

Your evaluation MUST be in valid JSON format with these properties:
- score: a number from 0.0 to 10.0
- evaluation: a detailed evaluation explaining strengths and weaknesses
- improvementSuggestions: specific suggestions for modifying the agent's prompt to produce better responses
- updatedPrompt: a complete rewrite of the agent's instructions that incorporates your improvements

IMPORTANT: Your entire response must ONLY contain valid JSON. Do not include any explanatory text, markdown code blocks, or formatting outside of the JSON object. The JSON must be properly formatted and directly parseable.
");

            // First, let's get the agent definition to know the current instructions
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(evaluation.AgentName);
            var currentInstructions = agentDefinition?.Instructions ?? "No instructions available";

            chatHistory.AddUserMessage($@"
Current Agent Instructions:
{currentInstructions}

Prompt given to the Agent: 
{evaluation.Prompt}

Expected Output (provided by user):
{evaluation.ExpectedOutput}

Actual AI Agent Response:
{evaluation.Output}

Please evaluate the response on a scale from 0.0 to 10.0 using these criteria:
1. Accuracy: How well does the actual output match the expected output? (40% weight)
2. Completeness: Did the agent cover all aspects mentioned in the expected output? (20% weight)
3. Correctness: Is the information provided by the agent factually correct? (20% weight)
4. Clarity & Coherence: How clear and well-structured is the agent's response? (10% weight) 
5. Improvements: How could the agent be improved to produce output closer to the expected? (10% weight)

Based on this evaluation:
1. Suggest specific modifications to the agent's instructions/prompt that would help it produce results closer to the expected output.
2. Provide a complete rewrite of the agent's instructions that incorporates your suggested improvements.

Provide your evaluation in the following JSON format:
{{
  ""score"": [score between 0.0 and 10.0],
  ""evaluation"": ""[detailed evaluation explaining the strengths and weaknesses]"",
  ""improvementSuggestions"": ""[specific suggestions for modifying the agent's prompt/instructions]"",
  ""updatedPrompt"": ""[complete rewritten instructions including all your improvements]""
}}
");

            // Get evaluation using chat completion service
            var response = await _chatCompletionService.GetChatMessageContentAsync(chatHistory);
            var evaluationResult = response.Content;
            
            try
            {
                // Sanitize and extract clean JSON from the response
                string jsonContent = SanitizeJsonResponse(evaluationResult);
                
                // Attempt to deserialize the JSON
                var result = JsonSerializer.Deserialize<EvaluationResultDto>(jsonContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                // Update the evaluation in the database, just storing the score
                evaluation.Score = result.Score;
                evaluation.EvaluatedAt = DateTime.Now;
                
                await _agentEvaluationRepository.UpdateAsync(evaluation);
                
                result.EvaluationId = evaluationId;
                return result;
            }
            catch (JsonException ex)
            {
                // Log the exception details for debugging
                Console.WriteLine($"JSON parsing error: {ex.Message}");
                
                // If the AI didn't return proper JSON, create a more basic evaluation
                var fallbackResult = new EvaluationResultDto
                {
                    EvaluationId = evaluationId,
                    Score = 5.0, // Default middle score
                    Evaluation = "Could not parse AI evaluation.",
                    ImprovementSuggestions = evaluationResult, // Use the raw response as suggestions
                    UpdatedPrompt = currentInstructions // Keep original instructions as fallback
                };
                
                // Update the evaluation in the database, just storing the score
                evaluation.Score = fallbackResult.Score;
                evaluation.EvaluatedAt = DateTime.Now;
                
                await _agentEvaluationRepository.UpdateAsync(evaluation);
                
                return fallbackResult;
            }
        }

        /// <summary>
        /// Gets just the updated prompt from an evaluation result
        /// </summary>
        /// <param name="evaluationId">ID of the evaluation to get the updated prompt from</param>
        /// <returns>The updated prompt as a string</returns>
        public async Task<string> GetUpdatedPromptAsync(Guid evaluationId)
        {
            var evaluationResult = await EvaluateResponseAsync(evaluationId);
            
            if (string.IsNullOrEmpty(evaluationResult.UpdatedPrompt))
            {
                throw new Exception("The evaluation does not contain an updated prompt");
            }
            
            return evaluationResult.UpdatedPrompt;
        }

        /// <summary>
        /// Update an existing evaluation
        /// </summary>
        /// <param name="evaluationId">ID of the evaluation to update</param>
        /// <param name="updateDto">Update data</param>
        /// <returns>Updated evaluation DTO</returns>
        public async Task<AgentEvaluationDto> UpdateEvaluationAsync(Guid evaluationId, UpdateAgentEvaluationDto updateDto)
        {
            var evaluation = await _agentEvaluationRepository.GetByIdAsync(evaluationId);
            if (evaluation == null)
            {
                return null;
            }

            // Update the evaluation fields
            evaluation.Prompt = updateDto.Prompt;
            evaluation.ExpectedOutput = updateDto.ExpectedOutput;
            
            // Clear previous execution results when updating the prompt or expected output
            evaluation.Output = null;
            evaluation.Score = null;
            evaluation.EvaluatedAt = null;

            var success = await _agentEvaluationRepository.UpdateAsync(evaluation);
            if (!success)
            {
                throw new Exception("Failed to update evaluation");
            }

            return MapToDto(evaluation);
        }

        /// <summary>
        /// Delete an evaluation
        /// </summary>
        /// <param name="evaluationId">ID of the evaluation to delete</param>
        /// <returns>True if deleted successfully</returns>
        public async Task<bool> DeleteEvaluationAsync(Guid evaluationId)
        {
            return await _agentEvaluationRepository.DeleteAsync(evaluationId);
        }

        private AgentEvaluationDto MapToDto(AgentEvaluation evaluation)
        {
            return new AgentEvaluationDto
            {
                Id = evaluation.Id,
                AgentName = evaluation.AgentName,
                Prompt = evaluation.Prompt,
                ExpectedOutput = evaluation.ExpectedOutput,
                Output = evaluation.Output,
                Score = evaluation.Score,
                CreatedAt = evaluation.CreatedAt,
                EvaluatedAt = evaluation.EvaluatedAt
            };
        }

        /// <summary>
        /// Helper method to sanitize and extract clean JSON from potentially formatted text
        /// </summary>
        /// <param name="rawResponse">The raw response text that might contain JSON</param>
        /// <returns>Sanitized JSON string</returns>
        private string SanitizeJsonResponse(string rawResponse)
        {
            // If empty or null, return empty string
            if (string.IsNullOrWhiteSpace(rawResponse))
                return "{}";
                
            string jsonContent = rawResponse.Trim();
            
            // Check if response is wrapped in markdown code blocks
            if (jsonContent.Contains("```"))
            {
                // Try to extract content between markdown json code blocks first
                if (jsonContent.Contains("```json"))
                {
                    int startIndex = jsonContent.IndexOf("```json") + "```json".Length;
                    int endIndex = jsonContent.IndexOf("```", startIndex);
                    
                    if (startIndex > 0 && endIndex > startIndex)
                    {
                        jsonContent = jsonContent.Substring(startIndex, endIndex - startIndex).Trim();
                    }
                }
                // Try to extract content between regular markdown code blocks
                else
                {
                    int startIndex = jsonContent.IndexOf("```") + "```".Length;
                    int endIndex = jsonContent.IndexOf("```", startIndex);
                    
                    if (startIndex > 0 && endIndex > startIndex)
                    {
                        jsonContent = jsonContent.Substring(startIndex, endIndex - startIndex).Trim();
                    }
                }
            }
            
            // Try to extract JSON-like content using regex if needed
            if (!jsonContent.StartsWith("{") || !jsonContent.EndsWith("}"))
            {
                var jsonPattern = @"\{(?:[^{}]|(?<open>\{)|(?<-open>\}))+(?(open)(?!))\}";
                var match = System.Text.RegularExpressions.Regex.Match(jsonContent, jsonPattern);
                
                if (match.Success)
                {
                    jsonContent = match.Value;
                }
            }
            
            return jsonContent;
        }

        /// <summary>
        /// Submit feedback for an agent response and save as evaluation
        /// </summary>
        /// <param name="feedbackDto">Feedback data from user</param>
        /// <returns>Created evaluation DTO</returns>
        public async Task<AgentEvaluationDto> SubmitFeedbackAsync(AgentFeedbackDto feedbackDto)
        {
            // Validate agent exists
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(feedbackDto.AgentName);
            if (agentDefinition == null)
            {
                throw new Exception($"Agent {feedbackDto.AgentName} not found");
            }
            
            // Create evaluation from feedback
            var evaluation = new AgentEvaluation
            {
                AgentName = feedbackDto.AgentName,
                Prompt = feedbackDto.Prompt,
                Output = feedbackDto.Output,
                Score = feedbackDto.Score,
                ExpectedOutput = feedbackDto.IsPositiveFeedback ? "User provided positive feedback" : "User provided negative feedback",
                CreatedAt = DateTime.Now,
                EvaluatedAt = DateTime.Now
            };
            
            // Save the evaluation
            await _agentEvaluationRepository.CreateAsync(evaluation);
            
            return MapToDto(evaluation);
        }
    }
}
