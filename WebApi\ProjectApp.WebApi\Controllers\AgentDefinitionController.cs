﻿using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ProjectApp.Infrastructure.AIAgents;
using System.Diagnostics;
using System.Reflection.Metadata;
using System.Text;
using Microsoft.SemanticKernel.ChatCompletion;
using ProjectApp.Infrastructure;
using Microsoft.SemanticKernel.Agents;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AgentDefinitionController(IAgentDefinitionRepository _agentDefinitionRepository, AIAgentFactory _aiAgentFactory, AIService _aiService) : ControllerBase
    {
        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<AgentDefinition>>> GetAll()
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllAsync();
            return Ok(agentDefinitions);
        }

        [HttpGet("GetAllAgentName")]
        [AllowAnonymous]
        public async Task<ActionResult<List<AgentDefinitionDto>>> GetAllAgentName()
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllAgentName();
            return Ok(agentDefinitions);
        }

        [HttpGet("GetAllByWorkspace")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<List<AgentDefinition>>> GetAllByWorkspace(string workspace)
        {
            var agentDefinitions = await _agentDefinitionRepository.GetAllByWorkspace(workspace);
            return Ok(agentDefinitions);
        }

        [HttpGet("GetByAgentName/{agentName}")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<AgentDefinition>> GetByAgentName(string agentName)
        {
            var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
            if (agentDefinition == null)
            {
                return NotFound(new ResponseMessage { IsError = true, Message = $"Agent definition with name {agentName} not found." });
            }
            return Ok(agentDefinition);
        }
        [HttpPost("CreateOrUpdate")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<AgentDefinition>> CreateOrUpdate(AgentDefinitionDto agentDefinition)
        {
            var res = await _agentDefinitionRepository.CreateOrUpdate(agentDefinition);

            // Automatically add navigation entry for the new/updated agent
            try
            {
                //var navigationEntry = new NavigationEntryDto
                //{
                //    Title = agentDefinition.AgentName,
                //    Description = agentDefinition.Instructions ?? $"AI Agent: {agentDefinition.AgentName}",
                //    Route = $"/agent-chat/{agentDefinition.AgentName}",
                //    NavigationType = "Agent",
                //    Icon = "🤖"
                //};

                //await _aiService.AddNavigationEntry(navigationEntry);


                var chatEntry = new NavigationEntryDto
                {
                    Id = "Navigation-Chat-" + agentDefinition.AgentName,
                    Title = agentDefinition.AgentName,
                    Description = $"Chat with {agentDefinition.AgentName}" + (!string.IsNullOrEmpty(agentDefinition.UserInstructions) ? $" - {agentDefinition.UserInstructions}" : ""),
                    NavigationType = "Agent",
                    Route = $"/chat/agent/{agentDefinition.AgentName}",
                    Icon = "ri-chat-3-line",
                };

                // Create config navigation entry (for settings page)
                var configEntry = new NavigationEntryDto
                {
                    Id = "Navigation-Config-" + agentDefinition.AgentName,
                    Title = $"{agentDefinition.AgentName} (Config)",
                    Description = $"Configure and edit the {agentDefinition.AgentName} agent",
                    NavigationType = "Settings",
                    Route = $"/settings/agents/{agentDefinition.AgentName}",
                    Icon = "ri-settings-line",
                };

                // Add both entries to Kernel Memory
                await _aiService.AddNavigationEntry(chatEntry);
                await _aiService.AddNavigationEntry(configEntry);
            }
            catch (Exception ex)
            {
                // Log but don't fail the agent creation if navigation entry fails
                Console.WriteLine($"Warning: Failed to add navigation entry for agent {agentDefinition.AgentName}: {ex.Message}");
            }

            return Ok(res);
        }
        [HttpDelete("Delete/{agentName}")]
        [Authorize(Roles = "Admin")]

        public async Task<ActionResult<ResponseMessage>> Delete(string agentName)
        {
            try
            {
                var deleted = await _agentDefinitionRepository.DeleteByAgentName(agentName);
                if (!deleted)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent definition with name {agentName} not found." });
                }

                // Try to remove navigation entry for deleted agent
                try
                {
                    await _aiService.DeleteSingleMemory("Navigation-Config-" + agentName, "Navigation");
                    await _aiService.DeleteSingleMemory("Navigation-Chat-" + agentName, "Navigation");
                    Console.WriteLine($"Agent {agentName} deleted - navigation entry should be removed");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to remove navigation entry for deleted agent {agentName}: {ex.Message}");
                }

                return Ok(new ResponseMessage { IsError = false, Message = "Agent definition deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }

        [HttpPost("SyncToMemory")]
        public async Task<IActionResult> SyncToMemory()
        {
            try
            {
                await _agentDefinitionRepository.SyncToMemory();
                return Ok(new ResponseMessage { IsError = false, Message = "Agent definitions synced to memory successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new ResponseMessage { IsError = true, Message = ex.Message });
            }
        }

        [HttpPost("TestAgentCall")]
        public async Task<ActionResult<ResponseMessage>> TestAgentCall(string agentName, string question)
        {
            var response = await _aiService.CallAgentManually(agentName, question);
            if (response.IsError)
            {
                return BadRequest(response);
            }
            return Ok(response);
        }


        [HttpGet("TotalAgentInWorkspace")]
        public async Task<ActionResult<int>> TotalAgentInWorkspace(string workspace)
        {
            var count = await _agentDefinitionRepository.TotalAgentCountInWorkspace(workspace);
            return Ok(count);
        }



    }
}
