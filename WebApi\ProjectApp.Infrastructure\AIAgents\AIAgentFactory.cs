﻿using DocumentFormat.OpenXml.Office.SpreadSheetML.Y2023.MsForms;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.SemanticKernel.Plugins.OpenApi;
using Microsoft.SemanticKernel.Plugins;
using OpenTelemetry;
using OpenTelemetry.Logs;
using OpenTelemetry.Metrics;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Threading;
using ProjectApp.Infrastructure.AIAgents.Tools;
using ProjectApp.Infrastructure.Repositories;
using System.Reflection;
using System.Text;
using System.Diagnostics;
using ModelContextProtocol.Protocol.Transport;
using ModelContextProtocol.Protocol.Types;
using ModelContextProtocol.Client;
using ModelContextProtocol;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using ProjectApp.Infrastructure.Services;

namespace ProjectApp.Infrastructure.AIAgents;

public class AIAgentFactory : IDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly IModelDetailsRepository _modelDetailsRepository;
    private readonly IApiCredentialsRepository _apiCredentialsRepository;
    private readonly IAgentDefinitionRepository _agentDefinitionRepositoy;
    private readonly IKernelMemory _memory;
    private readonly IPluginRepository _pluginRepository;
    private readonly ILogger<AIAgentFactory> _logger;
    private readonly IMemoryCache _memoryCache;
    private readonly SemaphoreSlim _cacheLock = new(1, 1);

    // Cache configuration
    private static readonly TimeSpan CacheExpiration = TimeSpan.FromHours(24);
    private const string CacheKeyPrefix = "AIAgent_";

    public AIAgentFactory(
        IServiceProvider serviceProvider,
        IModelDetailsRepository modelDetailsRepository,
        IApiCredentialsRepository apiCredentialsRepository,
        IAgentDefinitionRepository agentDefinitionRepository,
        IKernelMemory memory,
        IPluginRepository pluginRepository,
        IMemoryCache memoryCache,
        ILogger<AIAgentFactory> logger = null)
    {
        _serviceProvider = serviceProvider;
        _modelDetailsRepository = modelDetailsRepository;
        _apiCredentialsRepository = apiCredentialsRepository;
        _agentDefinitionRepositoy = agentDefinitionRepository;
        _memory = memory;
        _pluginRepository = pluginRepository;
        _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger), "Logger must be provided");

        _logger.LogInformation("AIAgentFactory initialized with caching and telemetry enabled");
    }

    /// <summary>
    /// Generates a unique cache key for an agent based on its configuration
    /// </summary>
    private string GenerateCacheKey(AgentDefinitionDto agentDefinitionDto)
    {
        var toolsHash = agentDefinitionDto.Tools?.Length > 0
            ? string.Join(",", agentDefinitionDto.Tools.OrderBy(t => t)).GetHashCode().ToString()
            : "NoTools";

        var argumentsHash = agentDefinitionDto.Arguments?.Count > 0
            ? string.Join(",", agentDefinitionDto.Arguments.OrderBy(kvp => kvp.Key).Select(kvp => $"{kvp.Key}:{kvp.Value}")).GetHashCode().ToString()
            : "NoArgs";

        return $"{CacheKeyPrefix}{agentDefinitionDto.AgentName}_{agentDefinitionDto.ModelName}_{toolsHash}_{argumentsHash}";
    }

    /// <summary>
    /// Checks if an agent is cached
    /// </summary>
    public bool IsAgentCached(string agentName, string modelName, string[] tools = null, Dictionary<string, object> arguments = null)
    {
        var agentDefinitionDto = new AgentDefinitionDto
        {
            AgentName = agentName,
            ModelName = modelName,
            Tools = tools,
            Arguments = arguments
        };

        var cacheKey = GenerateCacheKey(agentDefinitionDto);
        return _memoryCache.TryGetValue(cacheKey, out _);
    }

    public async Task<ChatCompletionAgent> CreateAIAgent(AgentDefinitionDto agentDefinitionDto)
    {
        // Generate cache key
        var cacheKey = GenerateCacheKey(agentDefinitionDto);

        // Try to get from cache first
        if (_memoryCache.TryGetValue(cacheKey, out ChatCompletionAgent cachedAgent))
        {
            _logger.LogInformation("Retrieved cached AI agent: {AgentName} with model: {ModelName}",
                agentDefinitionDto.AgentName, agentDefinitionDto.ModelName);
            return cachedAgent;
        }

        // Use semaphore to prevent multiple threads from creating the same agent simultaneously
        await _cacheLock.WaitAsync();
        try
        {
            // Double-check pattern: check cache again after acquiring lock
            if (_memoryCache.TryGetValue(cacheKey, out cachedAgent))
            {
                _logger.LogInformation("Retrieved cached AI agent after lock: {AgentName} with model: {ModelName}",
                    agentDefinitionDto.AgentName, agentDefinitionDto.ModelName);
                return cachedAgent;
            }

            _logger.LogInformation("Creating new AI agent: {AgentName} with model: {ModelName}",
                agentDefinitionDto.AgentName, agentDefinitionDto.ModelName);

            var modelDetails = await _modelDetailsRepository.GetByModelNameAsync(agentDefinitionDto.ModelName);
            if (modelDetails == null || !modelDetails.IsActive)
            {
                _logger.LogError("Model '{ModelName}' not found or inactive", agentDefinitionDto.ModelName);
                throw new ArgumentException($"Model '{agentDefinitionDto.ModelName}' not found or inactive.");
            }

            var credentials = modelDetails.ApiCredentialsId.HasValue
               ? await _apiCredentialsRepository.GetById(modelDetails.ApiCredentialsId.Value)
               : null;

            if (credentials == null)
            {
                _logger.LogError("No credentials found for provider '{Provider}'", modelDetails.ModelProvider);
                throw new InvalidOperationException($"No credentials found for provider '{modelDetails.ModelProvider}'.");
            }

            var kernelBuilder = Kernel.CreateBuilder();
            // Kernel will use the application's configured logging

            var modelName = agentDefinitionDto.ModelName;
            if (!string.IsNullOrEmpty(modelName) && modelName.Contains('_'))
            {
                modelName = modelName[(modelName.IndexOf('_') + 1)..];
            }
            // Get the chat source service from the service provider
            var chatSourceService = _serviceProvider.GetRequiredService<IChatSourceService>();
            // Get the logger for LoggingFilter
            var loggingFilterLogger = _serviceProvider.GetRequiredService<ILogger<LoggingFilter>>();
            // Create and register the logging filter with the chat source service and logger
            kernelBuilder.Services.AddSingleton<IFunctionInvocationFilter>(new LoggingFilter(chatSourceService, loggingFilterLogger));

            ConfigureKernelForProvider(kernelBuilder, modelDetails.ModelProvider, credentials, modelName);

            var kernel = kernelBuilder.Build();

            if (agentDefinitionDto.Tools != null && agentDefinitionDto.Tools.Length > 0)
            {
                var availableClassTypes = Assembly.GetExecutingAssembly().GetTypes();
                foreach (var toolName in agentDefinitionDto.Tools)
                {
                    // First check if it's a plugin stored in our database
                    var plugin = await _pluginRepository.GetPluginByNameAsync(toolName);
                    if (plugin != null)
                    {
                        if (plugin.Type?.ToLower() == "openapi")
                        {
                            // It's an OpenAPI plugin from our database
                            await kernel.ImportPluginFromOpenApiAsync(
                                pluginName: plugin.PluginName,
                                uri: new Uri(plugin.Url),
                                executionParameters: new OpenApiFunctionExecutionParameters
                                {
                                    EnablePayloadNamespacing = true
                                });
                            continue;
                        }
                        else if (plugin.Type?.ToLower() == "mcp")
                        {
                            // Handle MCP plugin
                            //await AddMcpPluginToKernel(kernel, plugin);
                            //continue;
                            throw new NotImplementedException("MCP plugin handling is not implemented yet.");
                        }
                    }

                    // Otherwise, check if it's a local plugin
                    var pluginType = availableClassTypes.FirstOrDefault(t => t.Name == toolName);
                    if (pluginType != null)
                    {
                        var pluginInstance = _serviceProvider.GetRequiredService(pluginType);
                        kernel.Plugins.AddFromObject(pluginInstance);
                    }
                }
            }

            // Create kernel arguments with function choice behavior and temperature
            var kernelArguments = new KernelArguments(new OpenAIPromptExecutionSettings()
            {
                FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(),
                Temperature = 0
            });

            // Add any custom arguments from the agent definition
            if (agentDefinitionDto.Arguments != null && agentDefinitionDto.Arguments.Count > 0)
            {
                foreach (var arg in agentDefinitionDto.Arguments)
                {
                    kernelArguments[arg.Key] = arg.Value;
                }
            }

            // Load source context if agent has a source
            var enhancedInstructions = await LoadSourceContextAsync(agentDefinitionDto);

            // Define the agent
            ChatCompletionAgent agent = new()
            {
                Name = agentDefinitionDto.AgentName,
                Instructions = enhancedInstructions,
                Kernel = kernel,
                Arguments = kernelArguments,
            };

            // Cache the agent with sliding expiration
            var cacheEntryOptions = new MemoryCacheEntryOptions
            {
                SlidingExpiration = CacheExpiration,
                Priority = CacheItemPriority.High,
                Size = 1 // Each agent counts as 1 unit for cache size management
            };

            _memoryCache.Set(cacheKey, agent, cacheEntryOptions);

            _logger.LogInformation("Cached new AI agent: {AgentName} with model: {ModelName} for {Hours} hours",
                agentDefinitionDto.AgentName, agentDefinitionDto.ModelName, CacheExpiration.TotalHours);

            return agent;
        }
        finally
        {
            _cacheLock.Release();
        }
    }

    // Helper method to add MCP plugin to the Kernel
    private async Task AddMcpPluginToKernel(Kernel kernel, PluginResponseDto plugin)
    {
        if (string.IsNullOrEmpty(plugin.Url))
        {
            throw new ArgumentException($"MCP plugin '{plugin.PluginName}' requires a valid URL or configuration.");
        }

        string pluginName = plugin.PluginName;

        if (plugin.Url.StartsWith("docker://"))
        {
            string dockerImage = plugin.Url.Replace("docker://", "").Trim();
            await ImportMcpPluginFromDocker(kernel, pluginName, dockerImage, plugin.RequiredParameters, plugin.EnvironmentVariables);
        }
        else if (plugin.Url.StartsWith("npx://"))
        {
            string npxPackage = plugin.Url.Replace("npx://", "").Trim();
            await ImportMcpPluginFromNpx(kernel, pluginName, npxPackage, plugin.RequiredParameters, plugin.EnvironmentVariables);
        }
        else
        {
            throw new NotSupportedException($"Unsupported MCP plugin URL format: {plugin.Url}");
        }
    }

    // Helper method to sanitize plugin names for Semantic Kernel
    private string SanitizePluginName(string pluginName)
    {
        return string.Concat(pluginName.Select(c => char.IsLetterOrDigit(c) || c == '_' ? c : '_'));
    }

    // Import MCP plugin from a Docker container
    private async Task ImportMcpPluginFromDocker(Kernel kernel, string pluginName, string dockerImage, string requiredParameters, string environmentVariables)
    {
        // Parse environment variables (if any)
        Dictionary<string, string> envVars = string.IsNullOrEmpty(environmentVariables)
            ? null
            : JsonSerializer.Deserialize<Dictionary<string, string>>(environmentVariables);

        // Set environment variables at the process level
        List<(string Key, string OriginalValue)> originalEnvVars = new();
        if (envVars != null)
        {
            foreach (var kvp in envVars)
            {
                string originalValue = Environment.GetEnvironmentVariable(kvp.Key);
                originalEnvVars.Add((kvp.Key, originalValue));
                Environment.SetEnvironmentVariable(kvp.Key, kvp.Value);
            }
        }

        try
        {
            var serverConfig = new McpServerConfig
            {
                Id = pluginName,
                Name = pluginName,
                TransportType = "stdio",
                TransportOptions = new Dictionary<string, string>
                {
                    ["command"] = "docker",
                    ["arguments"] = $"run -i --rm {dockerImage}".Trim()
                }
            };

            var mcpClient = await McpClientFactory.CreateAsync(serverConfig);
            var tools = await mcpClient.ListToolsAsync();

            // Log the tools for debugging
            foreach (var tool in tools)
            {
                Console.WriteLine($"[{pluginName}] Tool: {tool.Name}, Description: {tool.Description}");
            }

            Dictionary<string, Dictionary<string, string>> toolParameters = string.IsNullOrEmpty(requiredParameters)
                ? new Dictionary<string, Dictionary<string, string>>()
                : JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(requiredParameters);

            var kernelFunctions = new List<KernelFunction>();
            foreach (var tool in tools)
            {
                var kernelFunction = KernelFunctionFactory.CreateFromMethod(
                    async (KernelArguments args) =>
                    {
                        if (toolParameters.TryGetValue(tool.Name, out var parameters))
                        {
                            var toolArgs = new Dictionary<string, object>();
                            foreach (var param in parameters.Keys)
                            {
                                if (!args.ContainsName(param))
                                {
                                    throw new ArgumentException($"Missing required parameter '{param}' for tool '{tool.Name}' in plugin '{pluginName}'.");
                                }
                                toolArgs[param] = args[param];
                            }
                            try
                            {
                                Console.WriteLine($"[{pluginName}] Calling tool {tool.Name} with arguments: {JsonSerializer.Serialize(toolArgs)}");
                                var result = await mcpClient.CallToolAsync(tool.Name, toolArgs, null, CancellationToken.None);
                                Console.WriteLine($"[{pluginName}] Tool {tool.Name} returned: {result}");
                                return result;
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"[{pluginName}] Error executing tool {tool.Name}: {ex.Message}");
                                throw new Exception($"Failed to execute tool {tool.Name} in plugin {pluginName}: {ex.Message}", ex);
                            }
                        }
                        else
                        {
                            return await mcpClient.CallToolAsync(tool.Name, new Dictionary<string, object>(), null, CancellationToken.None);
                        }
                    },
                    tool.Name,
                    tool.Description);
                kernelFunctions.Add(kernelFunction);
            }

            string sanitizedPluginName = SanitizePluginName(pluginName);
            if (kernelFunctions.Any())
            {
                kernel.Plugins.AddFromFunctions(sanitizedPluginName, kernelFunctions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[{pluginName}] Failed to initialize plugin: {ex.Message}");
            throw;
        }
        finally
        {
            // Restore original environment variables
            foreach (var (key, originalValue) in originalEnvVars)
            {
                if (originalValue == null)
                {
                    Environment.SetEnvironmentVariable(key, null);
                }
                else
                {
                    Environment.SetEnvironmentVariable(key, originalValue);
                }
            }
        }
    }

    // Import MCP plugin from an NPX package
    private async Task ImportMcpPluginFromNpx(Kernel kernel, string pluginName, string npxPackage, string requiredParameters, string environmentVariables)
    {
        // Parse environment variables (if any)
        Dictionary<string, string> envVars = string.IsNullOrEmpty(environmentVariables)
            ? null
            : JsonSerializer.Deserialize<Dictionary<string, string>>(environmentVariables);

        // Set environment variables at the process level
        List<(string Key, string OriginalValue)> originalEnvVars = new();
        if (envVars != null)
        {
            foreach (var kvp in envVars)
            {
                string originalValue = Environment.GetEnvironmentVariable(kvp.Key);
                originalEnvVars.Add((kvp.Key, originalValue));
                Environment.SetEnvironmentVariable(kvp.Key, kvp.Value);
            }
        }

        try
        {
            var serverConfig = new McpServerConfig
            {
                Id = pluginName,
                Name = pluginName,
                TransportType = "stdio",
                TransportOptions = new Dictionary<string, string>
                {
                    ["command"] = "npx",
                    ["arguments"] = $"-y {npxPackage}".Trim()
                }
            };

            var mcpClient = await McpClientFactory.CreateAsync(serverConfig);
            var tools = await mcpClient.ListToolsAsync();

            // Log the tools for debugging
            foreach (var tool in tools)
            {
                Console.WriteLine($"[{pluginName}] Tool: {tool.Name}, Description: {tool.Description}");
            }

            Dictionary<string, Dictionary<string, string>> toolParameters = string.IsNullOrEmpty(requiredParameters)
                ? new Dictionary<string, Dictionary<string, string>>()
                : JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, string>>>(requiredParameters);

            var kernelFunctions = new List<KernelFunction>();
            foreach (var tool in tools)
            {
                var kernelFunction = KernelFunctionFactory.CreateFromMethod(
                    async (KernelArguments args) =>
                    {
                        if (toolParameters.TryGetValue(tool.Name, out var parameters))
                        {
                            var toolArgs = new Dictionary<string, object>();
                            foreach (var param in parameters.Keys)
                            {
                                if (!args.ContainsName(param))
                                {
                                    throw new ArgumentException($"Missing required parameter '{param}' for tool '{tool.Name}' in plugin '{pluginName}'.");
                                }
                                toolArgs[param] = args[param];
                            }
                            try
                            {
                                Console.WriteLine($"[{pluginName}] Calling tool {tool.Name} with arguments: {JsonSerializer.Serialize(toolArgs)}");
                                var result = await mcpClient.CallToolAsync(tool.Name, toolArgs, null, CancellationToken.None);
                                Console.WriteLine($"[{pluginName}] Tool {tool.Name} returned: {result}");
                                return result;
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"[{pluginName}] Error executing tool {tool.Name}: {ex.Message}");
                                throw new Exception($"Failed to execute tool {tool.Name} in plugin {pluginName}: {ex.Message}", ex);
                            }
                        }
                        else
                        {
                            return await mcpClient.CallToolAsync(tool.Name, new Dictionary<string, object>(), null, CancellationToken.None);
                        }
                    },
                    tool.Name,
                    tool.Description);
                kernelFunctions.Add(kernelFunction);
            }

            string sanitizedPluginName = SanitizePluginName(pluginName);
            if (kernelFunctions.Any())
            {
                kernel.Plugins.AddFromFunctions(sanitizedPluginName, kernelFunctions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[{pluginName}] Failed to initialize plugin: {ex.Message}");
            throw;
        }
        finally
        {
            // Restore original environment variables
            foreach (var (key, originalValue) in originalEnvVars)
            {
                if (originalValue == null)
                {
                    Environment.SetEnvironmentVariable(key, null);
                }
                else
                {
                    Environment.SetEnvironmentVariable(key, originalValue);
                }
            }
        }
    }

    public async Task<ResponseMessageList> GetPluginClassNames()
    {
        var namespaceToSearch = "ProjectApp.Infrastructure.AIAgents.Tools";
        var pluginClassNames = Assembly.GetExecutingAssembly()
            .GetTypes()
            .Where(t => t.Namespace == namespaceToSearch && t.IsClass && t.Name.EndsWith("Plugin"))
            .Select(t => t.Name)  // Select only the class names
            .ToList();

        return new ResponseMessageList { IsError = false, Message = pluginClassNames };
    }

    //private async Task<ApiCredentials> GetCredentialsForProvider(string provider)
    //{
    //    var allCredentials = await _apiCredentialsRepository.GetAll();
    //    return allCredentials.FirstOrDefault(c => IsProviderMatch(c.TokenUrl, provider));
    //}

    //private bool IsProviderMatch(string tokenUrl, string provider)
    //{
    //    var uri = new Uri(tokenUrl.ToLower());
    //    return provider.ToLower() switch
    //    {
    //        "openai" => uri.Host.Contains("openai.com"),
    //        "google" => uri.Host.Contains("generativelanguage.googleapis.com"),
    //        "localai" => uri.Host.Contains("localai"),
    //        _ => false
    //    };
    //}

    private void ConfigureKernelForProvider(IKernelBuilder builder, string provider, ApiCredentials credentials, string modelName)
    {
        var httpClient = _serviceProvider.GetRequiredService<IHttpClientFactory>().CreateClient();

        switch (provider.ToLower())
        {
            case "openai":
                builder.AddOpenAIChatCompletion(modelName, credentials.ApiKey);
                break;

            case "localai":
                builder.AddOllamaChatCompletion(modelName, new Uri(credentials.TokenUrl.Replace("/models", "")));
                break;

            case "google":
                builder.AddGoogleAIGeminiChatCompletion(modelName, credentials.ApiKey);
                break;

            case "azure":
                var baseUrl = GetBaseUrl(credentials.TokenUrl);
                builder.AddAzureOpenAIChatCompletion(modelName, baseUrl, credentials.ApiKey);
                break;

            default:
                throw new NotSupportedException($"Provider '{provider}' is not supported by the AI agent factory.");
        }
    }
    private string GetBaseUrl(string tokenUrl)
    {
        if (Uri.TryCreate(tokenUrl, UriKind.Absolute, out var uri))
        {
            return $"{uri.Scheme}://{uri.Host}";
        }
        throw new ArgumentException("Invalid token URL format.", nameof(tokenUrl));
    }

    public async IAsyncEnumerable<ResponseMessage> StreamAgentResponseAsync(string agentName, string question, string modelName = null, List<ChatHistories> historyDto = null, List<MemoryTag> tags = null, string index = null)
    {
        var agentDefinition = await _agentDefinitionRepositoy.GetByAgentName(agentName);
        if (agentDefinition == null)
        {
            yield return new ResponseMessage { IsError = true, Message = $"Agent with name {agentName} not found." };
            yield break;
        }

        modelName ??= agentDefinition.ModelName;

        var agentDefinitionDto = new AgentDefinitionDto
        {
            AgentName = agentDefinition.AgentName,
            Instructions = agentDefinition.Instructions,
            ModelName = modelName,
            Tools = agentDefinition.ToolsArray
        };

        var agent = await CreateAIAgent(agentDefinitionDto);
        ChatHistory chat = new ChatHistory();
        chat.AddUserMessage(question);

        if (historyDto != null)
        {
            var message = GetChatStringForAI(historyDto);
            chat.AddAssistantMessage(message);
        }

        if (!string.IsNullOrEmpty(index))
        {
            var filter = new MemoryFilter();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    filter.ByTag(memoryTag.Name, memoryTag.Value);
                }
            }

            var memoryAnswer = await _memory.SearchAsync(question, index: index, filter: filter, minRelevance: 0.4);
            var documentIds = memoryAnswer.Results.Select(result => result.DocumentId).ToList();
        }

        // Determine the response type based on the agent name
        string responseType = "simpleView"; // Default view

        // Set specific view types for special agents
        if (agentName.Equals("SqlQueryAgent", StringComparison.OrdinalIgnoreCase))
        {
            responseType = "sqlView";
        }
        else if (agentName.Equals("BlogGeneratorAgent", StringComparison.OrdinalIgnoreCase))
        {
            responseType = "blogView";
        }
        else if (agentName.Equals("EmailGeneratorAgent", StringComparison.OrdinalIgnoreCase) || agentName.Equals("EmailContentGenerator", StringComparison.OrdinalIgnoreCase))
        {
            responseType = "emailView";
        }

        await foreach (var chunk in agent.InvokeStreamingAsync(chat))
        {
            if (!string.IsNullOrEmpty(chunk.Content))
            {
                yield return new ResponseMessage
                {
                    IsError = false,
                    Message = chunk.Content,
                    ResponseType = responseType
                };
            }
        }
    }

    public async Task<ResponseMessage> AgentResponseAsync(string agentName, string question, Dictionary<string, object> agentArguments = null)
    {
        using var activity = System.Diagnostics.Activity.Current?.Source.StartActivity($"AgentResponseAsync:{agentName}");
        activity?.AddTag("agent.name", agentName);
        activity?.AddTag("agent.question", question);

        _logger.LogInformation("Agent response requested for agent: {AgentName}", agentName);

        var agentDefinition = await _agentDefinitionRepositoy.GetByAgentName(agentName);
        if (agentDefinition == null)
        {
            _logger.LogWarning("Agent with name {AgentName} not found", agentName);
            return new ResponseMessage { IsError = true, Message = $"Agent with name {agentName} not found." };
        }

        var agentDefinitionDto = new AgentDefinitionDto
        {
            AgentName = agentDefinition.AgentName,
            Instructions = agentDefinition.Instructions,
            ModelName = agentDefinition.ModelName,
            Tools = agentDefinition.ToolsArray,
            Arguments = new Dictionary<string, object>
               {
                   { "userQuestion", "" },
                   { "availableAgents", ""}
               }
        };

        _logger.LogInformation("Creating agent {AgentName} with model {ModelName}",
            agentDefinition.AgentName, agentDefinition.ModelName);

        var agent = await CreateAIAgent(agentDefinitionDto);
        ChatHistory chat = new ChatHistory();
        chat.AddUserMessage(question);

        //// Create a KernelArguments object with the question as a parameter
        var kernelArguments = new KernelArguments();
        kernelArguments["question"] = question;

        // Add any custom arguments provided
        if (agentArguments != null)
        {
            _logger.LogInformation("Adding {Count} custom arguments to agent request", agentArguments.Count);
            foreach (var arg in agentArguments)
            {
                kernelArguments[arg.Key] = arg.Value;
                activity?.AddTag($"agent.argument.{arg.Key}", arg.Value?.ToString() ?? "null");
            }
        }

        try
        {
            _logger.LogInformation("Invoking agent {AgentName}", agentDefinition.AgentName);

            // Aggregate the streaming response into a single string
            var contentBuilder = new System.Text.StringBuilder();
            //await foreach (var chunk in agent.InvokeAsync(chat, options: new() { KernelArguments = kernelArguments }))
            await foreach (var chunk in agent.InvokeAsync(chat, kernelArguments))
            {
                if (!string.IsNullOrEmpty(chunk.Content))
                {
                    contentBuilder.Append(chunk.Content);
                }
            }

            var response = contentBuilder.ToString();
            _logger.LogInformation("Agent {AgentName} response completed successfully with {Length} characters",
                agentDefinition.AgentName, response.Length);

            activity?.AddTag("agent.response.length", response.Length);
            activity?.AddTag("agent.response.success", true);
            var responseType = "simpleView"; // Default view
            return new ResponseMessage { IsError = false, Message = response, ResponseType = responseType };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking agent {AgentName}: {ErrorMessage}",
                agentDefinition.AgentName, ex.Message);

            activity?.AddTag("agent.response.success", false);
            activity?.AddTag("agent.response.error", ex.Message);
            activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, ex.Message);

            return new ResponseMessage { IsError = true, Message = $"Error invoking agent: {ex.Message}" };
        }
    }
    public static string GetChatStringForAI(List<ChatHistories> chatHistories)
    {
        var messages = chatHistories.OrderBy(c => c.CreatedAt).ToList(); // Sort by time

        StringBuilder sb = new StringBuilder();

        foreach (var chat in messages)
        {
            if (chat.IsEdited)
            {
                sb.AppendLine($"User (edited): {chat.Message} (Sent at: {chat.CreatedAt})");
            }
            else
            {
                sb.AppendLine($"User: {chat.Message} (Sent at: {chat.CreatedAt})");
            }

            foreach (var response in chat.Responses.OrderBy(r => r.CreatedAt))
            {
                sb.AppendLine($"AI: {response.Response} (Replied at: {response.CreatedAt})");
            }
        }

        return sb.ToString();
    }

    public async Task<string> CallAIAgentAsync(string agentName, string input)
    {
        using var activity = System.Diagnostics.Activity.Current?.Source.StartActivity($"CallAIAgentAsync:{agentName}");
        activity?.AddTag("agent.name", agentName);
        activity?.AddTag("agent.input", input);

        _logger.LogInformation("Calling AI agent: {AgentName}", agentName);

        var agentDefinition = await _agentDefinitionRepositoy.GetByAgentName(agentName);
        if (agentDefinition == null)
        {
            _logger.LogWarning("Agent with name {AgentName} not found", agentName);
            throw new Exception($"Agent with name {agentName} not found.");
        }

        var agentDefinitionDto = new AgentDefinitionDto
        {
            AgentName = agentDefinition.AgentName,
            Instructions = agentDefinition.Instructions,
            ModelName = agentDefinition.ModelName,
            Tools = agentDefinition.ToolsArray
        };

        try
        {
            _logger.LogInformation("Creating agent {AgentName} with model {ModelName}",
                agentDefinition.AgentName, agentDefinition.ModelName);

            var agent = CreateAIAgent(agentDefinitionDto);
            var chat = new ChatHistory();
            chat.AddUserMessage(input);

            _logger.LogInformation("Invoking agent {AgentName}", agentDefinition.AgentName);

            var response = new StringBuilder();
            await foreach (var chunk in agent.Result.InvokeAsync(chat))
            {
                if (!string.IsNullOrEmpty(chunk.Content))
                {
                    response.Append(chunk.Content);
                }
            }

            var responseText = response.ToString();
            _logger.LogInformation("Agent {AgentName} response completed successfully with {Length} characters",
                agentDefinition.AgentName, responseText.Length);

            activity?.AddTag("agent.response.length", responseText.Length);
            activity?.AddTag("agent.response.success", true);

            return responseText;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invoking agent {AgentName}: {ErrorMessage}",
                agentDefinition.AgentName, ex.Message);

            activity?.AddTag("agent.response.success", false);
            activity?.AddTag("agent.response.error", ex.Message);
            activity?.SetStatus(System.Diagnostics.ActivityStatusCode.Error, ex.Message);

            throw; // Rethrow to maintain original behavior
        }
    }

    /// <summary>
    /// Load source context for agents that have a configured source
    /// </summary>
    private async Task<string> LoadSourceContextAsync(AgentDefinitionDto agentDefinitionDto)
    {
        var instructions = agentDefinitionDto.Instructions;

        // Check if agent has a source configured
        if (string.IsNullOrEmpty(agentDefinitionDto.SourceType) || string.IsNullOrEmpty(agentDefinitionDto.SourceName))
        {
            return instructions;
        }

        try
        {
            string sourceContext = "";

            switch (agentDefinitionDto.SourceType.ToLower())
            {
                case "database":
                    sourceContext = await LoadDatabaseSourceContextAsync(agentDefinitionDto.SourceName);
                    break;
                case "wikipedia":
                    // Future implementation
                    sourceContext = "Wikipedia source integration coming soon.";
                    break;
                case "filesystem":
                    // Future implementation
                    sourceContext = "File system source integration coming soon.";
                    break;
                default:
                    _logger.LogWarning("Unknown source type: {SourceType}", agentDefinitionDto.SourceType);
                    return instructions;
            }

            // Inject source context into instructions
            var enhancedInstructions = $@"{instructions}

AVAILABLE DATA SOURCE:
{sourceContext}

Use the above data source information to help answer user questions. When generating SQL queries, use the exact table and column names provided in the schema.";

            _logger.LogInformation("Enhanced agent {AgentName} with {SourceType} source context",
                agentDefinitionDto.AgentName, agentDefinitionDto.SourceType);

            return enhancedInstructions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading source context for agent {AgentName}", agentDefinitionDto.AgentName);
            return instructions; // Return original instructions if source loading fails
        }
    }

    /// <summary>
    /// Load database schema context from Kernel Memory
    /// </summary>
    private async Task<string> LoadDatabaseSourceContextAsync(string connectionName)
    {
        try
        {
            var schemaService = _serviceProvider.GetRequiredService<DatabaseSchemaService>();
            return await schemaService.GetSchemaContextAsync(connectionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading database schema context for connection {ConnectionName}", connectionName);
            return $"Error loading database schema for connection '{connectionName}': {ex.Message}";
        }
    }

    /// <summary>
    /// Dispose method to clean up resources
    /// </summary>
    public void Dispose()
    {
        _cacheLock?.Dispose();
    }
}
