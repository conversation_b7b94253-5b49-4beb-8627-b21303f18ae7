using System;
using System.Collections.Generic;

namespace ProjectApp.Core.Dtos
{
    public class AgentEvaluationDto
    {
        public Guid Id { get; set; }
        
        public string AgentName { get; set; }
        
        public string Prompt { get; set; }
        
        public string ExpectedOutput { get; set; }
        
        public string Output { get; set; }
        
        public double? Score { get; set; }
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime? EvaluatedAt { get; set; }
    }

    public class CreateAgentEvaluationDto
    {
        public string AgentName { get; set; }
        
        public string Prompt { get; set; }
        
        public string ExpectedOutput { get; set; }
    }

    public class UpdateAgentEvaluationDto
    {
        public string Prompt { get; set; }
        
        public string ExpectedOutput { get; set; }
    }

    public class EvaluationResultDto
    {
        public Guid EvaluationId { get; set; }
        
        public double Score { get; set; }
        
        public string Evaluation { get; set; }
        
        public string ImprovementSuggestions { get; set; }
        
        public string UpdatedPrompt { get; set; }
    }
    
    public class BatchExecuteRequestDto
    {
        public string AgentName { get; set; }
        
        public IEnumerable<Guid> EvaluationIds { get; set; }
    }
    
    public class AgentFeedbackDto
    {
        public string AgentName { get; set; }
        
        public string Prompt { get; set; }
        
        public string Output { get; set; }
        
        public double Score { get; set; }
        
        public bool IsPositiveFeedback { get; set; }
    }
}
