using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;

namespace ProjectApp.Core.Repositories
{
    public interface IDatabaseSchemaRepository
    {
        Task<bool> SaveSchemaAsync(Guid connectionId, List<DatabaseTable> tables);
        
        Task<DatabaseSchemaDto?> GetSchemaByConnectionIdAsync(Guid connectionId);
        
        Task<DatabaseSchemaDto?> GetSchemaByConnectionNameAsync(string connectionName);
        
        Task<List<DatabaseTableDto>> GetTablesByConnectionIdAsync(Guid connectionId);
        
        Task<DatabaseTableDto?> GetTableSchemaAsync(Guid connectionId, string tableName);
        
        Task<bool> DeleteSchemaAsync(Guid connectionId);
        
        Task<bool> SchemaExistsAsync(Guid connectionId);
        
        Task<DateTime?> GetLastExtractionTimeAsync(Guid connectionId);
    }
}
