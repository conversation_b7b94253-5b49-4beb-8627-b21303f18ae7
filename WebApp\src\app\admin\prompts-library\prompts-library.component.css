/* Custom styles for the delete confirmation modal */
    .custom-delete-modal .ant-modal-content {
      background-color: var(--background-white);
      border-radius: var(--border-radius-large);
      box-shadow: var(--box-shadow);
      padding: var(--padding-medium);
    }

    .custom-delete-modal .ant-modal-header {
      background-color: var(--background-white);
      border-bottom: none;
      padding: 0;
      margin-bottom: var(--margin-small);
    }

    .custom-delete-modal .ant-modal-title {
      color: var(--text-dark);
      font-family: var(--font-family);
      font-size: var(--font-size-header);
      font-weight: var(--font-weight-bold);
    }

    .custom-delete-modal .ant-modal-body {
      color: var(--text-medium-gray);
      font-family: var(--font-family);
      font-size: var(--font-size-body);
      line-height: var(--line-height);
      padding: 0;
    }

    .custom-delete-modal .ant-modal-confirm-btns {
      margin-top: var(--margin-small);
      display: flex;
      justify-content: flex-end;
      gap: var(--padding-small);
    }

    .custom-delete-modal .ant-btn {
      border-radius: var(--border-radius-small);
      font-family: var(--font-family);
      font-size: var(--font-size-body);
      transition: var(--transition-default);
      padding: var(--padding-small) var(--padding-medium);
      height: auto;
    }

    .custom-delete-modal .ant-btn-default {
      background-color: var(--background-light-gray);
      border-color: var(--hover-blue-gray);
      color: var(--text-dark);
    }

    .custom-delete-modal .ant-btn-default:hover {
      background-color: var(--hover-blue-gray);
      color: var(--primary-purple);
    }

    .custom-delete-modal .ant-btn-primary {
      background-color: var(--primary-purple);
      border-color: var(--primary-purple);
      color: var(--background-white);
    }

    .custom-delete-modal .ant-btn-primary:hover {
      background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple));
      border-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple));
    }

    /* Override span background for buttons */
    .custom-delete-modal .ant-modal-confirm-btns .ant-btn span {
      background-color: transparent !important;
    }

    /* Dark mode support */
    :host-context(.dark) .custom-delete-modal .ant-modal-content {
      background-color: #9B2C2C;
    }

    :host-context(.dark) .custom-delete-modal .ant-modal-title {
      color: var(--text-dark);
    }

    :host-context(.dark) .custom-delete-modal .ant-modal-body {
      color: var(--text-medium-gray);
    }

    :host-context(.dark) .custom-delete-modal .ant-btn-default {
      background-color: var(--background-light-gray);
      border-color: var(--hover-blue-gray);
      color: var(--text-dark);
    }

    :host-context(.dark) .custom-delete-modal .ant-btn-default:hover {
      background-color: var(--hover-blue-gray);
      color: var(--primary-purple);
    }

    :host-context(.dark) .custom-delete-modal .ant-btn-primary {
      background-color: var(--primary-purple);
      border-color: var(--primary-purple);
      color: var(--background-white);
    }

    :host-context(.dark) .custom-delete-modal .ant-btn-primary:hover {
      background-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple));
      border-color: color-mix(in srgb, var(--primary-purple) 90%, var(--secondary-purple));
    }

 @keyframes fadeIn {
   from {
     opacity: 0;
     transform: translateY(-10px);
   }

   to {
     opacity: 1;
     transform: translateY(0);
   }
 }

 @keyframes pulse {
   0% {
     transform: scale(1);
   }

   50% {
     transform: scale(1.05);
   }

   100% {
     transform: scale(1);
   }
 }

 @keyframes slideInRight {
   from {
     opacity: 0;
     transform: translateX(20px);
   }

   to {
     opacity: 1;
     transform: translateX(0);
   }
 }

 .animate-fadeIn {
   animation: fadeIn 0.5s ease-in-out;
 }

 .animate-pulse {
   animation: pulse 2s ease-in-out infinite;
 }

 /* Header styles */
 .sticky-header {
   position: sticky;
   top: 0;
   z-index: 10;
   backdrop-filter: blur(5px);
 }

 /* Table styles */
 .prompt-table {
   border-collapse: separate;
   border-spacing: 0;
 }

 .prompt-table th {
   position: sticky;
   top: 0;
   z-index: 10;
   font-weight: var(--font-weight-semibold);
   color: var(--text-dark);
   background-color: var(--hover-blue-gray);
   transition: background-color 0.3s ease;
 }

 .prompt-table tr {
   transition: all 0.2s ease;
 }

 .prompt-truncate {
   max-width: 300px;
   white-space: nowrap;
   overflow: hidden;
   text-overflow: ellipsis;
   transition: all 0.3s ease;
 }

 .prompt-truncate:hover {
   white-space: normal;
   overflow: visible;
   max-width: none;
   background-color: var(--background-light-gray);
   position: relative;
   z-index: 20;
   border-radius: var(--border-radius-small);
   box-shadow: var(--box-shadow);
   padding: 0.5rem;
 }

 /* Button styles */
 .action-button {
   opacity: 0.9;
   transition: all 0.2s ease;
   box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
 }

 .action-button:hover {
   opacity: 1;
   transform: translateY(-2px);
   box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
 }

 tr:hover .action-button {
   opacity: 1;
 }

 /* Dark mode button styles */
 :host-context(.dark) .action-button {
   box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
 }

 :host-context(.dark) .action-button:first-child {
   background-color: #2D3748;
 }

 :host-context(.dark) .action-button:first-child:hover {
   background-color: #4A5568;
 }

 :host-context(.dark) .action-button:last-child {
   background-color: #742A2A;
 }

 :host-context(.dark) .action-button:last-child:hover {
   background-color: #9B2C2C;
 }

 button {
   position: relative;
   overflow: hidden;
 }

 button::after {
   content: '';
   position: absolute;
   top: 50%;
   left: 50%;
   width: 5px;
   height: 5px;
   background: rgba(255, 255, 255, 0.5);
   opacity: 0;
   border-radius: 100%;
   transform: scale(1, 1) translate(-50%);
   transform-origin: 50% 50%;
 }

 button:focus:not(:active)::after {
   animation: ripple 1s ease-out;
 }

 @keyframes ripple {
   0% {
     transform: scale(0, 0);
     opacity: 0.5;
   }

   20% {
     transform: scale(25, 25);
     opacity: 0.3;
   }

   100% {
     opacity: 0;
     transform: scale(40, 40);
   }
 }

 /* Dark mode specific styles */
 :host-context(.dark) input,
 :host-context(.dark) select,
 :host-context(.dark) button {
   color: var(--text-dark);
   border-color: rgba(255, 255, 255, 0.1);
 }

 :host-context(.dark) .prompt-table th {
   background-color: var(--hover-blue-gray);
   color: var(--text-dark);
 }

 :host-context(.dark) .prompt-truncate:hover {
   background-color: var(--hover-blue-gray);
   color: var(--text-dark);
 }

 /* Dark mode pagination styles */
 :host-context(.dark) .pagination-container {
   background-color: var(--hover-blue-gray);
   border-color: rgba(255, 255, 255, 0.1);
 }

 :host-context(.dark) .pagination-container span,
 :host-context(.dark) .pagination-container button {
   color: var(--text-dark);
 }

 :host-context(.dark) .pagination-container button:hover:not(:disabled):not(.bg-\[var\(--primary-purple\)\]) {
   background-color: #4A5568;
 }

 :host-context(.dark) .pagination-container button:disabled {
   color: rgba(255, 255, 255, 0.3);
 }

 /* Responsive styles */
 @media (max-width: 768px) {
   .action-button {
     opacity: 1;
   }

   .prompt-truncate {
     max-width: 150px;
   }



 }

