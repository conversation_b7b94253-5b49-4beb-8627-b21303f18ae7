namespace ProjectApp.Core.Dtos
{
    public class DatabaseSchemaDto
    {
        public string DatabaseName { get; set; } = string.Empty;
        
        public List<DatabaseTableDto> Tables { get; set; } = new();
        
        public DateTime ExtractedAt { get; set; }
        
        public string ConnectionName { get; set; } = string.Empty;
    }
    
    public class DatabaseTableDto
    {
        public string TableName { get; set; } = string.Empty;
        
        public string SchemaName { get; set; } = string.Empty;
        
        public string? Description { get; set; }
        
        public List<DatabaseColumnDto> Columns { get; set; } = new();
        
        public List<DatabaseRelationshipDto> Relationships { get; set; } = new();
        
        public int RowCount { get; set; }
    }
    
    public class DatabaseColumnDto
    {
        public string ColumnName { get; set; } = string.Empty;
        
        public string DataType { get; set; } = string.Empty;
        
        public bool IsNullable { get; set; }
        
        public bool IsPrimaryKey { get; set; }
        
        public bool IsForeignKey { get; set; }
        
        public int? MaxLength { get; set; }
        
        public int? Precision { get; set; }
        
        public int? Scale { get; set; }
        
        public string? DefaultValue { get; set; }
        
        public string? Description { get; set; }
    }
    
    public class DatabaseRelationshipDto
    {
        public string ForeignKeyColumn { get; set; } = string.Empty;
        
        public string ReferencedTable { get; set; } = string.Empty;
        
        public string ReferencedColumn { get; set; } = string.Empty;
        
        public string RelationshipType { get; set; } = string.Empty;
    }
    
    public class DatabaseQueryDto
    {
        public string ConnectionName { get; set; } = string.Empty;
        
        public string Query { get; set; } = string.Empty;
        
        public int TimeoutSeconds { get; set; } = 30;
        
        public bool IsReadOnly { get; set; } = true;
    }
    
    public class DatabaseQueryResultDto
    {
        public bool Success { get; set; }
        
        public string Message { get; set; } = string.Empty;
        
        public List<Dictionary<string, object?>> Data { get; set; } = new();
        
        public List<string> ColumnNames { get; set; } = new();
        
        public int RowCount { get; set; }
        
        public double ExecutionTimeMs { get; set; }
        
        public string? Error { get; set; }
    }
}
