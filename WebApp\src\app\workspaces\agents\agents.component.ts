import { CommonModule } from '@angular/common';
import { Component, model } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { Router, RouterLink } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import {
  AgentDefinitionServiceProxy,
  AiServiceProxy,
  ModelDetailsServiceProxy,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AddOrEditAgentDialogComponent } from '../../dialogs/add-or-edit-agent-dialog/add-or-edit-agent-dialog.component';
import { RemoveProviderPrefixPipe } from "../../../shared/pipes/remove-provider-prefix.pipe";
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';
@Component({
  selector: 'app-agents',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzInputModule,
    NzIconModule,
    NzAutocompleteModule,
    NzIconModule,
    NzBreadCrumbModule,
    ServiceProxyModule,
    RouterLink,
    RemoveProviderPrefixPipe,
    SpinnerComponent
  ],
  templateUrl: './agents.component.html',
  styleUrl: './agents.component.css',
  providers: [NzModalService],
})
export class AgentsComponent {
  agents: any[] = [];
  filteredAgents: any[] = [];
  paginatedAgents: any[] = [];
  showForm: boolean = false;
  isEditing: boolean = false;
  isLoading: boolean = false; // Loading state for spinner
  currentAgent: any = {};
  modelSearchQuery: string = '';
  searchQuery: string = '';

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 6;
  totalPages: number = 1;
  previousPageSize: number = 6;

  // Make Math available to the template
  Math = Math;

  filteredModels: any[] = [];
  models: any = [];
  workspaceName: string = '';
  plugins: any[] = [];

  constructor(
    private modelDetailsService: ModelDetailsServiceProxy, // Inject your service here
    private agentService: AgentDefinitionServiceProxy,
    private router: Router,
    private modalService: NzModalService, // Inject the modal service
    private aiService: AiServiceProxy
  ) { }
  ngOnInit(): void {
    const routerSegments = this.router.url.split('/');

    // Check if the URL is for settings or workspace
    if (routerSegments.includes('settings') && routerSegments.includes('agents')) {
      // Load all agents for settings
      this.loadAllAgents();
    } else if (routerSegments.includes('chat') && routerSegments.includes('workspace')) {
      // Extract workspace name from the URL
      const workspaceIndex = routerSegments.indexOf('workspace');
      if (workspaceIndex !== -1 && routerSegments.length > workspaceIndex + 1) {
        this.workspaceName = decodeURIComponent(routerSegments[workspaceIndex + 1]);
        console.log('Workspace name:', this.workspaceName);

        // Load agents for the specific workspace
        this.loadAgents();
      }
    }

    // Load models and plugins (common for both contexts)
    this.loadModels();
    this.loadPlugins();
  }
  loadAgents() {
    // Fetch agents from the service and assign to local array
    this.isLoading = true; // Show spinner
    this.agentService
      .getAllByWorkspace(this.workspaceName)
      .subscribe({
        next: (agents: any) => {
          this.agents = agents;
          this.filteredAgents = [...this.agents];
          this.updatePagination();
          console.log(this.agents);
          this.isLoading = false; // Hide spinner
        },
        error: (error: any) => {
          console.error('Error loading agents:', error);
          this.isLoading = false; // Hide spinner on error
        }
      });
  }

  loadAllAgents() {
    // Fetch all agents from the service and assign to local array
    this.isLoading = true; // Show spinner
    this.agentService.getAll().subscribe({
      next: (agents: any) => {
        this.agents = agents;
        this.filteredAgents = [...this.agents];
        this.updatePagination();
        this.isLoading = false; // Hide spinner
      },
      error: (error: any) => {
        console.error('Error loading all agents:', error);
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  /**
   * Filter agents based on search query
   */
  filterAgents() {
    if (!this.searchQuery) {
      this.filteredAgents = [...this.agents];
    } else {
      const query = this.searchQuery.toLowerCase();
      this.filteredAgents = this.agents.filter(agent =>
        agent.agentName.toLowerCase().includes(query) ||
        agent.instructions.toLowerCase().includes(query) ||
        agent.modelName.toLowerCase().includes(query) ||
        agent.tools.some((tool: string) => tool.toLowerCase().includes(query))
      );
    }

    // Reset to first page when filtering
    this.currentPage = 1;
    this.updatePagination();
  }

  /**
   * Update pagination based on current page and page size
   */
  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages
    this.totalPages = Math.ceil(this.filteredAgents.length / this.pageSize);

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages || 1;

    // Get current page of agents
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, this.filteredAgents.length);
    this.paginatedAgents = this.filteredAgents.slice(startIndex, endIndex);

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredAgents.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedAgents.length
    });
  }

  /**
   * Go to a specific page
   */
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  /**
   * Go to the previous page
   */
  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  /**
   * Go to the next page
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  loadModels() {
    // Fetch models from the service and assign to filteredModels
    this.modelDetailsService.getAllActiveModel().subscribe((models: any) => {
      this.filteredModels = models;
      this.models = models;
    });
  }
  loadPlugins() {
    this.aiService.getPluginClassNames().subscribe((res: any) => {
      // this.plugins = res.message;
      // console.log(this.plugins);
      this.plugins = [...res.message];
    });
  }
  saveAgent() {
    this.currentAgent.Workspace = this.workspaceName;

    this.agentService.createOrUpdate(this.currentAgent).subscribe((res) => {
      this.loadAgents(); // refresh agents from backend
      console.log(res);
    });
    this.showForm = false;
  }



  deleteAgent(agent: any) {
    // take the whole agent instead of agentName
    this.agentService.delete(agent).subscribe((res) => {
      if (res) {
        this.loadAgents(); // refresh agents from backend
        console.log(res);
      }
    }); // Delete the agent using the service
  }

  resetForm() {
    this.currentAgent = {};
    this.showForm = false;
    this.isEditing = false;
  }

  onChange(event: any) {
    const query = event.target.value.toLowerCase();
    // Filter local dummy models by query
    this.filteredModels = this.models.filter((m: any) =>
      m.modelName.toLowerCase().includes(query)
    );
  }

  updateModel(modelName: string) {
    this.currentAgent.modelName = modelName;
  }

  cancel() {
    this.resetForm();
  }
}
