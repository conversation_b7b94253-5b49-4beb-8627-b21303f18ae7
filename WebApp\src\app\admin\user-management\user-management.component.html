<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-user-3-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">User Management</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredUsersGetter.length }}
        </div>
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input type="text" placeholder="Search users..." [(ngModel)]="searchTerm" (ngModelChange)="filterUsers()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)] border-none bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200" />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchTerm">
            <button (click)="searchTerm = ''; filterUsers()" class="text-[var(--text-medium-gray)] bg-transparent border-none
               hover:text-[var(--text-dark)] transition-colors focus:outline-none">
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Role Filter - Teams-style -->
        <div class="relative w-auto flex items-center">
          <select [(ngModel)]="selectedRole" (ngModelChange)="filterUsers()"
            class="appearance-none w-auto h-8 px-3 py-1 pr-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200 cursor-pointer">
            <option value="">All Roles</option>
            <option value="User">User</option>
            <option value="Admin">Admin</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
            <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
        </div>

        <!-- Add User Button - Teams-style -->
        <button (click)="showAddUserDialog = true"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] border-none text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1">
          <i class="ri-add-line"></i>
          <span>Add User</span>
        </button>
      </div>
    </div>

    <!-- Users Table -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="relative min-h-[300px]">
        <app-spinner message="Loading users..." [overlay]="false"></app-spinner>
      </div>

      <div *ngIf="!isLoading"
        class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30">
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-[var(--text-dark)] user-table">
            <thead>
              <tr class="bg-[var(--hover-blue-gray)] border-b border-[var(--hover-blue-gray)]">
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Name</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Email</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Role</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left font-semibold">
                  <div class="flex items-center">
                    <span>Skills</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center font-semibold">
                  <div class="flex items-center justify-center">
                    <span>Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let user of paginatedUsers; let i = index"
                class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] transition-all duration-200 animate-fadeIn"
                [ngStyle]="{'animation-delay': (i * 0.05) + 's'}">
                <td class="px-4 py-3">
                  <div class="flex items-center gap-3">
                    <span class="font-medium">{{ user.name }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{ user.email }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex flex-wrap gap-2">
                    <span *ngFor="let role of user.roles"
                      class="inline-flex items-center justify-between px-2.5 py-1 rounded-full text-xs font-medium group"
                      [ngClass]="role === 'Admin' ? 'bg-[var(--primary-purple)] text-white' : 'bg-[var(--secondary-purple)] text-[var(--text-dark)]'">
                      {{ role }}
                      <button (click)="removeRole(user, role)"
                        class="ml-1.5 bg-transparent transition opacity-0 group-hover:opacity-100 duration-300 outline-none border-none cursor-pointer">
                        <i class="ri-close-line"></i>
                      </button>
                    </span>
                    <button *ngIf="(user.roles?.length || 0) < 2" nz-dropdown nzTrigger="click" [nzDropdownMenu]="menu"
                      class="inline-flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)] hover:bg-[var(--secondary-purple)] transition-all duration-200 border-none">
                      <i class="ri-add-line"></i>
                    </button>
                    <nz-dropdown-menu #menu="nzDropdownMenu">
                      <ul nz-menu
                        class="bg-[var(--background-white)] text-[var(--text-dark)] shadow-lg rounded-md border border-[var(--hover-blue-gray)]">
                        <li nz-menu-item *ngIf="user.roles?.includes('User')" (click)="assignRole(user, 'Admin')"
                          class="hover:bg-[var(--hover-blue-gray)] transition-all duration-200 px-4 py-2 cursor-pointer">
                          Admin
                        </li>
                        <li nz-menu-item *ngIf="user.roles?.includes('Admin')" (click)="assignRole(user, 'User')"
                          class="hover:bg-[var(--hover-blue-gray)] transition-all duration-200 px-4 py-2 cursor-pointer">
                          User
                        </li>
                      </ul>
                    </nz-dropdown-menu>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{ user.skills || 'N/A' }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center justify-center space-x-2">
                    <button (click)="deleteUser(user)"
                      class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none"
                      title="Delete User">
                      <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoading && filteredUsersGetter.length === 0"
          class="flex flex-col items-center justify-center py-16 px-4">
          <div
            class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4 border border-[var(--primary-purple)] border-opacity-20">
            <i class="ri-user-3-line text-3xl text-[var(--text-medium-gray)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No users found</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            No users match your current search criteria. Try adjusting your search or filter settings.
          </p>
          <button (click)="searchTerm = ''; selectedRole = ''; filterUsers()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] border-none rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 flex items-center gap-2">
            <i class="ri-refresh-line"></i>
            <span>Reset Filters</span>
          </button>
        </div>
      </div>

      <!-- Add User Dialog -->
      <div *ngIf="showAddUserDialog"
        class="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 animate-fadeIn">
        <div
          class="bg-[var(--background-white)] rounded-md p-6 w-full max-w-md border border-[var(--hover-blue-gray)] shadow-md">
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center gap-3">
              <div
                class="w-10 h-10 rounded-full bg-[var(--primary-purple)] bg-opacity-10 flex items-center justify-center border border-[var(--primary-purple)] border-opacity-20">
                <i class="ri-user-add-line text-white text-xl"></i>
              </div>
              <h3 class="text-lg font-semibold text-[var(--text-dark)]">Add New User</h3>
            </div>
            <button (click)="showAddUserDialog = false; resetFields()"
              class="w-8 h-8 flex items-center justify-center rounded-full hover:bg-[var(--hover-blue-gray)] transition-all duration-200 border-none bg-transparent">
              <i class="ri-close-line text-[var(--text-medium-gray)] text-xl"></i>
            </button>
          </div>

          <form class="space-y-4">
            <!-- Name -->
            <div>
              <label for="name" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">Name</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <i class="ri-user-line text-[var(--text-medium-gray)]"></i>
                </div>
                <input id="name" name="name" [(ngModel)]="newUser.name"
                  class="w-full h-10 pl-10 pr-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                  placeholder="Enter user name" required />
              </div>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">Email</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <i class="ri-mail-line text-[var(--text-medium-gray)]"></i>
                </div>
                <input id="email" name="email" type="email" [(ngModel)]="newUser.email"
                  class="w-full h-10 pl-10 pr-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                  placeholder="Enter email address" required />
              </div>
            </div>

            <!-- Role -->
            <div>
              <label for="role" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">Role</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <i class="ri-shield-user-line text-[var(--text-medium-gray)]"></i>
                </div>
                <select id="role" name="role" [(ngModel)]="newUser.role"
                  class="appearance-none w-full h-10 pl-10 pr-10 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                  required>
                  <option value="" disabled>Select Role</option>
                  <option value="User">User</option>
                  <option value="Admin">Admin</option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
                </div>
              </div>
            </div>

            <!-- Skills -->
            <div>
              <label for="skills" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">Skills</label>
              <div class="relative">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <i class="ri-tools-line text-[var(--text-medium-gray)]"></i>
                </div>
                <input id="skills" name="skills" [(ngModel)]="newUser.skills"
                  class="w-full h-10 pl-10 pr-4 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                  placeholder="Enter user skills (optional)" />
              </div>
            </div>

            <!-- Password -->
            <div>
              <label for="password" class="text-sm font-medium text-[var(--text-dark)] mb-1 block">Password</label>
              <div class="flex gap-2">
                <div class="relative flex-1">
                  <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <i class="ri-lock-line text-[var(--text-medium-gray)]"></i>
                  </div>
                  <input id="password" name="password" [type]="showPassword ? 'text' : 'password'"
                    [(ngModel)]="newUser.password"
                    class="w-full h-10 pl-10 pr-10 py-2 text-sm font-normal text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200"
                    placeholder="Enter password" required />
                  <button type="button" (click)="showPassword = !showPassword"
                    class="absolute inset-y-0 right-0 flex items-center border-none bg-transparent pr-3 text-[var(--text-medium-gray)] hover:text-[var(--text-dark)] transition-colors focus:outline-none">
                    <i [ngClass]="showPassword ? 'ri-eye-off-line' : 'ri-eye-line'"></i>
                  </button>
                </div>
                <button type="button" (click)="generatePassword()"
                  class="w-10 h-10 flex items-center justify-center rounded-md bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-all duration-200 border-none"
                  title="Generate Password">
                  <i class="ri-key-line text-[var(--text-dark)]"></i>
                </button>
              </div>
            </div>

            <!-- Buttons -->
            <div class="flex justify-end gap-3 mt-6 pt-4 border-t border-[var(--hover-blue-gray)]">
              <button type="button" (click)="showAddUserDialog = false; resetFields()"
                class="h-10 px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] text-sm font-medium rounded-md hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center gap-2 border-none">
                <i class="ri-close-line"></i>
                <span>Cancel</span>
              </button>

              <button type="submit" (click)="addUser()"
                class="h-10 px-4 py-2 bg-[var(--primary-purple)] text-white text-sm font-medium rounded-md hover:bg-opacity-90 transition-all duration-300 flex items-center justify-center gap-2 border-none">
                <i class="ri-save-line"></i>
                <span>Add User</span>
              </button>
            </div>
          </form>
        </div>
      </div>
      <!-- Pagination -->
      <div
        class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-4 mb-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
        *ngIf="!isLoading && filteredUsersGetter.length > 0">
        <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
          <ng-container *ngIf="filteredUsersGetter.length > 0">
            <span>Showing</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
            <span>to</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) +
              paginatedUsers.length
              }}</span>
            <span>of</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredUsersGetter.length }}</span>
            <span>users</span>
          </ng-container>
          <ng-container *ngIf="filteredUsersGetter.length === 0">
            <span>No users to display</span>
          </ng-container>
        </div>

        <div class="flex items-center">
          <div class="hidden sm:flex items-center mr-6 space-x-2">
            <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
            <div class="relative">
              <select [(ngModel)]="pageSize" (ngModelChange)="updatePagination()"
                class="appearance-none h-7 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md text-sm px-2 pr-7 py-0 text-center focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)]">
                <option [ngValue]="5" class="text-center">5</option>
                <option [ngValue]="10" class="text-center">10</option>
                <option [ngValue]="20" class="text-center">20</option>
                <option [ngValue]="50" class="text-center">50</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
                <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
            <button (click)="goToPage(1)" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="First page">
              <i class="ri-skip-back-mini-line text-sm"></i>
            </button>

            <button (click)="previousPage()" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Previous page">
              <i class="ri-arrow-left-s-line text-sm"></i>
            </button>

            <div class="flex items-center space-x-1">
              <button *ngIf="currentPage > 2 && totalPages > 3" (click)="goToPage(1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                1
              </button>

              <span *ngIf="currentPage > 3 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage > 1 && totalPages > 1" (click)="goToPage(currentPage - 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage - 1 }}
              </button>

              <button
                class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
                {{ currentPage }}
              </button>

              <button *ngIf="currentPage < totalPages && totalPages > 1" (click)="goToPage(currentPage + 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage + 1 }}
              </button>

              <span *ngIf="currentPage < totalPages - 2 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage < totalPages - 1 && totalPages > 3" (click)="goToPage(totalPages)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ totalPages }}
              </button>
            </div>

            <button (click)="nextPage()" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Next page">
              <i class="ri-arrow-right-s-line text-sm"></i>
            </button>

            <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Last page">
              <i class="ri-skip-forward-mini-line text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>
