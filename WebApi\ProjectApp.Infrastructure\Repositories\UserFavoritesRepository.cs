using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class UserFavoritesRepository : IUserFavoritesRepository
    {
        private readonly IDbConnection _dbConnection;

        public UserFavoritesRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<ResponseMessage> ToggleFavorite(string userEmail, string itemKey, string itemType)
        {
            // Check if the favorite already exists
            var existsQuery = "SELECT COUNT(*) FROM UserFavorites WHERE UserEmail = @UserEmail AND ItemKey = @ItemKey AND ItemType = @ItemType";
            var count = await _dbConnection.ExecuteScalarAsync<int>(existsQuery, new { UserEmail = userEmail, ItemKey = itemKey, ItemType = itemType });

            if (count > 0)
            {
                // Remove favorite
                var deleteQuery = "DELETE FROM UserFavorites WHERE UserEmail = @UserEmail AND ItemKey = @ItemKey AND ItemType = @ItemType";
                await _dbConnection.ExecuteAsync(deleteQuery, new { UserEmail = userEmail, ItemKey = itemKey, ItemType = itemType });
                return new ResponseMessage { IsError = false, Message = "Favorite removed." };
            }
            else
            {
                // Add favorite
                var insertQuery = "INSERT INTO UserFavorites (UserEmail, ItemKey, ItemType) VALUES (@UserEmail, @ItemKey, @ItemType)";
                await _dbConnection.ExecuteAsync(insertQuery, new { UserEmail = userEmail, ItemKey = itemKey, ItemType = itemType });
                return new ResponseMessage { IsError = false, Message = "Favorite added." };
            }
        }

        public async Task<List<UserFavoriteDto>> GetFavorites(string userEmail)
        {
            var query = @"
                SELECT uf.ItemKey, uf.ItemType
                FROM UserFavorites uf
                WHERE uf.UserEmail = @UserEmail";
            
            return (await _dbConnection.QueryAsync<UserFavoriteDto>(query, new { UserEmail = userEmail })).AsList();
        }

        public async Task<ResponseMessage> IsFavorite(string userEmail, string itemKey, string itemType)
        {
            var query = "SELECT COUNT(*) FROM UserFavorites WHERE UserEmail = @UserEmail AND ItemKey = @ItemKey AND ItemType = @ItemType";
            var count = await _dbConnection.ExecuteScalarAsync<int>(query, new { UserEmail = userEmail, ItemKey = itemKey, ItemType = itemType });
            return new ResponseMessage { IsError = false, Message = count > 0 ? "Is favorite" : "Is not favorite" };
        }
    }
}
