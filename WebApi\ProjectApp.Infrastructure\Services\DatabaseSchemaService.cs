using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.KernelMemory;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.Data;
using System.Text.Json;

namespace ProjectApp.Infrastructure.Services
{
    public class DatabaseSchemaService
    {
        private readonly IDatabaseConnectionRepository _connectionRepository;
        private readonly IKernelMemory _kernelMemory;
        private readonly ILogger<DatabaseSchemaService> _logger;

        public DatabaseSchemaService(
            IDatabaseConnectionRepository connectionRepository,
            IKernelMemory kernelMemory,
            ILogger<DatabaseSchemaService> logger)
        {
            _connectionRepository = connectionRepository;
            _kernelMemory = kernelMemory;
            _logger = logger;
        }

        public async Task<SchemaExtractionResponseDto> ExtractAndStoreSchemaAsync(Guid connectionId, bool forceRefresh = false)
        {
            var response = new SchemaExtractionResponseDto
            {
                ExtractionTime = DateTime.UtcNow
            };

            try
            {
                _logger.LogInformation("Starting schema extraction for connection {ConnectionId}", connectionId);

                // Get connection details
                var connection = await _connectionRepository.GetByIdAsync(connectionId);
                if (connection == null)
                {
                    response.Success = false;
                    response.Message = "Database connection not found";
                    return response;
                }

                // Update status to in progress
                await _connectionRepository.UpdateSchemaExtractionStatusAsync(connectionId, "InProgress");

                // Extract schema from database
                var tables = await ExtractSchemaFromDatabaseAsync(connection);

                response.TablesExtracted = tables.Count;
                response.ColumnsExtracted = tables.Sum(t => t.Columns.Count);

                // Store schema in Kernel Memory
                await StoreSchemaInMemoryAsync(connection.Name, tables);

                // Update status to success
                await _connectionRepository.UpdateSchemaExtractionStatusAsync(connectionId, "Success");

                response.Success = true;
                response.Message = $"Successfully extracted schema for {tables.Count} tables";

                _logger.LogInformation("Schema extraction completed for connection {ConnectionId}. Tables: {TableCount}, Columns: {ColumnCount}",
                    connectionId, response.TablesExtracted, response.ColumnsExtracted);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting schema for connection {ConnectionId}", connectionId);

                await _connectionRepository.UpdateSchemaExtractionStatusAsync(connectionId, "Failed", ex.Message);

                response.Success = false;
                response.Message = $"Schema extraction failed: {ex.Message}";
                response.Errors.Add(ex.Message);
            }

            return response;
        }

        public async Task<string> GetSchemaContextAsync(string connectionName)
        {
            try
            {
                _logger.LogInformation("Retrieving schema context for connection {ConnectionName}", connectionName);

                // Search for schema documents in Kernel Memory
                var searchResults = await _kernelMemory.SearchAsync(
                    query: $"database schema tables columns {connectionName}",
                    index: "DatabaseSchema",
                    filter: new MemoryFilter().ByTag("ConnectionName", connectionName),
                    limit: 50,
                    minRelevance: 0.1
                );

                if (!searchResults.Results.Any())
                {
                    _logger.LogWarning("No schema found for connection {ConnectionName}", connectionName);
                    return "No database schema available for this connection.";
                }

                var schemaContext = "Database Schema Information:\n\n";

                foreach (var result in searchResults.Results)
                {
                    schemaContext += result.Partitions.First().Text + "\n\n";
                }

                return schemaContext;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving schema context for connection {ConnectionName}", connectionName);
                return "Error retrieving database schema information.";
            }
        }

        private async Task<List<DatabaseTable>> ExtractSchemaFromDatabaseAsync(DatabaseConnectionDto connection)
        {
            var tables = new List<DatabaseTable>();

            using var dbConnection = new SqlConnection(connection.ConnectionString);
            await dbConnection.OpenAsync();

            // Get all tables
            var tablesSql = GetTablesQuery(connection.DatabaseType);
            var tableResults = await dbConnection.QueryAsync<dynamic>(tablesSql);

            foreach (var tableRow in tableResults)
            {
                var table = new DatabaseTable
                {
                    TableName = tableRow.TABLE_NAME,
                    SchemaName = tableRow.TABLE_SCHEMA ?? "dbo"
                };

                // Get columns for this table
                var columnsSql = GetColumnsQuery(connection.DatabaseType);
                var columnResults = await dbConnection.QueryAsync<dynamic>(columnsSql, new
                {
                    TableName = table.TableName,
                    SchemaName = table.SchemaName
                });

                foreach (var columnRow in columnResults)
                {
                    var column = new DatabaseColumn
                    {
                        ColumnName = columnRow.COLUMN_NAME,
                        DataType = columnRow.DATA_TYPE,
                        IsNullable = columnRow.IS_NULLABLE == "YES",
                        MaxLength = columnRow.CHARACTER_MAXIMUM_LENGTH,
                        Precision = columnRow.NUMERIC_PRECISION,
                        Scale = columnRow.NUMERIC_SCALE,
                        DefaultValue = columnRow.COLUMN_DEFAULT
                    };

                    table.Columns.Add(column);
                }

                // Get primary keys
                var pkSql = GetPrimaryKeysQuery(connection.DatabaseType);
                var pkResults = await dbConnection.QueryAsync<string>(pkSql, new
                {
                    TableName = table.TableName,
                    SchemaName = table.SchemaName
                });

                foreach (var pkColumn in pkResults)
                {
                    var column = table.Columns.FirstOrDefault(c => c.ColumnName == pkColumn);
                    if (column != null)
                        column.IsPrimaryKey = true;
                }

                // Get foreign keys
                var fkSql = GetForeignKeysQuery(connection.DatabaseType);
                var fkResults = await dbConnection.QueryAsync<dynamic>(fkSql, new
                {
                    TableName = table.TableName,
                    SchemaName = table.SchemaName
                });

                foreach (var fkRow in fkResults)
                {
                    var column = table.Columns.FirstOrDefault(c => c.ColumnName == fkRow.COLUMN_NAME);
                    if (column != null)
                        column.IsForeignKey = true;

                    table.Relationships.Add(new DatabaseRelationship
                    {
                        ForeignKeyColumn = fkRow.COLUMN_NAME,
                        ReferencedTable = fkRow.REFERENCED_TABLE_NAME,
                        ReferencedColumn = fkRow.REFERENCED_COLUMN_NAME,
                        RelationshipType = "ManyToOne"
                    });
                }

                tables.Add(table);
            }

            return tables;
        }

        private async Task StoreSchemaInMemoryAsync(string connectionName, List<DatabaseTable> tables)
        {
            foreach (var table in tables)
            {
                var schemaDocument = CreateSchemaDocument(connectionName, table);
                var documentId = $"schema_{connectionName}_{table.SchemaName}_{table.TableName}";

                await _kernelMemory.ImportTextAsync(
                    text: schemaDocument,
                    documentId: documentId,
                    index: "DatabaseSchema",
                    tags: new TagCollection
                    {
                        { "ConnectionName", connectionName },
                        { "TableName", table.TableName },
                        { "SchemaName", table.SchemaName },
                        { "DocumentType", "DatabaseSchema" }
                    }
                );
            }
        }

        private static string CreateSchemaDocument(string connectionName, DatabaseTable table)
        {
            var doc = $"Database Connection: {connectionName}\n";
            doc += $"Table: {table.SchemaName}.{table.TableName}\n\n";
            doc += "Columns:\n";

            foreach (var column in table.Columns)
            {
                doc += $"- {column.ColumnName} ({column.DataType}";
                if (column.MaxLength.HasValue)
                    doc += $"({column.MaxLength})";
                if (column.IsPrimaryKey)
                    doc += ", PRIMARY KEY";
                if (column.IsForeignKey)
                    doc += ", FOREIGN KEY";
                if (!column.IsNullable)
                    doc += ", NOT NULL";
                doc += ")\n";
            }

            if (table.Relationships.Any())
            {
                doc += "\nRelationships:\n";
                foreach (var rel in table.Relationships)
                {
                    doc += $"- {rel.ForeignKeyColumn} references {rel.ReferencedTable}.{rel.ReferencedColumn}\n";
                }
            }

            return doc;
        }

        private static IDbConnection CreateDatabaseConnection(string connectionString, string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => new SqlConnection(connectionString),
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }

        private static string GetTablesQuery(string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => @"
                    SELECT TABLE_NAME, TABLE_SCHEMA 
                    FROM INFORMATION_SCHEMA.TABLES 
                    WHERE TABLE_TYPE = 'BASE TABLE'
                    ORDER BY TABLE_SCHEMA, TABLE_NAME",
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }

        private static string GetColumnsQuery(string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => @"
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH, 
                           NUMERIC_PRECISION, NUMERIC_SCALE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = @TableName AND TABLE_SCHEMA = @SchemaName
                    ORDER BY ORDINAL_POSITION",
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }

        private static string GetPrimaryKeysQuery(string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => @"
                    SELECT COLUMN_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE OBJECTPROPERTY(OBJECT_ID(CONSTRAINT_SCHEMA + '.' + CONSTRAINT_NAME), 'IsPrimaryKey') = 1
                    AND TABLE_NAME = @TableName AND TABLE_SCHEMA = @SchemaName",
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }

        private static string GetForeignKeysQuery(string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => @"
                    SELECT 
                        kcu.COLUMN_NAME,
                        kcu2.TABLE_NAME AS REFERENCED_TABLE_NAME,
                        kcu2.COLUMN_NAME AS REFERENCED_COLUMN_NAME
                    FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu ON rc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
                    JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu2 ON rc.UNIQUE_CONSTRAINT_NAME = kcu2.CONSTRAINT_NAME
                    WHERE kcu.TABLE_NAME = @TableName AND kcu.TABLE_SCHEMA = @SchemaName",
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }
    }
}
