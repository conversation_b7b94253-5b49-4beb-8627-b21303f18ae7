import { Injectable } from '@angular/core';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, delay } from 'rxjs/operators';
import { DateTime } from 'luxon';
import {
  AgentPerformance,
  AgentComparison,
  AgentAnalyticsSummary,
  AnalyticsFilter,
  MetricComparison,
  MetricType,
  ChartData,
  PerformanceInsight,
  FailedTask,
  TaskStep,
  QualityMetrics,
  PerformanceMetrics,
  ActivityPeriod,
  TaskPerformance,
  TaskComparisonResult
} from '../models/agent-performance.models';

@Injectable({
  providedIn: 'root'
})
export class AgentAnalyticsService {
  private analyticsDataSubject = new BehaviorSubject<AgentPerformance[]>([]);
  public analyticsData$ = this.analyticsDataSubject.asObservable();

  constructor() {
    // Initialize with demo data
    this.initializeDemoData();
  }

  /**
   * Get all agent performance data
   */
  getAllAgentPerformance(filter?: AnalyticsFilter): Observable<AgentPerformance[]> {
    let data = this.analyticsDataSubject.value;

    if (filter) {
      data = this.applyFilter(data, filter);
    }

    return of(data).pipe(delay(500)); // Simulate API delay
  }

  /**
   * Get specific agent performance by ID
   */
  getAgentPerformance(agentId: string): Observable<AgentPerformance | null> {
    const agent = this.analyticsDataSubject.value.find(a => a.agentId === agentId);
    return of(agent || null).pipe(delay(300));
  }

  /**
   * Compare two agents
   */
  compareAgents(agentId1: string, agentId2: string): Observable<AgentComparison | null> {
    const agents = this.analyticsDataSubject.value;
    const agent1 = agents.find(a => a.agentId === agentId1);
    const agent2 = agents.find(a => a.agentId === agentId2);

    if (!agent1 || !agent2) {
      return of(null);
    }

    const comparison: AgentComparison = {
      agent1,
      agent2,
      comparisonMetrics: {
        taskEfficiency: this.calculateEfficiencyDifference(agent1, agent2),
        qualityDifference: this.calculateQualityDifference(agent1, agent2),
        reliabilityDifference: this.calculateReliabilityDifference(agent1, agent2),
        speedDifference: this.calculateSpeedDifference(agent1, agent2)
      },
      recommendations: this.generateRecommendations(agent1, agent2)
    };

    // Add task-specific comparison if agents work on similar tasks
    if (this.doAgentsWorkOnSimilarTasks(agent1, agent2)) {
      comparison.taskComparison = this.generateTaskComparison(agent1, agent2);
    }

    return of(comparison).pipe(delay(400));
  }

  /**
   * Get task-based performance comparisons
   */
  getTaskPerformanceComparisons(): Observable<TaskPerformance[]> {
    const agents = this.analyticsDataSubject.value;
    const taskComparisons: TaskPerformance[] = [];

    // Simple direct matching approach
    const humanAgents = agents.filter(a => a.agentType === 'Human');
    const aiAgents = agents.filter(a => a.agentType === 'AI');

    // Find content creation comparison
    const humanContentAgent = humanAgents.find(a =>
      a.workArea.toLowerCase().includes('blog') ||
      a.workArea.toLowerCase().includes('content')
    );
    const aiContentAgent = aiAgents.find(a =>
      a.workArea.toLowerCase().includes('blog') ||
      a.workArea.toLowerCase().includes('content')
    );

    if (humanContentAgent && aiContentAgent) {
      taskComparisons.push({
        taskType: 'content-creation',
        taskName: 'Content Creation',
        description: 'Creating blog posts, articles, and written content',
        humanAgent: humanContentAgent,
        aiAgent: aiContentAgent,
        comparisonResult: this.generateTaskComparison(humanContentAgent, aiContentAgent)
      });
    }

    // Find SQL query comparison
    const humanSqlAgent = humanAgents.find(a =>
      a.workArea.toLowerCase().includes('sql') ||
      a.workArea.toLowerCase().includes('query')
    );
    const aiSqlAgent = aiAgents.find(a =>
      a.workArea.toLowerCase().includes('sql') ||
      a.workArea.toLowerCase().includes('query')
    );

    if (humanSqlAgent && aiSqlAgent) {
      taskComparisons.push({
        taskType: 'sql-generation',
        taskName: 'SQL Query Generation',
        description: 'Generating and optimizing SQL queries',
        humanAgent: humanSqlAgent,
        aiAgent: aiSqlAgent,
        comparisonResult: this.generateTaskComparison(humanSqlAgent, aiSqlAgent)
      });
    }

    return of(taskComparisons).pipe(delay(300));
  }

  /**
   * Compare human vs AI performance for specific task type
   */
  compareHumanVsAIForTask(taskType: string): Observable<TaskComparisonResult | null> {
    const agents = this.analyticsDataSubject.value;
    const humanAgent = agents.find(a => a.agentType === 'Human' && this.getTaskTypeFromWorkArea(a.workArea) === taskType);
    const aiAgent = agents.find(a => a.agentType === 'AI' && this.getTaskTypeFromWorkArea(a.workArea) === taskType);

    if (!humanAgent || !aiAgent) {
      return of(null);
    }

    const comparison = this.generateTaskComparison(humanAgent, aiAgent);
    return of(comparison).pipe(delay(200));
  }

  /**
   * Get analytics summary for dashboard
   */
  getAnalyticsSummary(): Observable<AgentAnalyticsSummary> {
    const agents = this.analyticsDataSubject.value;
    const activeAgents = agents.filter(a => a.status === 'active');

    const summary: AgentAnalyticsSummary = {
      totalAgents: agents.length,
      activeAgents: activeAgents.length,
      averagePerformance: this.calculateAveragePerformance(agents),
      topPerformers: this.getTopPerformers(agents, 3),
      underPerformers: this.getUnderPerformers(agents, 3),
      recentComparisons: [] // Would be populated from recent comparison history
    };

    return of(summary).pipe(delay(300));
  }

  /**
   * Get chart data for performance visualization
   */
  getPerformanceChartData(agentIds: string[]): Observable<ChartData> {
    const agents = this.analyticsDataSubject.value.filter(a => agentIds.includes(a.agentId));

    const chartData: ChartData = {
      labels: agents.map(a => a.agentName),
      datasets: [
        {
          label: 'Success Rate (%)',
          data: agents.map(a => a.metrics.successRate),
          backgroundColor: ['#10A37F', '#6B46C1', '#F59E0B', '#EF4444'],
          borderColor: ['#10A37F', '#6B46C1', '#F59E0B', '#EF4444'],
          borderWidth: 2
        },
        {
          label: 'Average Time (seconds)',
          data: agents.map(a => a.metrics.averageTime),
          backgroundColor: ['rgba(16, 163, 127, 0.5)', 'rgba(107, 70, 193, 0.5)', 'rgba(245, 158, 11, 0.5)', 'rgba(239, 68, 68, 0.5)'],
          borderColor: ['#10A37F', '#6B46C1', '#F59E0B', '#EF4444'],
          borderWidth: 2
        }
      ]
    };

    return of(chartData).pipe(delay(200));
  }

  /**
   * Initialize demo data
   */
  private initializeDemoData(): void {
    const demoData: AgentPerformance[] = [
      {
        agentId: 'blog-gen-ai-01',
        agentName: 'BlogGenAI-01',
        agentType: 'AI',
        workArea: 'Generating blog content automatically',
        reportDuration: {
          startDate: DateTime.fromISO('2025-07-01'),
          endDate: DateTime.fromISO('2025-07-18')
        },
        metrics: {
          totalTasks: 125,
          successRate: 96,
          averageTime: 18.4,
          failedAttempts: 5,
          failureRate: 4
        },
        taskSteps: [
          { stepName: 'Finding Keywords', averageTime: 1.2 },
          { stepName: 'Creating Outline', averageTime: 2.4 },
          { stepName: 'Writing Introduction', averageTime: 3.1 },
          { stepName: 'Writing Main Body', averageTime: 7.6 },
          { stepName: 'Writing Conclusion', averageTime: 2.3 },
          { stepName: 'SEO Formatting', averageTime: 1.5 },
          { stepName: 'Grammar Check', averageTime: 0.8 }
        ],
        quality: {
          grammarQuality: 96.2,
          readabilityScore: 67.4,
          copiedContent: 6.8,
          overallQuality: 89.3
        },
        failedTasks: [
          {
            taskId: 'B-002',
            reason: 'Took too long (timeout)',
            timestamp: DateTime.fromISO('2025-07-02T10:30:00')
          },
          {
            taskId: 'B-048',
            reason: 'Error in SEO formatting',
            timestamp: DateTime.fromISO('2025-07-08T14:15:00')
          },
          {
            taskId: 'B-066',
            reason: 'Input was incorrect',
            timestamp: DateTime.fromISO('2025-07-12T09:45:00')
          }
        ],
        insights: [
          {
            type: 'positive',
            title: 'High Success Rate',
            description: 'Maintaining 96% success rate consistently'
          },
          {
            type: 'negative',
            title: 'Slow Body Writing',
            description: 'Writing main body takes 7.6 seconds on average',
            suggestion: 'Optimize content generation algorithm'
          }
        ],
        activityPeriods: [
          { period: '11 AM - 2 PM', taskCount: 45, averageTime: 16.2, successRate: 98 },
          { period: '1 AM - 3 AM', taskCount: 8, averageTime: 24.1, successRate: 87 }
        ],
        lastUpdated: DateTime.now(),
        status: 'active'
      },
      // Add a human agent for comparison - same task type as AI
      {
        agentId: 'human-writer-01',
        agentName: 'Sarah-ContentWriter',
        agentType: 'Human',
        workArea: 'Blog content creation and editing (same as AI)',
        reportDuration: {
          startDate: DateTime.fromISO('2025-07-01'),
          endDate: DateTime.fromISO('2025-07-18')
        },
        metrics: {
          totalTasks: 45,
          successRate: 98,
          averageTime: 3600, // 1 hour in seconds
          failedAttempts: 1,
          failureRate: 2
        },
        taskSteps: [
          { stepName: 'Research & Planning', averageTime: 900 },
          { stepName: 'Writing Draft', averageTime: 1800 },
          { stepName: 'Editing & Review', averageTime: 600 },
          { stepName: 'SEO Optimization', averageTime: 300 }
        ],
        quality: {
          grammarQuality: 99.1,
          readabilityScore: 78.2,
          copiedContent: 0.5,
          overallQuality: 95.8
        },
        failedTasks: [
          {
            taskId: 'H-023',
            reason: 'Missed deadline due to illness',
            timestamp: DateTime.fromISO('2025-07-10T16:00:00')
          }
        ],
        insights: [
          {
            type: 'positive',
            title: 'Excellent Quality',
            description: 'Consistently produces high-quality content with minimal errors'
          },
          {
            type: 'negative',
            title: 'Slower Processing',
            description: 'Takes significantly longer per task compared to AI agents',
            suggestion: 'Consider AI assistance for initial drafts'
          }
        ],
        activityPeriods: [
          { period: '9 AM - 12 PM', taskCount: 20, averageTime: 3200, successRate: 100 },
          { period: '2 PM - 5 PM', taskCount: 25, averageTime: 3800, successRate: 96 }
        ],
        lastUpdated: DateTime.now(),
        status: 'active'
      },
      // Add another AI agent for better comparison
      {
        agentId: 'sql-query-ai-01',
        agentName: 'SqlQueryAgent-01',
        agentType: 'AI',
        workArea: 'Automated SQL query generation and optimization',
        reportDuration: {
          startDate: DateTime.fromISO('2025-07-01'),
          endDate: DateTime.fromISO('2025-07-18')
        },
        metrics: {
          totalTasks: 340,
          successRate: 92,
          averageTime: 2.8,
          failedAttempts: 27,
          failureRate: 8
        },
        taskSteps: [
          { stepName: 'Query Analysis', averageTime: 0.5 },
          { stepName: 'Schema Validation', averageTime: 0.8 },
          { stepName: 'Query Generation', averageTime: 1.2 },
          { stepName: 'Optimization', averageTime: 0.3 }
        ],
        quality: {
          grammarQuality: 100,
          readabilityScore: 85.6,
          copiedContent: 0,
          overallQuality: 94.2
        },
        failedTasks: [
          {
            taskId: 'SQL-045',
            reason: 'Complex join operation timeout',
            timestamp: DateTime.fromISO('2025-07-05T11:30:00')
          },
          {
            taskId: 'SQL-156',
            reason: 'Invalid schema reference',
            timestamp: DateTime.fromISO('2025-07-12T14:20:00')
          }
        ],
        insights: [
          {
            type: 'positive',
            title: 'High Throughput',
            description: 'Processes large volumes of queries efficiently'
          },
          {
            type: 'negative',
            title: 'Complex Query Challenges',
            description: 'Struggles with very complex multi-table joins',
            suggestion: 'Improve training data for complex scenarios'
          }
        ],
        activityPeriods: [
          { period: '24/7 Active', taskCount: 340, averageTime: 2.8, successRate: 92 }
        ],
        lastUpdated: DateTime.now(),
        status: 'active'
      },
      // Add human SQL developer for comparison
      {
        agentId: 'human-sql-dev-01',
        agentName: 'Mike-SQLDeveloper',
        agentType: 'Human',
        workArea: 'Manual SQL query generation and database optimization',
        reportDuration: {
          startDate: DateTime.fromISO('2025-07-01'),
          endDate: DateTime.fromISO('2025-07-18')
        },
        metrics: {
          totalTasks: 85,
          successRate: 97,
          averageTime: 1800, // 30 minutes
          failedAttempts: 3,
          failureRate: 3
        },
        taskSteps: [
          { stepName: 'Requirements Analysis', averageTime: 600 },
          { stepName: 'Schema Review', averageTime: 300 },
          { stepName: 'Query Writing', averageTime: 720 },
          { stepName: 'Testing & Validation', averageTime: 180 }
        ],
        quality: {
          grammarQuality: 100,
          readabilityScore: 92.5,
          copiedContent: 0,
          overallQuality: 97.8
        },
        failedTasks: [
          {
            taskId: 'SQL-H-012',
            reason: 'Complex stored procedure requirements unclear',
            timestamp: DateTime.fromISO('2025-07-07T15:30:00')
          }
        ],
        insights: [
          {
            type: 'positive',
            title: 'Superior Complex Query Handling',
            description: 'Excels at complex multi-table joins and stored procedures'
          },
          {
            type: 'negative',
            title: 'Lower Throughput',
            description: 'Processes fewer queries per hour compared to AI',
            suggestion: 'Use AI for simple queries, human for complex ones'
          }
        ],
        activityPeriods: [
          { period: '9 AM - 5 PM', taskCount: 85, averageTime: 1800, successRate: 97 }
        ],
        lastUpdated: DateTime.now(),
        status: 'active'
      }
    ];

    this.analyticsDataSubject.next(demoData);
  }

  // Helper methods for calculations
  private calculateEfficiencyDifference(agent1: AgentPerformance, agent2: AgentPerformance): number {
    const efficiency1 = agent1.metrics.successRate / agent1.metrics.averageTime;
    const efficiency2 = agent2.metrics.successRate / agent2.metrics.averageTime;
    return ((efficiency1 - efficiency2) / efficiency2) * 100;
  }

  private calculateQualityDifference(agent1: AgentPerformance, agent2: AgentPerformance): number {
    return agent1.quality.overallQuality - agent2.quality.overallQuality;
  }

  private calculateReliabilityDifference(agent1: AgentPerformance, agent2: AgentPerformance): number {
    return agent1.metrics.successRate - agent2.metrics.successRate;
  }

  private calculateSpeedDifference(agent1: AgentPerformance, agent2: AgentPerformance): number {
    return ((agent2.metrics.averageTime - agent1.metrics.averageTime) / agent2.metrics.averageTime) * 100;
  }

  private generateRecommendations(agent1: AgentPerformance, agent2: AgentPerformance): string[] {
    const recommendations: string[] = [];

    if (agent1.metrics.successRate < agent2.metrics.successRate) {
      recommendations.push(`Improve ${agent1.agentName}'s reliability by analyzing ${agent2.agentName}'s success patterns`);
    }

    if (agent1.metrics.averageTime > agent2.metrics.averageTime) {
      recommendations.push(`Optimize ${agent1.agentName}'s processing speed using ${agent2.agentName}'s efficient methods`);
    }

    return recommendations;
  }

  private calculateAveragePerformance(agents: AgentPerformance[]): number {
    if (agents.length === 0) return 0;
    const totalPerformance = agents.reduce((sum, agent) => sum + agent.metrics.successRate, 0);
    return totalPerformance / agents.length;
  }

  private getTopPerformers(agents: AgentPerformance[], count: number): AgentPerformance[] {
    return agents
      .sort((a, b) => b.metrics.successRate - a.metrics.successRate)
      .slice(0, count);
  }

  private getUnderPerformers(agents: AgentPerformance[], count: number): AgentPerformance[] {
    return agents
      .sort((a, b) => a.metrics.successRate - b.metrics.successRate)
      .slice(0, count);
  }

  private applyFilter(data: AgentPerformance[], filter: AnalyticsFilter): AgentPerformance[] {
    return data.filter(agent => {
      // Filter by agent types
      if (filter.agentTypes.length > 0 && !filter.agentTypes.includes(agent.agentType)) {
        return false;
      }

      // Filter by status
      if (filter.status.length > 0 && !filter.status.includes(agent.status)) {
        return false;
      }

      // Filter by performance threshold
      if (filter.performanceThreshold && agent.metrics.successRate < filter.performanceThreshold) {
        return false;
      }

      return true;
    });
  }

  /**
   * Check if two agents work on similar tasks
   */
  private doAgentsWorkOnSimilarTasks(agent1: AgentPerformance, agent2: AgentPerformance): boolean {
    const task1Type = this.getTaskTypeFromWorkArea(agent1.workArea);
    const task2Type = this.getTaskTypeFromWorkArea(agent2.workArea);
    return task1Type === task2Type;
  }

  /**
   * Extract task type from work area description
   */
  private getTaskTypeFromWorkArea(workArea: string): string {
    const lowerWorkArea = workArea.toLowerCase();
    if (lowerWorkArea.includes('blog') || lowerWorkArea.includes('content')) {
      return 'content-creation';
    }
    if (lowerWorkArea.includes('sql') || lowerWorkArea.includes('query')) {
      return 'sql-generation';
    }
    if (lowerWorkArea.includes('code') || lowerWorkArea.includes('programming')) {
      return 'code-generation';
    }
    if (lowerWorkArea.includes('analysis') || lowerWorkArea.includes('data')) {
      return 'data-analysis';
    }
    return 'general';
  }

  /**
   * Group agents by task type for comparison
   */
  private groupAgentsByTaskType(agents: AgentPerformance[]): Array<{
    taskType: string;
    taskName: string;
    description: string;
    agents: AgentPerformance[];
  }> {
    const groups: { [key: string]: AgentPerformance[] } = {};

    agents.forEach(agent => {
      const taskType = this.getTaskTypeFromWorkArea(agent.workArea);
      if (!groups[taskType]) {
        groups[taskType] = [];
      }
      groups[taskType].push(agent);
    });

    return Object.keys(groups).map(taskType => ({
      taskType,
      taskName: this.getTaskDisplayName(taskType),
      description: this.getTaskDescription(taskType),
      agents: groups[taskType]
    }));
  }

  /**
   * Get display name for task type
   */
  private getTaskDisplayName(taskType: string): string {
    const names: { [key: string]: string } = {
      'content-creation': 'Content Creation',
      'sql-generation': 'SQL Query Generation',
      'code-generation': 'Code Generation',
      'data-analysis': 'Data Analysis',
      'general': 'General Tasks'
    };
    return names[taskType] || taskType;
  }

  /**
   * Get description for task type
   */
  private getTaskDescription(taskType: string): string {
    const descriptions: { [key: string]: string } = {
      'content-creation': 'Creating blog posts, articles, and written content',
      'sql-generation': 'Generating and optimizing SQL queries',
      'code-generation': 'Writing and debugging code',
      'data-analysis': 'Analyzing and interpreting data',
      'general': 'Various general purpose tasks'
    };
    return descriptions[taskType] || 'General task processing';
  }

  /**
   * Generate detailed task comparison between human and AI agents
   */
  private generateTaskComparison(humanAgent: AgentPerformance, aiAgent: AgentPerformance): TaskComparisonResult {
    // Speed comparison
    const speedAdvantage = this.compareSpeed(humanAgent, aiAgent);

    // Quality comparison
    const qualityAdvantage = this.compareQuality(humanAgent, aiAgent);

    // Reliability comparison
    const reliabilityAdvantage = this.compareReliability(humanAgent, aiAgent);

    // Cost efficiency analysis
    const costEfficiency = this.analyzeCostEfficiency(humanAgent, aiAgent);

    // Determine overall winner
    const winner = this.determineOverallWinner(speedAdvantage, qualityAdvantage, reliabilityAdvantage);

    // Generate recommendation
    const overallRecommendation = this.generateTaskRecommendation(
      humanAgent, aiAgent, speedAdvantage, qualityAdvantage, reliabilityAdvantage, costEfficiency
    );

    return {
      winner,
      speedAdvantage,
      qualityAdvantage,
      reliabilityAdvantage,
      costEfficiency,
      overallRecommendation
    };
  }

  /**
   * Compare speed between human and AI agents
   */
  private compareSpeed(humanAgent: AgentPerformance, aiAgent: AgentPerformance): TaskComparisonResult['speedAdvantage'] {
    const humanTime = humanAgent.metrics.averageTime;
    const aiTime = aiAgent.metrics.averageTime;
    const difference = ((humanTime - aiTime) / humanTime) * 100;

    return {
      winner: aiTime < humanTime ? 'ai' : humanTime < aiTime ? 'human' : 'tie',
      difference: Math.abs(difference),
      humanTime,
      aiTime
    };
  }

  /**
   * Compare quality between human and AI agents
   */
  private compareQuality(humanAgent: AgentPerformance, aiAgent: AgentPerformance): TaskComparisonResult['qualityAdvantage'] {
    const humanQuality = humanAgent.quality.overallQuality;
    const aiQuality = aiAgent.quality.overallQuality;
    const difference = Math.abs(humanQuality - aiQuality);

    return {
      winner: humanQuality > aiQuality ? 'human' : aiQuality > humanQuality ? 'ai' : 'tie',
      difference,
      humanQuality,
      aiQuality
    };
  }

  /**
   * Compare reliability between human and AI agents
   */
  private compareReliability(humanAgent: AgentPerformance, aiAgent: AgentPerformance): TaskComparisonResult['reliabilityAdvantage'] {
    const humanReliability = humanAgent.metrics.successRate;
    const aiReliability = aiAgent.metrics.successRate;
    const difference = Math.abs(humanReliability - aiReliability);

    return {
      winner: humanReliability > aiReliability ? 'human' : aiReliability > humanReliability ? 'ai' : 'tie',
      difference,
      humanReliability,
      aiReliability
    };
  }

  /**
   * Analyze cost efficiency
   */
  private analyzeCostEfficiency(humanAgent: AgentPerformance, aiAgent: AgentPerformance): TaskComparisonResult['costEfficiency'] {
    // Simple cost analysis based on time and throughput
    const humanTasksPerHour = 3600 / humanAgent.metrics.averageTime;
    const aiTasksPerHour = 3600 / aiAgent.metrics.averageTime;

    if (aiTasksPerHour > humanTasksPerHour * 10) {
      return {
        winner: 'ai',
        reasoning: `AI processes ${Math.round(aiTasksPerHour)} tasks/hour vs Human's ${Math.round(humanTasksPerHour)} tasks/hour`
      };
    } else if (humanAgent.quality.overallQuality > aiAgent.quality.overallQuality + 5) {
      return {
        winner: 'human',
        reasoning: 'Human provides significantly higher quality despite slower speed'
      };
    } else {
      return {
        winner: 'ai',
        reasoning: 'AI provides better cost efficiency through higher throughput'
      };
    }
  }

  /**
   * Determine overall winner based on multiple factors
   */
  private determineOverallWinner(
    speed: TaskComparisonResult['speedAdvantage'],
    quality: TaskComparisonResult['qualityAdvantage'],
    reliability: TaskComparisonResult['reliabilityAdvantage']
  ): 'human' | 'ai' | 'tie' {
    let humanScore = 0;
    let aiScore = 0;

    // Speed (weight: 1)
    if (speed.winner === 'human') humanScore += 1;
    else if (speed.winner === 'ai') aiScore += 1;

    // Quality (weight: 2 - more important)
    if (quality.winner === 'human') humanScore += 2;
    else if (quality.winner === 'ai') aiScore += 2;

    // Reliability (weight: 2 - more important)
    if (reliability.winner === 'human') humanScore += 2;
    else if (reliability.winner === 'ai') aiScore += 2;

    if (humanScore > aiScore) return 'human';
    if (aiScore > humanScore) return 'ai';
    return 'tie';
  }

  /**
   * Generate task-specific recommendation
   */
  private generateTaskRecommendation(
    humanAgent: AgentPerformance,
    aiAgent: AgentPerformance,
    speed: TaskComparisonResult['speedAdvantage'],
    quality: TaskComparisonResult['qualityAdvantage'],
    reliability: TaskComparisonResult['reliabilityAdvantage'],
    cost: TaskComparisonResult['costEfficiency']
  ): string {
    const taskType = this.getTaskTypeFromWorkArea(humanAgent.workArea);

    if (speed.winner === 'ai' && quality.winner === 'human') {
      return `For ${this.getTaskDisplayName(taskType)}: Use AI for initial drafts (${speed.difference.toFixed(1)}% faster), then human review for quality enhancement (+${quality.difference.toFixed(1)}% quality improvement)`;
    }

    if (quality.winner === 'human' && quality.difference > 10) {
      return `For ${this.getTaskDisplayName(taskType)}: Human agents provide significantly higher quality (+${quality.difference.toFixed(1)}%). Consider human-first approach for critical tasks.`;
    }

    if (speed.winner === 'ai' && speed.difference > 50) {
      return `For ${this.getTaskDisplayName(taskType)}: AI agents are ${speed.difference.toFixed(1)}% faster. Use AI for high-volume, time-sensitive tasks with human oversight.`;
    }

    if (reliability.winner === 'human') {
      return `For ${this.getTaskDisplayName(taskType)}: Human agents are more reliable (+${reliability.difference.toFixed(1)}% success rate). Use for mission-critical tasks.`;
    }

    return `For ${this.getTaskDisplayName(taskType)}: ${cost.winner === 'ai' ? 'AI' : 'Human'} agents provide better overall value. ${cost.reasoning}`;
  }
}
