﻿using System.Collections.Generic;

namespace ProjectApp.Core.Dtos;

public class AgentDefinitionDto
{
    public string AgentName { get; set; }
    public string Instructions { get; set; }
    public string UserInstructions { get; set; }
    public string ModelName { get; set; }
    public string Workspace { get; set; }
    public string[] Tools { get; set; }
    public Dictionary<string, object> Arguments { get; set; } = new Dictionary<string, object>();

    // New properties for source-based agents
    public string? SourceType { get; set; } // Database, Wikipedia, FileSystem, etc.
    public string? SourceName { get; set; } // Connection name or source identifier
    public string? SourceConfig { get; set; } // JSON configuration for the source
}

public class AgentNameDto
{
    public string AgentName { get; set; }
}
