
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-delete-prompt-modal',
  standalone: true,
  imports: [CommonModule, NzModalModule],
  template: `
    <div class="custom-delete-modal">
      <div class="ant-modal-content">
        <div class="ant-modal-header">
          <div class="ant-modal-title">
            <i class="ri-error-warning-line text-[var(--primary-purple)] mr-2"></i>{{ title }}
          </div>
        </div>
        <div class="ant-modal-body">
          <p>{{ content }}</p>
        </div>
        <div class="ant-modal-confirm-btns">
          <button nz-button class="ant-btn ant-btn-default" (click)="onCancel()">
            <span>{{ cancelText }}</span>
          </button>
          <button nz-button nzType="primary" class="ant-btn ant-btn-primary" [disabled]="isLoading" (click)="onOk()">
            <span *ngIf="!isLoading">{{ okText }}</span>
            <span *ngIf="isLoading">
              <i class="anticon anticon-loading anticon-spin"></i> {{ okText }}
            </span>
          </button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./delete-prompt-modal.component.css']
})
export class DeletePromptModalComponent {
  @Input() title: string = 'Confirm Delete';
  @Input() content: string = 'Are you sure?';
  @Input() okText: string = 'Yes';
  @Input() cancelText: string = 'No';
  @Input() isLoading: boolean = false;
  @Output() ok = new EventEmitter<void>();
  @Output() cancel = new EventEmitter<void>();

  onOk() {
    this.ok.emit();
  }

  onCancel() {
    this.cancel.emit();
  }
}

