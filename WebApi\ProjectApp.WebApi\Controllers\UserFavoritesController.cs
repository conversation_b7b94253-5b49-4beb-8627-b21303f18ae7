using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class UserFavoritesController : ControllerBase
    {
        private readonly IUserFavoritesRepository _userFavoritesRepository;
        private readonly IExtractEmailFromAccessor _extractEmail;

        public UserFavoritesController(IUserFavoritesRepository userFavoritesRepository, IExtractEmailFromAccessor extractEmail)
        {
            _userFavoritesRepository = userFavoritesRepository;
            _extractEmail = extractEmail;
        }

        private string GetUserEmail()
        {
            return _extractEmail.GetEmail(); // Retrieve the user's email
        }

        [HttpPost("toggle")]
        public async Task<ActionResult<ResponseMessage>> ToggleFavorite([FromBody] FavoriteToggleRequest request)
        {
            var userEmail = GetUserEmail();
            var isFavorite = await _userFavoritesRepository.ToggleFavorite(userEmail, request.ItemKey, request.ItemType);
            return Ok(new { IsFavorite = isFavorite });
        }

        [HttpGet("list")]
        public async Task<ActionResult<UserFavoriteDto>> GetFavorites()
        {
            var userEmail = GetUserEmail();
            var favorites = await _userFavoritesRepository.GetFavorites(userEmail);
            return Ok(favorites);
        }

        [HttpGet("isFavorite/{itemKey}/{itemType}")]
        public async Task<ActionResult<ResponseMessage>> IsFavorite(string itemKey, string itemType)
        {
            var userEmail = GetUserEmail();
            var isFavorite = await _userFavoritesRepository.IsFavorite(userEmail, itemKey, itemType);
            return Ok(new { IsFavorite = isFavorite });
        }
    }

    public class FavoriteToggleRequest
    {
        public string ItemKey { get; set; } // Workspace title OR agent name
        public string ItemType { get; set; } // 'Workspace' | 'Agent'
    }
}
