using System.ComponentModel.DataAnnotations;

namespace ProjectApp.Core.Dtos
{
    public class DatabaseConnectionDto
    {
        public Guid Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string DatabaseType { get; set; } = string.Empty;
        
        [Required]
        public string ConnectionString { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; }
        
        public DateTime? UpdatedAt { get; set; }
        
        public string? CreatedBy { get; set; }
        
        public string? UpdatedBy { get; set; }
        
        public bool IsSchemaExtracted { get; set; }
        
        public DateTime? LastSchemaExtraction { get; set; }
        
        public string? SchemaExtractionStatus { get; set; }
        
        public string? SchemaExtractionError { get; set; }
    }
    
    public class CreateDatabaseConnectionDto
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string DatabaseType { get; set; } = string.Empty;
        
        [Required]
        public string ConnectionString { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
    
    public class UpdateDatabaseConnectionDto
    {
        [Required]
        public Guid Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string DatabaseType { get; set; } = string.Empty;
        
        [Required]
        public string ConnectionString { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
    }
    
    public class TestConnectionDto
    {
        [Required]
        public string ConnectionString { get; set; } = string.Empty;
        
        [Required]
        public string DatabaseType { get; set; } = string.Empty;
    }
    
    public class SchemaExtractionRequestDto
    {
        [Required]
        public Guid DatabaseConnectionId { get; set; }
        
        public bool ForceRefresh { get; set; } = false;
    }
    
    public class SchemaExtractionResponseDto
    {
        public bool Success { get; set; }
        
        public string Message { get; set; } = string.Empty;
        
        public int TablesExtracted { get; set; }
        
        public int ColumnsExtracted { get; set; }
        
        public DateTime ExtractionTime { get; set; }
        
        public List<string> Errors { get; set; } = new();
    }
}
