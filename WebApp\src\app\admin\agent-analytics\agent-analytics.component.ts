import { Compo<PERSON>, OnInit, OnD<PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { DateTime } from 'luxon';

// Angular Material/ng-zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';

// Services and models
import { AgentAnalyticsService } from '../../services/agent-analytics.service';
import { ThemeService } from '../../../shared/services/theam.service';
import {
  AgentPerformance,
  AgentComparison,
  AgentAnalyticsSummary,
  AnalyticsFilter,
  MetricType,
  TaskPerformance,
  TaskComparisonResult
} from '../../models/agent-performance.models';

@Component({
  selector: 'app-agent-analytics',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    NzCardModule,
    NzStatisticModule,
    NzProgressModule,
    NzSelectModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzSpinModule,
    NzDividerModule,
    NzAlertModule,
    NzDatePickerModule
  ],
  templateUrl: './agent-analytics.component.html',
  styleUrls: ['./agent-analytics.component.css']
})
export class AgentAnalyticsComponent implements OnInit, OnDestroy {
  // Injected services
  private analyticsService = inject(AgentAnalyticsService);
  private themeService = inject(ThemeService);
  private router = inject(Router);
  private destroy$ = new Subject<void>();

  // Component state
  loading = false;
  summary: AgentAnalyticsSummary | null = null;
  agents: AgentPerformance[] = [];
  selectedAgents: string[] = [];
  comparison: AgentComparison | null = null;
  taskComparisons: TaskPerformance[] = [];

  // Filter state
  currentFilter: AnalyticsFilter = {
    dateRange: {
      startDate: DateTime.now().minus({ days: 30 }),
      endDate: DateTime.now()
    },
    agentTypes: ['AI', 'Human'],
    workAreas: [],
    status: ['active'],
    performanceThreshold: 0
  };

  // UI state
  activeTab: 'overview' | 'performance' | 'comparison' | 'task-comparison' | 'insights' = 'overview';
  showFilters = false;

  // Table columns for agent list
  agentColumns = [
    { title: 'Agent Name', key: 'agentName', sortable: true },
    { title: 'Type', key: 'agentType', sortable: true },
    { title: 'Work Area', key: 'workArea', sortable: false },
    { title: 'Success Rate', key: 'successRate', sortable: true },
    { title: 'Avg Time', key: 'averageTime', sortable: true },
    { title: 'Status', key: 'status', sortable: true },
    { title: 'Actions', key: 'actions', sortable: false }
  ];

  ngOnInit(): void {
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load all analytics data
   */
  loadData(): void {
    this.loading = true;

    forkJoin({
      summary: this.analyticsService.getAnalyticsSummary(),
      agents: this.analyticsService.getAllAgentPerformance(this.currentFilter),
      taskComparisons: this.analyticsService.getTaskPerformanceComparisons()
    }).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (data) => {
        this.summary = data.summary;
        this.agents = data.agents;
        this.taskComparisons = data.taskComparisons;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading analytics data:', error);
        this.loading = false;
      }
    });
  }

  /**
   * Handle tab change
   */
  onTabChange(tab: 'overview' | 'performance' | 'comparison' | 'task-comparison' | 'insights'): void {
    this.activeTab = tab;
  }

  /**
   * Handle agent selection for comparison
   */
  onAgentSelect(agentId: string): void {
    if (this.selectedAgents.includes(agentId)) {
      this.selectedAgents = this.selectedAgents.filter(id => id !== agentId);
    } else if (this.selectedAgents.length < 2) {
      this.selectedAgents.push(agentId);
    }

    // Auto-compare when 2 agents are selected
    if (this.selectedAgents.length === 2) {
      this.compareSelectedAgents();
    }
  }

  /**
   * Compare selected agents
   */
  compareSelectedAgents(): void {
    if (this.selectedAgents.length !== 2) return;

    this.loading = true;
    this.analyticsService.compareAgents(this.selectedAgents[0], this.selectedAgents[1])
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (comparison) => {
          this.comparison = comparison;
          this.activeTab = 'comparison';
          this.loading = false;
        },
        error: (error) => {
          console.error('Error comparing agents:', error);
          this.loading = false;
        }
      });
  }

  /**
   * Clear agent selection
   */
  clearSelection(): void {
    this.selectedAgents = [];
    this.comparison = null;
  }

  /**
   * Apply filters
   */
  applyFilters(): void {
    this.loadData();
    this.showFilters = false;
  }

  /**
   * Reset filters to default
   */
  resetFilters(): void {
    this.currentFilter = {
      dateRange: {
        startDate: DateTime.now().minus({ days: 30 }),
        endDate: DateTime.now()
      },
      agentTypes: ['AI', 'Human'],
      workAreas: [],
      status: ['active'],
      performanceThreshold: 0
    };
    this.loadData();
  }

  /**
   * Get status color for agent
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'maintenance': return 'orange';
      default: return 'default';
    }
  }

  /**
   * Get performance color based on success rate
   */
  getPerformanceColor(successRate: number): string {
    if (successRate >= 95) return '#52c41a'; // Green
    if (successRate >= 85) return '#faad14'; // Orange
    return '#ff4d4f'; // Red
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds: number): string {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
  }

  /**
   * Navigate to agent details
   */
  viewAgentDetails(agentId: string): void {
    this.router.navigate(['/settings/agent-analytics/details', agentId]);
  }

  /**
   * Export analytics data
   */
  exportData(): void {
    // Implementation for data export
    console.log('Exporting analytics data...');
  }

  /**
   * Check if theme is dark mode
   */
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode();
  }

  /**
   * Get theme-appropriate card class
   */
  get cardClass(): string {
    return this.isDarkMode
      ? 'bg-[var(--background-white)] border-[var(--hover-blue-gray)]'
      : 'bg-white border-gray-200';
  }

  /**
   * Get theme-appropriate text class
   */
  get textClass(): string {
    return this.isDarkMode ? 'text-[var(--text-dark)]' : 'text-gray-900';
  }

  /**
   * Get theme-appropriate secondary text class
   */
  get secondaryTextClass(): string {
    return this.isDarkMode ? 'text-[var(--text-medium-gray)]' : 'text-gray-600';
  }

  /**
   * Get winner display text and color
   */
  getWinnerDisplay(winner: 'human' | 'ai' | 'tie'): { text: string; color: string } {
    switch (winner) {
      case 'human':
        return { text: 'Human Agent', color: 'text-green-600' };
      case 'ai':
        return { text: 'AI Agent', color: 'text-blue-600' };
      case 'tie':
        return { text: 'Tie', color: 'text-gray-600' };
      default:
        return { text: 'Unknown', color: 'text-gray-400' };
    }
  }

  /**
   * Get performance advantage text
   */
  getAdvantageText(advantage: { winner: 'human' | 'ai' | 'tie'; difference: number }): string {
    if (advantage.winner === 'tie') return 'Equal performance';
    const winnerText = advantage.winner === 'human' ? 'Human' : 'AI';
    return `${winnerText} +${advantage.difference.toFixed(1)}%`;
  }
}
