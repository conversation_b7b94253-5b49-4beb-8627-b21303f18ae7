{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-message.mjs"], "sourcesContent": ["import { ComponentPortal } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { Directive, EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, NgModule, Injectable } from '@angular/core';\nimport { Subject } from 'rxjs';\nimport { filter, take, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { NgClass, NgSwitch, NgSwitchCase, NgForOf } from '@angular/common';\nimport { toCssPixel } from 'ng-zorro-antd/core/util';\nimport { moveUpMotion } from 'ng-zorro-antd/core/animation';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i1$2 from 'ng-zorro-antd/core/services';\nimport * as i2$1 from '@angular/cdk/overlay';\nfunction NzMessageComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 10);\n  }\n}\nfunction NzMessageComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n}\nfunction NzMessageComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 12);\n  }\n}\nfunction NzMessageComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 13);\n  }\n}\nfunction NzMessageComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n}\nfunction NzMessageComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r0.instance.content, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzMessageContainerComponent_nz_message_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-message\", 2);\n    i0.ɵɵlistener(\"destroyed\", function NzMessageContainerComponent_nz_message_1_Template_nz_message_destroyed_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.remove($event.id, $event.userAction));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const instance_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"instance\", instance_r3);\n  }\n}\nlet globalCounter = 0;\nclass NzMNService {\n  constructor(nzSingletonService, overlay, injector) {\n    this.nzSingletonService = nzSingletonService;\n    this.overlay = overlay;\n    this.injector = injector;\n  }\n  remove(id) {\n    if (this.container) {\n      if (id) {\n        this.container.remove(id);\n      } else {\n        this.container.removeAll();\n      }\n    }\n  }\n  getInstanceId() {\n    return `${this.componentPrefix}-${globalCounter++}`;\n  }\n  withContainer(ctor) {\n    let containerInstance = this.nzSingletonService.getSingletonWithKey(this.componentPrefix);\n    if (containerInstance) {\n      return containerInstance;\n    }\n    const overlayRef = this.overlay.create({\n      hasBackdrop: false,\n      scrollStrategy: this.overlay.scrollStrategies.noop(),\n      positionStrategy: this.overlay.position().global()\n    });\n    const componentPortal = new ComponentPortal(ctor, null, this.injector);\n    const componentRef = overlayRef.attach(componentPortal);\n    const overlayWrapper = overlayRef.hostElement;\n    overlayWrapper.style.zIndex = '1010';\n    if (!containerInstance) {\n      this.container = containerInstance = componentRef.instance;\n      this.nzSingletonService.registerSingletonWithKey(this.componentPrefix, containerInstance);\n      this.container.afterAllInstancesRemoved.subscribe(() => {\n        this.container = undefined;\n        this.nzSingletonService.unregisterSingletonWithKey(this.componentPrefix);\n        overlayRef.dispose();\n      });\n    }\n    return containerInstance;\n  }\n}\nclass NzMNContainerComponent {\n  constructor(cdr, nzConfigService) {\n    this.cdr = cdr;\n    this.nzConfigService = nzConfigService;\n    this.instances = [];\n    this._afterAllInstancesRemoved = new Subject();\n    this.afterAllInstancesRemoved = this._afterAllInstancesRemoved.asObservable();\n    this.destroy$ = new Subject();\n    this.updateConfig();\n  }\n  ngOnInit() {\n    this.subscribeConfigChange();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  create(data) {\n    const instance = this.onCreate(data);\n    if (this.instances.length >= this.config.nzMaxStack) {\n      this.instances = this.instances.slice(1);\n    }\n    this.instances = [...this.instances, instance];\n    this.readyInstances();\n    return instance;\n  }\n  remove(id, userAction = false) {\n    this.instances.map((instance, index) => ({\n      index,\n      instance\n    })).filter(({\n      instance\n    }) => instance.messageId === id).forEach(({\n      index,\n      instance\n    }) => {\n      this.instances.splice(index, 1);\n      this.instances = [...this.instances];\n      this.onRemove(instance, userAction);\n      this.readyInstances();\n    });\n    if (!this.instances.length) {\n      this.onAllInstancesRemoved();\n    }\n  }\n  removeAll() {\n    this.instances.forEach(i => this.onRemove(i, false));\n    this.instances = [];\n    this.readyInstances();\n    this.onAllInstancesRemoved();\n  }\n  onCreate(instance) {\n    instance.options = this.mergeOptions(instance.options);\n    instance.onClose = new Subject();\n    return instance;\n  }\n  onRemove(instance, userAction) {\n    instance.onClose.next(userAction);\n    instance.onClose.complete();\n  }\n  onAllInstancesRemoved() {\n    this._afterAllInstancesRemoved.next();\n    this._afterAllInstancesRemoved.complete();\n  }\n  readyInstances() {\n    this.cdr.detectChanges();\n  }\n  mergeOptions(options) {\n    const {\n      nzDuration,\n      nzAnimate,\n      nzPauseOnHover\n    } = this.config;\n    return {\n      nzDuration,\n      nzAnimate,\n      nzPauseOnHover,\n      ...options\n    };\n  }\n  static {\n    this.ɵfac = function NzMNContainerComponent_Factory(t) {\n      return new (t || NzMNContainerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMNContainerComponent\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMNContainerComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }], null);\n})();\nclass NzMNComponent {\n  constructor(cdr) {\n    this.cdr = cdr;\n    this.destroyed = new EventEmitter();\n    this.animationStateChanged = new Subject();\n    this.userAction = false;\n  }\n  ngOnInit() {\n    this.options = this.instance.options;\n    if (this.options.nzAnimate) {\n      this.instance.state = 'enter';\n      this.animationStateChanged.pipe(filter(event => event.phaseName === 'done' && event.toState === 'leave'), take(1)).subscribe(() => {\n        clearTimeout(this.closeTimer);\n        this.destroyed.next({\n          id: this.instance.messageId,\n          userAction: this.userAction\n        });\n      });\n    }\n    this.autoClose = this.options.nzDuration > 0;\n    if (this.autoClose) {\n      this.initErase();\n      this.startEraseTimeout();\n    }\n  }\n  ngOnDestroy() {\n    if (this.autoClose) {\n      this.clearEraseTimeout();\n    }\n    this.animationStateChanged.complete();\n  }\n  onEnter() {\n    if (this.autoClose && this.options.nzPauseOnHover) {\n      this.clearEraseTimeout();\n      this.updateTTL();\n    }\n  }\n  onLeave() {\n    if (this.autoClose && this.options.nzPauseOnHover) {\n      this.startEraseTimeout();\n    }\n  }\n  destroy(userAction = false) {\n    this.userAction = userAction;\n    if (this.options.nzAnimate) {\n      this.instance.state = 'leave';\n      this.cdr.detectChanges();\n      this.closeTimer = setTimeout(() => {\n        this.closeTimer = undefined;\n        this.destroyed.next({\n          id: this.instance.messageId,\n          userAction\n        });\n      }, 200);\n    } else {\n      this.destroyed.next({\n        id: this.instance.messageId,\n        userAction\n      });\n    }\n  }\n  initErase() {\n    this.eraseTTL = this.options.nzDuration;\n    this.eraseTimingStart = Date.now();\n  }\n  updateTTL() {\n    if (this.autoClose) {\n      this.eraseTTL -= Date.now() - this.eraseTimingStart;\n    }\n  }\n  startEraseTimeout() {\n    if (this.eraseTTL > 0) {\n      this.clearEraseTimeout();\n      this.eraseTimer = setTimeout(() => this.destroy(), this.eraseTTL);\n      this.eraseTimingStart = Date.now();\n    } else {\n      this.destroy();\n    }\n  }\n  clearEraseTimeout() {\n    if (this.eraseTimer !== null) {\n      clearTimeout(this.eraseTimer);\n      this.eraseTimer = undefined;\n    }\n  }\n  static {\n    this.ɵfac = function NzMNComponent_Factory(t) {\n      return new (t || NzMNComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzMNComponent\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMNComponent, [{\n    type: Directive\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageComponent extends NzMNComponent {\n  constructor(cdr) {\n    super(cdr);\n    this.destroyed = new EventEmitter();\n  }\n  static {\n    this.ɵfac = function NzMessageComponent_Factory(t) {\n      return new (t || NzMessageComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMessageComponent,\n      selectors: [[\"nz-message\"]],\n      inputs: {\n        instance: \"instance\"\n      },\n      outputs: {\n        destroyed: \"destroyed\"\n      },\n      exportAs: [\"nzMessage\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 10,\n      vars: 9,\n      consts: [[1, \"ant-message-notice\", 3, \"mouseenter\", \"mouseleave\"], [1, \"ant-message-notice-content\"], [1, \"ant-message-custom-content\", 3, \"ngClass\"], [3, \"ngSwitch\"], [\"nz-icon\", \"\", \"nzType\", \"check-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"info-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"exclamation-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"close-circle\", 4, \"ngSwitchCase\"], [\"nz-icon\", \"\", \"nzType\", \"loading\", 4, \"ngSwitchCase\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", \"nzType\", \"check-circle\"], [\"nz-icon\", \"\", \"nzType\", \"info-circle\"], [\"nz-icon\", \"\", \"nzType\", \"exclamation-circle\"], [\"nz-icon\", \"\", \"nzType\", \"close-circle\"], [\"nz-icon\", \"\", \"nzType\", \"loading\"], [3, \"innerHTML\"]],\n      template: function NzMessageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵlistener(\"@moveUpMotion.done\", function NzMessageComponent_Template_div_animation_moveUpMotion_done_0_listener($event) {\n            return ctx.animationStateChanged.next($event);\n          })(\"mouseenter\", function NzMessageComponent_Template_div_mouseenter_0_listener() {\n            return ctx.onEnter();\n          })(\"mouseleave\", function NzMessageComponent_Template_div_mouseleave_0_listener() {\n            return ctx.onLeave();\n          });\n          i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementContainerStart(3, 3);\n          i0.ɵɵtemplate(4, NzMessageComponent_span_4_Template, 1, 0, \"span\", 4)(5, NzMessageComponent_span_5_Template, 1, 0, \"span\", 5)(6, NzMessageComponent_span_6_Template, 1, 0, \"span\", 6)(7, NzMessageComponent_span_7_Template, 1, 0, \"span\", 7)(8, NzMessageComponent_span_8_Template, 1, 0, \"span\", 8);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(9, NzMessageComponent_ng_container_9_Template, 2, 1, \"ng-container\", 9);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"@moveUpMotion\", ctx.instance.state);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", \"ant-message-\" + ctx.instance.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitch\", ctx.instance.type);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"success\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"info\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"warning\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"error\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngSwitchCase\", \"loading\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.instance.content);\n        }\n      },\n      dependencies: [NgClass, NgSwitch, NgSwitchCase, NzIconModule, i1$1.NzIconDirective, NzOutletModule, i2.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      data: {\n        animation: [moveUpMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-message',\n      exportAs: 'nzMessage',\n      preserveWhitespaces: false,\n      animations: [moveUpMotion],\n      template: `\n    <div\n      class=\"ant-message-notice\"\n      [@moveUpMotion]=\"instance.state\"\n      (@moveUpMotion.done)=\"animationStateChanged.next($event)\"\n      (mouseenter)=\"onEnter()\"\n      (mouseleave)=\"onLeave()\"\n    >\n      <div class=\"ant-message-notice-content\">\n        <div class=\"ant-message-custom-content\" [ngClass]=\"'ant-message-' + instance.type\">\n          <ng-container [ngSwitch]=\"instance.type\">\n            <span *ngSwitchCase=\"'success'\" nz-icon nzType=\"check-circle\"></span>\n            <span *ngSwitchCase=\"'info'\" nz-icon nzType=\"info-circle\"></span>\n            <span *ngSwitchCase=\"'warning'\" nz-icon nzType=\"exclamation-circle\"></span>\n            <span *ngSwitchCase=\"'error'\" nz-icon nzType=\"close-circle\"></span>\n            <span *ngSwitchCase=\"'loading'\" nz-icon nzType=\"loading\"></span>\n          </ng-container>\n          <ng-container *nzStringTemplateOutlet=\"instance.content\">\n            <span [innerHTML]=\"instance.content\"></span>\n          </ng-container>\n        </div>\n      </div>\n    </div>\n  `,\n      imports: [NgClass, NgSwitch, NgSwitchCase, NzIconModule, NzOutletModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    instance: [{\n      type: Input\n    }],\n    destroyed: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_COMPONENT_NAME = 'message';\nconst NZ_MESSAGE_DEFAULT_CONFIG = {\n  nzAnimate: true,\n  nzDuration: 3000,\n  nzMaxStack: 7,\n  nzPauseOnHover: true,\n  nzTop: 24,\n  nzDirection: 'ltr'\n};\nclass NzMessageContainerComponent extends NzMNContainerComponent {\n  constructor(cdr, nzConfigService) {\n    super(cdr, nzConfigService);\n    this.dir = 'ltr';\n    const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n    this.dir = config?.nzDirection || 'ltr';\n  }\n  subscribeConfigChange() {\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_COMPONENT_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateConfig();\n      const config = this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME);\n      if (config) {\n        const {\n          nzDirection\n        } = config;\n        this.dir = nzDirection || this.dir;\n      }\n    });\n  }\n  updateConfig() {\n    this.config = {\n      ...NZ_MESSAGE_DEFAULT_CONFIG,\n      ...this.config,\n      ...this.nzConfigService.getConfigForComponent(NZ_CONFIG_COMPONENT_NAME)\n    };\n    this.top = toCssPixel(this.config.nzTop);\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzMessageContainerComponent_Factory(t) {\n      return new (t || NzMessageContainerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzMessageContainerComponent,\n      selectors: [[\"nz-message-container\"]],\n      exportAs: [\"nzMessageContainer\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 5,\n      consts: [[1, \"ant-message\"], [3, \"instance\", \"destroyed\", 4, \"ngFor\", \"ngForOf\"], [3, \"destroyed\", \"instance\"]],\n      template: function NzMessageContainerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzMessageContainerComponent_nz_message_1_Template, 1, 1, \"nz-message\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"top\", ctx.top);\n          i0.ɵɵclassProp(\"ant-message-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.instances);\n        }\n      },\n      dependencies: [NzMessageComponent, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageContainerComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-message-container',\n      exportAs: 'nzMessageContainer',\n      preserveWhitespaces: false,\n      template: `\n    <div class=\"ant-message\" [class.ant-message-rtl]=\"dir === 'rtl'\" [style.top]=\"top\">\n      <nz-message\n        *ngFor=\"let instance of instances\"\n        [instance]=\"instance\"\n        (destroyed)=\"remove($event.id, $event.userAction)\"\n      ></nz-message>\n    </div>\n  `,\n      imports: [NzMessageComponent, NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzMessageModule {\n  static {\n    this.ɵfac = function NzMessageModule_Factory(t) {\n      return new (t || NzMessageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzMessageModule,\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzMessageContainerComponent, NzMessageComponent]\n    }]\n  }], null, null);\n})();\nclass NzMessageService extends NzMNService {\n  constructor(nzSingletonService, overlay, injector) {\n    super(nzSingletonService, overlay, injector);\n    this.componentPrefix = 'message-';\n  }\n  success(content, options) {\n    return this.createInstance({\n      type: 'success',\n      content\n    }, options);\n  }\n  error(content, options) {\n    return this.createInstance({\n      type: 'error',\n      content\n    }, options);\n  }\n  info(content, options) {\n    return this.createInstance({\n      type: 'info',\n      content\n    }, options);\n  }\n  warning(content, options) {\n    return this.createInstance({\n      type: 'warning',\n      content\n    }, options);\n  }\n  loading(content, options) {\n    return this.createInstance({\n      type: 'loading',\n      content\n    }, options);\n  }\n  create(type, content, options) {\n    return this.createInstance({\n      type,\n      content\n    }, options);\n  }\n  createInstance(message, options) {\n    this.container = this.withContainer(NzMessageContainerComponent);\n    return this.container.create({\n      ...message,\n      ...{\n        createdAt: new Date(),\n        messageId: this.getInstanceId(),\n        options\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NzMessageService_Factory(t) {\n      return new (t || NzMessageService)(i0.ɵɵinject(i1$2.NzSingletonService), i0.ɵɵinject(i2$1.Overlay), i0.ɵɵinject(i0.Injector));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzMessageService,\n      factory: NzMessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzMessageService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$2.NzSingletonService\n  }, {\n    type: i2$1.Overlay\n  }, {\n    type: i0.Injector\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzMNComponent, NzMNContainerComponent, NzMNService, NzMessageComponent, NzMessageContainerComponent, NzMessageModule, NzMessageService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,mCAAmC,IAAI,KAAK;AACnD,MAAI,KAAK,GAAG;AACV,IAAG,UAAU,GAAG,QAAQ,EAAE;AAAA,EAC5B;AACF;AACA,SAAS,2CAA2C,IAAI,KAAK;AAC3D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAS,SAAY,cAAc;AAAA,EACvE;AACF;AACA,SAAS,kDAAkD,IAAI,KAAK;AAClE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,cAAc,CAAC;AACpC,IAAG,WAAW,aAAa,SAAS,kFAAkF,QAAQ;AAC5H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc;AAChC,aAAU,YAAY,OAAO,OAAO,OAAO,IAAI,OAAO,UAAU,CAAC;AAAA,IACnE,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,cAAc,IAAI;AACxB,IAAG,WAAW,YAAY,WAAW;AAAA,EACvC;AACF;AACA,IAAI,gBAAgB;AACpB,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,oBAAoB,SAAS,UAAU;AACjD,SAAK,qBAAqB;AAC1B,SAAK,UAAU;AACf,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,OAAO,IAAI;AACT,QAAI,KAAK,WAAW;AAClB,UAAI,IAAI;AACN,aAAK,UAAU,OAAO,EAAE;AAAA,MAC1B,OAAO;AACL,aAAK,UAAU,UAAU;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,WAAO,GAAG,KAAK,eAAe,IAAI,eAAe;AAAA,EACnD;AAAA,EACA,cAAc,MAAM;AAClB,QAAI,oBAAoB,KAAK,mBAAmB,oBAAoB,KAAK,eAAe;AACxF,QAAI,mBAAmB;AACrB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,KAAK,QAAQ,OAAO;AAAA,MACrC,aAAa;AAAA,MACb,gBAAgB,KAAK,QAAQ,iBAAiB,KAAK;AAAA,MACnD,kBAAkB,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAA,IACnD,CAAC;AACD,UAAM,kBAAkB,IAAI,gBAAgB,MAAM,MAAM,KAAK,QAAQ;AACrE,UAAM,eAAe,WAAW,OAAO,eAAe;AACtD,UAAM,iBAAiB,WAAW;AAClC,mBAAe,MAAM,SAAS;AAC9B,QAAI,CAAC,mBAAmB;AACtB,WAAK,YAAY,oBAAoB,aAAa;AAClD,WAAK,mBAAmB,yBAAyB,KAAK,iBAAiB,iBAAiB;AACxF,WAAK,UAAU,yBAAyB,UAAU,MAAM;AACtD,aAAK,YAAY;AACjB,aAAK,mBAAmB,2BAA2B,KAAK,eAAe;AACvE,mBAAW,QAAQ;AAAA,MACrB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,KAAK,iBAAiB;AAChC,SAAK,MAAM;AACX,SAAK,kBAAkB;AACvB,SAAK,YAAY,CAAC;AAClB,SAAK,4BAA4B,IAAI,QAAQ;AAC7C,SAAK,2BAA2B,KAAK,0BAA0B,aAAa;AAC5E,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO,MAAM;AACX,UAAM,WAAW,KAAK,SAAS,IAAI;AACnC,QAAI,KAAK,UAAU,UAAU,KAAK,OAAO,YAAY;AACnD,WAAK,YAAY,KAAK,UAAU,MAAM,CAAC;AAAA,IACzC;AACA,SAAK,YAAY,CAAC,GAAG,KAAK,WAAW,QAAQ;AAC7C,SAAK,eAAe;AACpB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,IAAI,aAAa,OAAO;AAC7B,SAAK,UAAU,IAAI,CAAC,UAAU,WAAW;AAAA,MACvC;AAAA,MACA;AAAA,IACF,EAAE,EAAE,OAAO,CAAC;AAAA,MACV;AAAA,IACF,MAAM,SAAS,cAAc,EAAE,EAAE,QAAQ,CAAC;AAAA,MACxC;AAAA,MACA;AAAA,IACF,MAAM;AACJ,WAAK,UAAU,OAAO,OAAO,CAAC;AAC9B,WAAK,YAAY,CAAC,GAAG,KAAK,SAAS;AACnC,WAAK,SAAS,UAAU,UAAU;AAClC,WAAK,eAAe;AAAA,IACtB,CAAC;AACD,QAAI,CAAC,KAAK,UAAU,QAAQ;AAC1B,WAAK,sBAAsB;AAAA,IAC7B;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,UAAU,QAAQ,OAAK,KAAK,SAAS,GAAG,KAAK,CAAC;AACnD,SAAK,YAAY,CAAC;AAClB,SAAK,eAAe;AACpB,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,SAAS,UAAU;AACjB,aAAS,UAAU,KAAK,aAAa,SAAS,OAAO;AACrD,aAAS,UAAU,IAAI,QAAQ;AAC/B,WAAO;AAAA,EACT;AAAA,EACA,SAAS,UAAU,YAAY;AAC7B,aAAS,QAAQ,KAAK,UAAU;AAChC,aAAS,QAAQ,SAAS;AAAA,EAC5B;AAAA,EACA,wBAAwB;AACtB,SAAK,0BAA0B,KAAK;AACpC,SAAK,0BAA0B,SAAS;AAAA,EAC1C;AAAA,EACA,iBAAiB;AACf,SAAK,IAAI,cAAc;AAAA,EACzB;AAAA,EACA,aAAa,SAAS;AACpB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI,KAAK;AACT,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,OACG;AAAA,EAEP;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAA2B,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,CAAC;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,YAAY,KAAK;AACf,SAAK,MAAM;AACX,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,wBAAwB,IAAI,QAAQ;AACzC,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,WAAW;AACT,SAAK,UAAU,KAAK,SAAS;AAC7B,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,SAAS,QAAQ;AACtB,WAAK,sBAAsB,KAAK,OAAO,WAAS,MAAM,cAAc,UAAU,MAAM,YAAY,OAAO,GAAG,KAAK,CAAC,CAAC,EAAE,UAAU,MAAM;AACjI,qBAAa,KAAK,UAAU;AAC5B,aAAK,UAAU,KAAK;AAAA,UAClB,IAAI,KAAK,SAAS;AAAA,UAClB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,SAAK,YAAY,KAAK,QAAQ,aAAa;AAC3C,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU;AACf,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,WAAW;AAClB,WAAK,kBAAkB;AAAA,IACzB;AACA,SAAK,sBAAsB,SAAS;AAAA,EACtC;AAAA,EACA,UAAU;AACR,QAAI,KAAK,aAAa,KAAK,QAAQ,gBAAgB;AACjD,WAAK,kBAAkB;AACvB,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,UAAU;AACR,QAAI,KAAK,aAAa,KAAK,QAAQ,gBAAgB;AACjD,WAAK,kBAAkB;AAAA,IACzB;AAAA,EACF;AAAA,EACA,QAAQ,aAAa,OAAO;AAC1B,SAAK,aAAa;AAClB,QAAI,KAAK,QAAQ,WAAW;AAC1B,WAAK,SAAS,QAAQ;AACtB,WAAK,IAAI,cAAc;AACvB,WAAK,aAAa,WAAW,MAAM;AACjC,aAAK,aAAa;AAClB,aAAK,UAAU,KAAK;AAAA,UAClB,IAAI,KAAK,SAAS;AAAA,UAClB;AAAA,QACF,CAAC;AAAA,MACH,GAAG,GAAG;AAAA,IACR,OAAO;AACL,WAAK,UAAU,KAAK;AAAA,QAClB,IAAI,KAAK,SAAS;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,SAAK,WAAW,KAAK,QAAQ;AAC7B,SAAK,mBAAmB,KAAK,IAAI;AAAA,EACnC;AAAA,EACA,YAAY;AACV,QAAI,KAAK,WAAW;AAClB,WAAK,YAAY,KAAK,IAAI,IAAI,KAAK;AAAA,IACrC;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,WAAW,GAAG;AACrB,WAAK,kBAAkB;AACvB,WAAK,aAAa,WAAW,MAAM,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAChE,WAAK,mBAAmB,KAAK,IAAI;AAAA,IACnC,OAAO;AACL,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,eAAe,MAAM;AAC5B,mBAAa,KAAK,UAAU;AAC5B,WAAK,aAAa;AAAA,IACpB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAkB,kBAAqB,iBAAiB,CAAC;AAAA,IAC5E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,qBAAN,MAAM,4BAA2B,cAAc;AAAA,EAC7C,YAAY,KAAK;AACf,UAAM,GAAG;AACT,SAAK,YAAY,IAAI,aAAa;AAAA,EACpC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAuB,kBAAqB,iBAAiB,CAAC;AAAA,IACjF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,WAAW;AAAA,MACb;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,sBAAsB,GAAG,cAAc,YAAY,GAAG,CAAC,GAAG,4BAA4B,GAAG,CAAC,GAAG,8BAA8B,GAAG,SAAS,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,WAAW,IAAI,UAAU,gBAAgB,GAAG,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,eAAe,GAAG,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,sBAAsB,GAAG,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,gBAAgB,GAAG,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,WAAW,GAAG,cAAc,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,UAAU,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,aAAa,GAAG,CAAC,WAAW,IAAI,UAAU,oBAAoB,GAAG,CAAC,WAAW,IAAI,UAAU,cAAc,GAAG,CAAC,WAAW,IAAI,UAAU,SAAS,GAAG,CAAC,GAAG,WAAW,CAAC;AAAA,MACpuB,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,sBAAsB,SAAS,uEAAuE,QAAQ;AAC1H,mBAAO,IAAI,sBAAsB,KAAK,MAAM;AAAA,UAC9C,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,cAAc,SAAS,wDAAwD;AAChF,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC;AACD,UAAG,eAAe,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AAC1C,UAAG,wBAAwB,GAAG,CAAC;AAC/B,UAAG,WAAW,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,oCAAoC,GAAG,GAAG,QAAQ,CAAC;AACpS,UAAG,sBAAsB;AACzB,UAAG,WAAW,GAAG,4CAA4C,GAAG,GAAG,gBAAgB,CAAC;AACpF,UAAG,aAAa,EAAE,EAAE;AAAA,QACtB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,iBAAiB,IAAI,SAAS,KAAK;AACjD,UAAG,UAAU,CAAC;AACd,UAAG,WAAW,WAAW,iBAAiB,IAAI,SAAS,IAAI;AAC3D,UAAG,UAAU;AACb,UAAG,WAAW,YAAY,IAAI,SAAS,IAAI;AAC3C,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,SAAS;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,MAAM;AACpC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,SAAS;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,OAAO;AACrC,UAAG,UAAU;AACb,UAAG,WAAW,gBAAgB,SAAS;AACvC,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,SAAS,OAAO;AAAA,QAC9D;AAAA,MACF;AAAA,MACA,cAAc,CAAC,SAAS,UAAU,cAAc,cAAmB,iBAAiB,gBAAmB,+BAA+B;AAAA,MACtI,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,YAAY;AAAA,MAC1B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,YAAY,CAAC,YAAY;AAAA,MACzB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAwBV,SAAS,CAAC,SAAS,UAAU,cAAc,cAAc,cAAc;AAAA,MACvE,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAA2B;AACjC,IAAM,4BAA4B;AAAA,EAChC,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,aAAa;AACf;AACA,IAAM,8BAAN,MAAM,qCAAoC,uBAAuB;AAAA,EAC/D,YAAY,KAAK,iBAAiB;AAChC,UAAM,KAAK,eAAe;AAC1B,SAAK,MAAM;AACX,UAAM,SAAS,KAAK,gBAAgB,sBAAsB,wBAAwB;AAClF,SAAK,MAAM,QAAQ,eAAe;AAAA,EACpC;AAAA,EACA,wBAAwB;AACtB,SAAK,gBAAgB,iCAAiC,wBAAwB,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC7H,WAAK,aAAa;AAClB,YAAM,SAAS,KAAK,gBAAgB,sBAAsB,wBAAwB;AAClF,UAAI,QAAQ;AACV,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,aAAK,MAAM,eAAe,KAAK;AAAA,MACjC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AACb,SAAK,SAAS,iDACT,4BACA,KAAK,SACL,KAAK,gBAAgB,sBAAsB,wBAAwB;AAExE,SAAK,MAAM,WAAW,KAAK,OAAO,KAAK;AACvC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,oCAAoC,GAAG;AAC1D,aAAO,KAAK,KAAK,8BAAgC,kBAAqB,iBAAiB,GAAM,kBAAqB,eAAe,CAAC;AAAA,IACpI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,sBAAsB,CAAC;AAAA,MACpC,UAAU,CAAC,oBAAoB;AAAA,MAC/B,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,mBAAmB;AAAA,MAChE,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,GAAG,YAAY,aAAa,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,aAAa,UAAU,CAAC;AAAA,MAC9G,UAAU,SAAS,qCAAqC,IAAI,KAAK;AAC/D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,mDAAmD,GAAG,GAAG,cAAc,CAAC;AACzF,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,OAAO,IAAI,GAAG;AAC7B,UAAG,YAAY,mBAAmB,IAAI,QAAQ,KAAK;AACnD,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,SAAS;AAAA,QACxC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,oBAAoB,OAAO;AAAA,MAC1C,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,6BAA6B,CAAC;AAAA,IACpG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASV,SAAS,CAAC,oBAAoB,OAAO;AAAA,MACrC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,6BAA6B,kBAAkB;AAAA,IAC3D,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,6BAA6B,kBAAkB;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,6BAA6B,kBAAkB;AAAA,IAC3D,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,mBAAN,MAAM,0BAAyB,YAAY;AAAA,EACzC,YAAY,oBAAoB,SAAS,UAAU;AACjD,UAAM,oBAAoB,SAAS,QAAQ;AAC3C,SAAK,kBAAkB;AAAA,EACzB;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,eAAe;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,MAAM,SAAS,SAAS;AACtB,WAAO,KAAK,eAAe;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,KAAK,SAAS,SAAS;AACrB,WAAO,KAAK,eAAe;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,eAAe;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,eAAe;AAAA,MACzB,MAAM;AAAA,MACN;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,OAAO,MAAM,SAAS,SAAS;AAC7B,WAAO,KAAK,eAAe;AAAA,MACzB;AAAA,MACA;AAAA,IACF,GAAG,OAAO;AAAA,EACZ;AAAA,EACA,eAAe,SAAS,SAAS;AAC/B,SAAK,YAAY,KAAK,cAAc,2BAA2B;AAC/D,WAAO,KAAK,UAAU,OAAO,kCACxB,UACA;AAAA,MACD,WAAW,oBAAI,KAAK;AAAA,MACpB,WAAW,KAAK,cAAc;AAAA,MAC9B;AAAA,IACF,EACD;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,SAAc,kBAAkB,GAAM,SAAc,OAAO,GAAM,SAAY,QAAQ,CAAC;AAAA,IAC9H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;", "names": []}