{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-divider.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, Input, NgModule } from '@angular/core';\nimport * as i1 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nfunction NzDividerComponent_Conditional_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzText);\n  }\n}\nfunction NzDividerComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 0);\n    i0.ɵɵtemplate(1, NzDividerComponent_Conditional_0_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzText);\n  }\n}\nclass NzDividerComponent {\n  constructor() {\n    this.nzType = 'horizontal';\n    this.nzOrientation = 'center';\n    this.nzDashed = false;\n    this.nzPlain = false;\n  }\n  static {\n    this.ɵfac = function NzDividerComponent_Factory(t) {\n      return new (t || NzDividerComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzDividerComponent,\n      selectors: [[\"nz-divider\"]],\n      hostAttrs: [1, \"ant-divider\"],\n      hostVars: 16,\n      hostBindings: function NzDividerComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-divider-horizontal\", ctx.nzType === \"horizontal\")(\"ant-divider-vertical\", ctx.nzType === \"vertical\")(\"ant-divider-with-text\", ctx.nzText)(\"ant-divider-plain\", ctx.nzPlain)(\"ant-divider-with-text-left\", ctx.nzText && ctx.nzOrientation === \"left\")(\"ant-divider-with-text-right\", ctx.nzText && ctx.nzOrientation === \"right\")(\"ant-divider-with-text-center\", ctx.nzText && ctx.nzOrientation === \"center\")(\"ant-divider-dashed\", ctx.nzDashed);\n        }\n      },\n      inputs: {\n        nzText: \"nzText\",\n        nzType: \"nzType\",\n        nzOrientation: \"nzOrientation\",\n        nzDashed: \"nzDashed\",\n        nzPlain: \"nzPlain\"\n      },\n      exportAs: [\"nzDivider\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[1, \"ant-divider-inner-text\"], [4, \"nzStringTemplateOutlet\"]],\n      template: function NzDividerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzDividerComponent_Conditional_0_Template, 2, 1, \"span\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.nzText ? 0 : -1);\n        }\n      },\n      dependencies: [NzOutletModule, i1.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzDividerComponent.prototype, \"nzDashed\", void 0);\n__decorate([InputBoolean()], NzDividerComponent.prototype, \"nzPlain\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDividerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-divider',\n      exportAs: 'nzDivider',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (nzText) {\n      <span class=\"ant-divider-inner-text\">\n        <ng-container *nzStringTemplateOutlet=\"nzText\">{{ nzText }}</ng-container>\n      </span>\n    }\n  `,\n      host: {\n        class: 'ant-divider',\n        '[class.ant-divider-horizontal]': `nzType === 'horizontal'`,\n        '[class.ant-divider-vertical]': `nzType === 'vertical'`,\n        '[class.ant-divider-with-text]': `nzText`,\n        '[class.ant-divider-plain]': `nzPlain`,\n        '[class.ant-divider-with-text-left]': `nzText && nzOrientation === 'left'`,\n        '[class.ant-divider-with-text-right]': `nzText && nzOrientation === 'right'`,\n        '[class.ant-divider-with-text-center]': `nzText && nzOrientation === 'center'`,\n        '[class.ant-divider-dashed]': `nzDashed`\n      },\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], () => [], {\n    nzText: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzOrientation: [{\n      type: Input\n    }],\n    nzDashed: [{\n      type: Input\n    }],\n    nzPlain: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDividerModule {\n  static {\n    this.ɵfac = function NzDividerModule_Factory(t) {\n      return new (t || NzDividerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzDividerModule,\n      imports: [NzDividerComponent],\n      exports: [NzDividerComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzDividerComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDividerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDividerComponent],\n      exports: [NzDividerComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzDividerComponent, NzDividerModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,SAAS,yDAAyD,IAAI,KAAK;AACzE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,MAAM;AAAA,EACpC;AACF;AACA,SAAS,0CAA0C,IAAI,KAAK;AAC1D,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,0DAA0D,GAAG,GAAG,gBAAgB,CAAC;AAClG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,MAAM;AAAA,EACvD;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,SAAS;AACd,SAAK,gBAAgB;AACrB,SAAK,WAAW;AAChB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,GAAG;AACjD,aAAO,KAAK,KAAK,qBAAoB;AAAA,IACvC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,CAAC;AAAA,MAC1B,WAAW,CAAC,GAAG,aAAa;AAAA,MAC5B,UAAU;AAAA,MACV,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,0BAA0B,IAAI,WAAW,YAAY,EAAE,wBAAwB,IAAI,WAAW,UAAU,EAAE,yBAAyB,IAAI,MAAM,EAAE,qBAAqB,IAAI,OAAO,EAAE,8BAA8B,IAAI,UAAU,IAAI,kBAAkB,MAAM,EAAE,+BAA+B,IAAI,UAAU,IAAI,kBAAkB,OAAO,EAAE,gCAAgC,IAAI,UAAU,IAAI,kBAAkB,QAAQ,EAAE,sBAAsB,IAAI,QAAQ;AAAA,QACxc;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,eAAe;AAAA,QACf,UAAU;AAAA,QACV,SAAS;AAAA,MACX;AAAA,MACA,UAAU,CAAC,WAAW;AAAA,MACtB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAAA,MACrE,UAAU,SAAS,4BAA4B,IAAI,KAAK;AACtD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,2CAA2C,GAAG,GAAG,QAAQ,CAAC;AAAA,QAC7E;AACA,YAAI,KAAK,GAAG;AACV,UAAG,cAAc,GAAG,IAAI,SAAS,IAAI,EAAE;AAAA,QACzC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,gBAAmB,+BAA+B;AAAA,MACjE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,YAAY,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,mBAAmB,WAAW,WAAW,MAAM;AAAA,CAC3E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,kCAAkC;AAAA,QAClC,gCAAgC;AAAA,QAChC,iCAAiC;AAAA,QACjC,6BAA6B;AAAA,QAC7B,sCAAsC;AAAA,QACtC,uCAAuC;AAAA,QACvC,wCAAwC;AAAA,QACxC,8BAA8B;AAAA,MAChC;AAAA,MACA,SAAS,CAAC,cAAc;AAAA,MACxB,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB;AAAA,MAC5B,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB;AAAA,MAC5B,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}