namespace ProjectApp.Core.Enums
{
    public enum AgentSourceType
    {
        None = 0,
        Database = 1,
        Wikipedia = 2,
        FileSystem = 3,
        WebApi = 4,
        DocumentLibrary = 5
    }
    
    public enum DatabaseType
    {
        SqlServer = 1,
        MySQL = 2,
        PostgreSQL = 3,
        Oracle = 4,
        SQLite = 5
    }
    
    public enum SchemaExtractionStatus
    {
        NotStarted = 0,
        InProgress = 1,
        Success = 2,
        Failed = 3
    }
}
