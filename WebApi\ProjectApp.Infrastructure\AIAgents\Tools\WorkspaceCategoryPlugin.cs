﻿using Dapper;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System.ComponentModel;
using System.Data;

namespace ProjectApp.Infrastructure.AIAgents.Tools;

public class WorkspaceCategoryPlugin(IWorkspaceRepository _workspaceRepository,
        IProjectCategoryRepository _projectCategoryRepository,
        IDbConnection _dbConnection,
        IKernelMemory _memory)
{
    [KernelFunction("get_workspace_project_memory")]
    [Description("Gets workspace project memory with document IDs")]
    public async Task<List<ProjectMemory>> GetWorkspaceProjectMemoryAsync(List<string> documentIds)
    {
        string sqlQuery = "SELECT Id, Workspace, ProjectDescription, ProjectCategory FROM [dbo].[ProjectMemories] WHERE Id IN @Ids AND IsProjectManagement = 1";
        var workspaces = await _dbConnection.QueryAsync<ProjectMemory>(sqlQuery, new { Ids = documentIds });
        return workspaces.ToList();
    }

    [KernelFunction("get_workspace_by_title")]
    [Description("Gets workspace details by title")]
    public async Task<WorkspaceDto> GetWorkspaceByTitleAsync(string title)
    {
        return await _workspaceRepository.GetByTitle(title);
    }

    [KernelFunction("get_project_category_by_workspace_id")]
    [Description("Gets project category by name")]
    public async Task<ProjectCategory> GetProjectCategoryByName(string categoryName)
    {
        return await _projectCategoryRepository.GetByNameAsync(categoryName);
    }

}

public class WorkspaceGenerationResult
{
    public bool Success { get; set; }
    public string WorkspaceName { get; set; }
    public string ProjectCategory { get; set; }
}
