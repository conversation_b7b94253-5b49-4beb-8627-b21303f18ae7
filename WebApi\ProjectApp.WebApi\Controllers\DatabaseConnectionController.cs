using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class DatabaseConnectionController : ControllerBase
    {
        private readonly IDatabaseConnectionRepository _connectionRepository;
        private readonly DatabaseSchemaService _schemaService;
        private readonly ILogger<DatabaseConnectionController> _logger;

        public DatabaseConnectionController(
            IDatabaseConnectionRepository connectionRepository,
            DatabaseSchemaService schemaService,
            ILogger<DatabaseConnectionController> logger)
        {
            _connectionRepository = connectionRepository;
            _schemaService = schemaService;
            _logger = logger;
        }

        /// <summary>
        /// Get all database connections
        /// </summary>
        [HttpGet("GetAll")]
        public async Task<ActionResult<List<DatabaseConnectionDto>>> GetAll()
        {
            try
            {
                var connections = await _connectionRepository.GetAllAsync();
                return Ok(connections);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving database connections");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get database connection by ID
        /// </summary>
        [HttpGet("GetById/{id}")]
        public async Task<ActionResult<DatabaseConnectionDto>> GetById(Guid id)
        {
            try
            {
                var connection = await _connectionRepository.GetByIdAsync(id);
                if (connection == null)
                    return NotFound($"Database connection with ID {id} not found");

                return Ok(connection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving database connection {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get database connection by name
        /// </summary>
        [HttpGet("GetByName/{name}")]
        public async Task<ActionResult<DatabaseConnectionDto>> GetByName(string name)
        {
            try
            {
                var connection = await _connectionRepository.GetByNameAsync(name);
                if (connection == null)
                    return NotFound($"Database connection with name '{name}' not found");

                return Ok(connection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving database connection {Name}", name);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Get all active database connections
        /// </summary>
        [HttpGet("GetActive")]
        public async Task<ActionResult<List<DatabaseConnectionDto>>> GetActive()
        {
            try
            {
                var connections = await _connectionRepository.GetActiveConnectionsAsync();
                return Ok(connections);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active database connections");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Create a new database connection
        /// </summary>
        [HttpPost("Create")]
        public async Task<ActionResult<DatabaseConnectionDto>> Create([FromBody] CreateDatabaseConnectionDto dto)
        {
            try
            {
                // Check if connection name already exists
                if (await _connectionRepository.ExistsAsync(dto.Name))
                {
                    return BadRequest($"Database connection with name '{dto.Name}' already exists");
                }

                var connection = await _connectionRepository.CreateAsync(dto);
                return CreatedAtAction(nameof(GetById), new { id = connection.Id }, connection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating database connection");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Update an existing database connection
        /// </summary>
        [HttpPut("Update/{id}")]
        public async Task<ActionResult<DatabaseConnectionDto>> Update(Guid id, [FromBody] UpdateDatabaseConnectionDto dto)
        {
            try
            {
                if (id != dto.Id)
                    return BadRequest("ID mismatch");

                var existingConnection = await _connectionRepository.GetByIdAsync(id);
                if (existingConnection == null)
                    return NotFound($"Database connection with ID {id} not found");

                var connection = await _connectionRepository.UpdateAsync(dto);
                return Ok(connection);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating database connection {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Delete a database connection
        /// </summary>
        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            try
            {
                var existingConnection = await _connectionRepository.GetByIdAsync(id);
                if (existingConnection == null)
                    return NotFound($"Database connection with ID {id} not found");

                var deleted = await _connectionRepository.DeleteAsync(id);
                if (!deleted)
                    return StatusCode(500, "Failed to delete database connection");

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting database connection {Id}", id);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Test database connection
        /// </summary>
        [HttpPost("TestConnection")]
        public async Task<ActionResult<ResponseMessage>> TestConnection([FromBody] TestConnectionDto dto)
        {
            try
            {
                var result = await _connectionRepository.TestConnectionAsync(dto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing database connection");
                return Ok(new ResponseMessage 
                { 
                    IsError = true, 
                    Message = $"Connection test failed: {ex.Message}" 
                });
            }
        }

        /// <summary>
        /// Extract and store database schema
        /// </summary>
        [HttpPost("ExtractSchema/{id}")]
        public async Task<ActionResult<SchemaExtractionResponseDto>> ExtractSchema(Guid id, [FromBody] SchemaExtractionRequestDto? dto = null)
        {
            try
            {
                var existingConnection = await _connectionRepository.GetByIdAsync(id);
                if (existingConnection == null)
                    return NotFound($"Database connection with ID {id} not found");

                var forceRefresh = dto?.ForceRefresh ?? false;
                var result = await _schemaService.ExtractAndStoreSchemaAsync(id, forceRefresh);
                
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting schema for connection {Id}", id);
                return Ok(new SchemaExtractionResponseDto
                {
                    Success = false,
                    Message = $"Schema extraction failed: {ex.Message}",
                    ExtractionTime = DateTime.UtcNow
                });
            }
        }

        /// <summary>
        /// Get schema context for a connection (for AI agents)
        /// </summary>
        [HttpGet("GetSchemaContext/{name}")]
        public async Task<ActionResult<string>> GetSchemaContext(string name)
        {
            try
            {
                var context = await _schemaService.GetSchemaContextAsync(name);
                return Ok(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving schema context for connection {Name}", name);
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
