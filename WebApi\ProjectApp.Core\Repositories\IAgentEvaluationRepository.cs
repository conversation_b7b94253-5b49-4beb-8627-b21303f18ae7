using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IAgentEvaluationRepository
    {
        Task<IEnumerable<AgentEvaluation>> GetAllAsync();
        
        Task<IEnumerable<AgentEvaluation>> GetByAgentNameAsync(string agentName);
        
        Task<AgentEvaluation> GetByIdAsync(Guid id);
        
        Task<Guid> CreateAsync(AgentEvaluation agentEvaluation);
        
        Task<bool> UpdateAsync(AgentEvaluation agentEvaluation);
        
        Task<bool> DeleteAsync(Guid id);
        
        Task<double> GetAverageScoreByAgentNameAsync(string agentName);
    }
}
