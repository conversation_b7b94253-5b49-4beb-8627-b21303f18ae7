{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-cdk-resize-observer.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Factory that creates a new ResizeObserver and allows us to stub it out in unit tests.\n */\nclass NzResizeObserverFactory {\n  create(callback) {\n    return typeof ResizeObserver === 'undefined' ? null : new ResizeObserver(callback);\n  }\n  static {\n    this.ɵfac = function NzResizeObserverFactory_Factory(t) {\n      return new (t || NzResizeObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserverFactory,\n      factory: NzResizeObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass NzResizeObserver {\n  constructor(nzResizeObserverFactory) {\n    this.nzResizeObserverFactory = nzResizeObserverFactory;\n    /** Keeps track of the existing ResizeObservers so they can be reused. */\n    this.observedElements = new Map();\n  }\n  ngOnDestroy() {\n    this.observedElements.forEach((_, element) => this.cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this.observeElement(element);\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n        this.unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing ResizeObserver if available, or creating a\n   * new one if not.\n   */\n  observeElement(element) {\n    if (!this.observedElements.has(element)) {\n      const stream = new Subject();\n      const observer = this.nzResizeObserverFactory.create(mutations => stream.next(mutations));\n      if (observer) {\n        observer.observe(element);\n      }\n      this.observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this.observedElements.get(element).count++;\n    }\n    return this.observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying ResizeObserver if nobody else is\n   * observing this element.\n   */\n  unobserveElement(element) {\n    if (this.observedElements.has(element)) {\n      this.observedElements.get(element).count--;\n      if (!this.observedElements.get(element).count) {\n        this.cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying ResizeObserver for the specified element. */\n  cleanupObserver(element) {\n    if (this.observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this.observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this.observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserver_Factory(t) {\n      return new (t || NzResizeObserver)(i0.ɵɵinject(NzResizeObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserver,\n      factory: NzResizeObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: NzResizeObserverFactory\n  }], null);\n})();\nclass NzResizeObserverDirective {\n  subscribe() {\n    this.unsubscribe();\n    this.currentSubscription = this.nzResizeObserver.observe(this.elementRef).subscribe(this.nzResizeObserve);\n  }\n  unsubscribe() {\n    this.currentSubscription?.unsubscribe();\n  }\n  constructor(nzResizeObserver, elementRef) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.elementRef = elementRef;\n    this.nzResizeObserve = new EventEmitter();\n    this.nzResizeObserverDisabled = false;\n    this.currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this.currentSubscription && !this.nzResizeObserverDisabled) {\n      this.subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzResizeObserve\n    } = changes;\n    if (nzResizeObserve) {\n      if (this.nzResizeObserverDisabled) {\n        this.unsubscribe();\n      } else {\n        this.subscribe();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserverDirective_Factory(t) {\n      return new (t || NzResizeObserverDirective)(i0.ɵɵdirectiveInject(NzResizeObserver), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzResizeObserverDirective,\n      selectors: [[\"\", \"nzResizeObserver\", \"\"]],\n      inputs: {\n        nzResizeObserverDisabled: \"nzResizeObserverDisabled\"\n      },\n      outputs: {\n        nzResizeObserve: \"nzResizeObserve\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzResizeObserverFactory]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzResizeObserverDirective.prototype, \"nzResizeObserverDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzResizeObserver]',\n      standalone: true,\n      providers: [NzResizeObserverFactory]\n    }]\n  }], () => [{\n    type: NzResizeObserver\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzResizeObserve: [{\n      type: Output\n    }],\n    nzResizeObserverDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzResizeObserverModule {\n  static {\n    this.ɵfac = function NzResizeObserverModule_Factory(t) {\n      return new (t || NzResizeObserverModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzResizeObserverModule,\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzResizeObserver, NzResizeObserverDirective, NzResizeObserverFactory, NzResizeObserverModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,UAAU;AACf,WAAO,OAAO,mBAAmB,cAAc,OAAO,IAAI,eAAe,QAAQ;AAAA,EACnF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,GAAG;AACtD,aAAO,KAAK,KAAK,0BAAyB;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,MACjC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAEH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,YAAY,yBAAyB;AACnC,SAAK,0BAA0B;AAE/B,SAAK,mBAAmB,oBAAI,IAAI;AAAA,EAClC;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,QAAQ,CAAC,GAAG,YAAY,KAAK,gBAAgB,OAAO,CAAC;AAAA,EAC7E;AAAA,EACA,QAAQ,cAAc;AACpB,UAAM,UAAU,cAAc,YAAY;AAC1C,WAAO,IAAI,WAAW,cAAY;AAChC,YAAM,SAAS,KAAK,eAAe,OAAO;AAC1C,YAAM,eAAe,OAAO,UAAU,QAAQ;AAC9C,aAAO,MAAM;AACX,qBAAa,YAAY;AACzB,aAAK,iBAAiB,OAAO;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,SAAS;AACtB,QAAI,CAAC,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACvC,YAAM,SAAS,IAAI,QAAQ;AAC3B,YAAM,WAAW,KAAK,wBAAwB,OAAO,eAAa,OAAO,KAAK,SAAS,CAAC;AACxF,UAAI,UAAU;AACZ,iBAAS,QAAQ,OAAO;AAAA,MAC1B;AACA,WAAK,iBAAiB,IAAI,SAAS;AAAA,QACjC;AAAA,QACA;AAAA,QACA,OAAO;AAAA,MACT,CAAC;AAAA,IACH,OAAO;AACL,WAAK,iBAAiB,IAAI,OAAO,EAAE;AAAA,IACrC;AACA,WAAO,KAAK,iBAAiB,IAAI,OAAO,EAAE;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB,SAAS;AACxB,QAAI,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACtC,WAAK,iBAAiB,IAAI,OAAO,EAAE;AACnC,UAAI,CAAC,KAAK,iBAAiB,IAAI,OAAO,EAAE,OAAO;AAC7C,aAAK,gBAAgB,OAAO;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAAA;AAAA,EAEA,gBAAgB,SAAS;AACvB,QAAI,KAAK,iBAAiB,IAAI,OAAO,GAAG;AACtC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,KAAK,iBAAiB,IAAI,OAAO;AACrC,UAAI,UAAU;AACZ,iBAAS,WAAW;AAAA,MACtB;AACA,aAAO,SAAS;AAChB,WAAK,iBAAiB,OAAO,OAAO;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,SAAS,uBAAuB,CAAC;AAAA,IACzE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,kBAAiB;AAAA,MAC1B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY;AACV,SAAK,YAAY;AACjB,SAAK,sBAAsB,KAAK,iBAAiB,QAAQ,KAAK,UAAU,EAAE,UAAU,KAAK,eAAe;AAAA,EAC1G;AAAA,EACA,cAAc;AACZ,SAAK,qBAAqB,YAAY;AAAA,EACxC;AAAA,EACA,YAAY,kBAAkB,YAAY;AACxC,SAAK,mBAAmB;AACxB,SAAK,aAAa;AAClB,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,2BAA2B;AAChC,SAAK,sBAAsB;AAAA,EAC7B;AAAA,EACA,qBAAqB;AACnB,QAAI,CAAC,KAAK,uBAAuB,CAAC,KAAK,0BAA0B;AAC/D,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,QAAI,iBAAiB;AACnB,UAAI,KAAK,0BAA0B;AACjC,aAAK,YAAY;AAAA,MACnB,OAAO;AACL,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,GAAG;AACxD,aAAO,KAAK,KAAK,4BAA8B,kBAAkB,gBAAgB,GAAM,kBAAqB,UAAU,CAAC;AAAA,IACzH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,oBAAoB,EAAE,CAAC;AAAA,MACxC,QAAQ;AAAA,QACN,0BAA0B;AAAA,MAC5B;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,MACA,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,uBAAuB,CAAC,GAAM,oBAAoB;AAAA,IACtF,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,0BAA0B,WAAW,4BAA4B,MAAM;AAAA,CACnG,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,WAAW,CAAC,uBAAuB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,0BAA0B,CAAC;AAAA,MACzB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,GAAG;AACrD,aAAO,KAAK,KAAK,yBAAwB;AAAA,IAC3C;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,yBAAyB;AAAA,MACnC,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,yBAAyB;AAAA,MACnC,SAAS,CAAC,yBAAyB;AAAA,IACrC,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}