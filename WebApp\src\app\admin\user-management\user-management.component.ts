import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { UserAccountServiceProxy, UserDto } from '../../../shared/service-proxies/service-proxies';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [FormsModule, CommonModule, NzDropDownModule, SpinnerComponent],
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css'],
})
export class UsersManagementComponent {
  showAddUserDialog = false;
  filteredUsers: UserDto[] = [];
  paginatedUsers: UserDto[] = [];
  searchTerm = '';
  selectedRole = '';
  isLoading = false; // Loading state for spinner
  newUser: any = { name: '', email: '', role: '', skills: '', password: '' };

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  previousPageSize: number = 10;

  Math = Math;

  users: any = [];

  constructor(
    private userAccountService: UserAccountServiceProxy,
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.loadUsers();
  }

  loadUsers() {
    this.isLoading = true; // Show spinner
    this.userAccountService.getAll().subscribe({
      next: (users: any) => {
        this.users = users;
        console.log(this.users);
        this.updateFilteredUsers();
        this.isLoading = false; // Hide spinner
      },
      error: (error: any) => {
        console.error('Error loading users:', error);
        this.message.error('Failed to load users');
        this.isLoading = false; // Hide spinner on error
      }
    });
  }

  updateFilteredUsers() {
    let result = [...this.users];

    // Search filter
    if (this.searchTerm) {
      result = result.filter(
        (user) =>
          user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    }

    // Role filter
    if (this.selectedRole) {
      result = result.filter((user) => user.roles.includes(this.selectedRole));
    }

    this.filteredUsers = result;
    this.updatePagination();
  }

  get filteredUsersGetter() {
    return this.filteredUsers;
  }

  toggleRole(user: any) {
    let role = user.role === 'User' ? 'Admin' : 'User';
    // Update the user in the backend
    this.userAccountService.assignRole(user.email, role).subscribe({
      next: () => {
        console.log('Role toggled for:', user.name, 'to:', user.role);
        this.message.success('Role updated successfully!');
        this.loadUsers();
      },
      error: (error) => {
        console.error('Error updating role:', error);
        this.message.error('Failed to update role');
      }
    });
  }

  // Method to trigger filtering (used by UI elements)
  filterUsers() {
    this.updateFilteredUsers();
  }

  addUser() {
    if (this.newUser.name && this.newUser.email && this.newUser.role && this.newUser.password) {
      this.userAccountService.register(this.newUser).subscribe({
        next: (res: any) => {
          this.message.success('User added successfully!');
          this.loadUsers();
          this.showAddUserDialog = false;
          console.log(res);
          this.resetFields();
        },
        error: (error) => {
          console.error('Error adding user:', error);
          this.message.error('Failed to add user');
        }
      });
    } else {
      this.message.warning('Please fill all required fields');
    }
  }

  resetFields() {
    this.newUser = {
      name: '',
      email: '',
      role: '',
      skills: '',
      password: '',
    };
  }

  deleteUser(user: any) {
    this.userAccountService.deleteUser(user.email).subscribe({
      next: (res) => {
        this.message.success('User deleted successfully!');
        this.loadUsers();
        console.log('User deleted:', res);
      },
      error: (error) => {
        console.error('Error deleting user:', error);
        this.message.error('Failed to delete user');
      }
    });
  }

  generatePassword() {
    let pass = Math.random().toString(36).slice(-8);
    this.newUser.password = pass;
  }

  assignRole(user: any, role: string) {
    this.userAccountService
      .assignRole(user.email, role)
      .subscribe({
        next: (res: any) => {
          this.message.success('Role assigned successfully!');
          this.loadUsers();
          console.log(res);
        },
        error: (error) => {
          console.error('Error assigning role:', error);
          this.message.error('Failed to assign role');
        }
      });
  }

  removeRole(user: any, role: string) {
    this.userAccountService
      .removeRole(user.email, role)
      .subscribe({
        next: (res: any) => {
          this.message.success('Role removed successfully!');
          this.loadUsers();
          console.log(res);
        },
        error: (error) => {
          console.error('Error removing role:', error);
          this.message.error('Failed to remove role');
        }
      });
  }

  showPassword = false;

  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(1, Math.ceil(this.filteredUsers.length / this.pageSize));

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Get current page of users
    if (this.filteredUsers.length === 0) {
      this.paginatedUsers = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredUsers.length);
      this.paginatedUsers = this.filteredUsers.slice(startIndex, endIndex);
    }

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredUsers.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedUsers.length
    });
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }
}
