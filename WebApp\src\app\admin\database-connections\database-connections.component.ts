import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import {
  DatabaseConnectionServiceProxy,
  DatabaseConnectionDto,
  CreateDatabaseConnectionDto,
  UpdateDatabaseConnectionDto,
  TestConnectionDto,
  SchemaExtractionRequestDto
} from '../../../shared/service-proxies/service-proxies';
import { SpinnerComponent } from '../../shared/components/spinner/spinner.component';

@Component({
  selector: 'app-database-connections',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzTableModule,
    NzButtonModule,
    NzModalModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzPopconfirmModule,
    NzTagModule,
    NzIconModule,
    NzSpinModule,
    NzCheckboxModule,
    SpinnerComponent
  ],
  templateUrl: './database-connections.component.html',
  styleUrls: ['./database-connections.component.css']
})
export class DatabaseConnectionsComponent implements OnInit {
  connections: DatabaseConnectionDto[] = [];
  filteredConnections: DatabaseConnectionDto[] = [];
  paginatedConnections: DatabaseConnectionDto[] = [];
  loading = false;
  isModalVisible = false;
  isEditMode = false;
  connectionForm: FormGroup;
  selectedConnection?: DatabaseConnectionDto;
  searchTerm = '';

  // Test connection functionality
  testingConnection = false;
  testResult: { success: boolean; message: string } | null = null;

  // Pagination properties
  currentPage: number = 1;
  pageSize: number = 10;
  totalPages: number = 1;
  previousPageSize: number = 10;

  Math = Math;

  databaseTypes = [
    { label: 'SQL Server', value: 'SqlServer' },
    { label: 'MySQL', value: 'MySQL' },
    { label: 'PostgreSQL', value: 'PostgreSQL' },
    { label: 'Oracle', value: 'Oracle' },
    { label: 'SQLite', value: 'SQLite' }
  ];

  constructor(
    private fb: FormBuilder,
    private message: NzMessageService,
    private databaseConnectionService: DatabaseConnectionServiceProxy
  ) {
    this.connectionForm = this.fb.group({
      name: ['', [Validators.required, Validators.maxLength(100)]],
      databaseType: ['SqlServer', [Validators.required]],
      connectionString: ['', [Validators.required]],
      description: ['', [Validators.maxLength(500)]],
      isActive: [true]
    });
  }

  ngOnInit(): void {
    this.loadConnections();
  }

  loadConnections(): void {
    this.loading = true;
    this.databaseConnectionService.getAll().subscribe({
      next: (connections) => {
        this.connections = connections;
        this.updateFilteredConnections();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading connections:', error);
        this.message.error('Failed to load database connections');
        this.loading = false;
      }
    });
  }

  updateFilteredConnections(): void {
    let result = [...this.connections];

    // Search filter
    if (this.searchTerm.trim()) {
      const term = this.searchTerm.toLowerCase();
      result = result.filter(connection =>
        connection.name.toLowerCase().includes(term) ||
        connection.databaseType.toLowerCase().includes(term) ||
        (connection.description && connection.description.toLowerCase().includes(term))
      );
    }

    this.filteredConnections = result;
    this.updatePagination();
  }

  get filteredConnectionsGetter() {
    return this.filteredConnections;
  }

  filterConnections(): void {
    this.updateFilteredConnections();
  }

  updatePagination() {
    // Ensure pageSize is a number
    this.pageSize = Number(this.pageSize);

    // Check if page size has changed
    const pageSizeChanged = this.previousPageSize !== this.pageSize;

    // Calculate total pages (minimum 1 page)
    this.totalPages = Math.max(1, Math.ceil(this.filteredConnections.length / this.pageSize));

    // Reset to page 1 when page size changes
    if (pageSizeChanged) {
      this.currentPage = 1;
      console.log('Page size changed from', this.previousPageSize, 'to', this.pageSize, '- resetting to page 1');
    }

    // Ensure current page is within bounds
    if (this.currentPage < 1) this.currentPage = 1;
    if (this.currentPage > this.totalPages) this.currentPage = this.totalPages;

    // Get current page of connections
    if (this.filteredConnections.length === 0) {
      this.paginatedConnections = [];
    } else {
      const startIndex = (this.currentPage - 1) * this.pageSize;
      const endIndex = Math.min(startIndex + this.pageSize, this.filteredConnections.length);
      this.paginatedConnections = this.filteredConnections.slice(startIndex, endIndex);
    }

    // Store current page size for next comparison
    this.previousPageSize = this.pageSize;

    // Log pagination state for debugging
    console.log('Pagination updated:', {
      totalItems: this.filteredConnections.length,
      pageSize: this.pageSize,
      totalPages: this.totalPages,
      currentPage: this.currentPage,
      itemsOnCurrentPage: this.paginatedConnections.length
    });
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.updatePagination();
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.updatePagination();
    }
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  trackByConnectionId(_: number, connection: DatabaseConnectionDto): string {
    return connection.id;
  }

  getStatusClasses(connection: DatabaseConnectionDto): string {
    if (!connection.isSchemaExtracted) {
      return 'bg-gray-100 text-gray-800';
    }

    switch (connection.schemaExtractionStatus) {
      case 'Success':
        return 'bg-green-100 text-green-800';
      case 'Failed':
        return 'bg-red-100 text-red-800';
      case 'InProgress':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  getConnectionStringExample(): string {
    const dbType = this.connectionForm.get('databaseType')?.value;
    switch (dbType) {
      case 'SqlServer':
        return 'Server=localhost;Database=MyDB;User ID=user;Password=****;TrustServerCertificate=True';
      case 'MySQL':
        return 'Server=localhost;Database=MyDB;Uid=user;Pwd=****;';
      case 'PostgreSQL':
        return 'Host=localhost;Database=MyDB;Username=user;Password=****;';
      case 'Oracle':
        return 'Data Source=localhost:1521/XE;User Id=user;Password=****;';
      case 'SQLite':
        return 'Data Source=database.db;';
      default:
        return 'Server=localhost;Database=MyDB;User ID=user;Password=****;';
    }
  }

  canTestConnection(): boolean {
    const form = this.connectionForm;
    return !!(form.get('databaseType')?.value && form.get('connectionString')?.value?.trim());
  }

  testConnectionFromForm(): void {
    if (!this.canTestConnection() || this.testingConnection) {
      return;
    }

    this.testingConnection = true;
    this.testResult = null;

    const testDto = new TestConnectionDto({
      connectionString: this.connectionForm.get('connectionString')?.value,
      databaseType: this.connectionForm.get('databaseType')?.value
    });

    this.databaseConnectionService.testConnection(testDto).subscribe({
      next: (result) => {
        this.testResult = {
          success: !result.isError,
          message: !result.isError ? 'Connection successful!' : result.message || 'Connection failed'
        };
        this.testingConnection = false;
      },
      error: (error) => {
        console.error('Error testing connection:', error);
        this.testResult = {
          success: false,
          message: 'Failed to test connection. Please check your settings.'
        };
        this.testingConnection = false;
      }
    });
  }

  confirmDelete(connection: DatabaseConnectionDto): void {
    if (confirm(`Are you sure you want to delete the connection "${connection.name}"?`)) {
      this.deleteConnection(connection);
    }
  }

  showCreateModal(): void {
    this.isEditMode = false;
    this.selectedConnection = undefined;
    this.testResult = null;
    this.connectionForm.reset({
      databaseType: 'SqlServer',
      isActive: true
    });
    this.isModalVisible = true;
  }

  showEditModal(connection: DatabaseConnectionDto): void {
    this.isEditMode = true;
    this.selectedConnection = connection;
    this.testResult = null;
    this.connectionForm.patchValue({
      name: connection.name,
      databaseType: connection.databaseType,
      connectionString: connection.connectionString,
      description: connection.description,
      isActive: connection.isActive
    });
    this.isModalVisible = true;
  }

  handleCancel(): void {
    this.isModalVisible = false;
    this.connectionForm.reset();
    this.testResult = null;
    this.selectedConnection = undefined;
  }

  handleOk(): void {
    if (this.connectionForm.valid) {
      const formValue = this.connectionForm.value;

      // For new connections, require successful test
      if (!this.isEditMode && !this.testResult?.success) {
        this.message.warning('Please test the connection before saving');
        return;
      }

      if (this.isEditMode && this.selectedConnection) {
        this.updateConnection(formValue);
      } else {
        this.createConnection(formValue);
      }
    } else {
      this.message.warning('Please fill in all required fields');
      Object.values(this.connectionForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  createConnection(connectionData: any): void {
    const createDto = new CreateDatabaseConnectionDto({
      name: connectionData.name,
      databaseType: connectionData.databaseType,
      connectionString: connectionData.connectionString,
      description: connectionData.description,
      isActive: connectionData.isActive
    });

    this.databaseConnectionService.create(createDto).subscribe({
      next: (_) => {
        this.message.success('Database connection created successfully');
        this.loadConnections();
        this.isModalVisible = false;
        this.connectionForm.reset();
        this.testResult = null;
      },
      error: (error) => {
        console.error('Error creating connection:', error);
        this.message.error('Failed to create database connection');
      }
    });
  }

  updateConnection(connectionData: any): void {
    if (!this.selectedConnection) return;

    const updateDto = new UpdateDatabaseConnectionDto({
      id: this.selectedConnection.id,
      name: connectionData.name,
      databaseType: connectionData.databaseType,
      connectionString: connectionData.connectionString,
      description: connectionData.description,
      isActive: connectionData.isActive
    });

    this.databaseConnectionService.update(this.selectedConnection.id, updateDto).subscribe({
      next: (_) => {
        this.message.success('Database connection updated successfully');
        this.loadConnections();
        this.isModalVisible = false;
        this.connectionForm.reset();
        this.testResult = null;
      },
      error: (error) => {
        console.error('Error updating connection:', error);
        this.message.error('Failed to update database connection');
      }
    });
  }

  deleteConnection(connection: DatabaseConnectionDto): void {
    this.databaseConnectionService.delete(connection.id).subscribe({
      next: () => {
        this.message.success('Database connection deleted successfully');
        this.loadConnections();
      },
      error: (error) => {
        console.error('Error deleting connection:', error);
        this.message.error('Failed to delete database connection');
      }
    });
  }

  testConnection(connection: DatabaseConnectionDto): void {
    const testDto = new TestConnectionDto({
      connectionString: connection.connectionString,
      databaseType: connection.databaseType
    });

    this.databaseConnectionService.testConnection(testDto).subscribe({
      next: (result) => {
        if (result.isError) {
          this.message.error(`Connection test failed: ${result.message}`);
        } else {
          this.message.success('Connection test successful!');
        }
      },
      error: (error) => {
        console.error('Error testing connection:', error);
        this.message.error('Failed to test connection');
      }
    });
  }

  extractSchema(connection: DatabaseConnectionDto): void {
    const extractDto = new SchemaExtractionRequestDto({
      databaseConnectionId: connection.id,
      forceRefresh: false
    });

    this.databaseConnectionService.extractSchema(connection.id, extractDto).subscribe({
      next: (result) => {
        if (result.success) {
          this.message.success(`Schema extracted successfully! ${result.tablesExtracted} tables, ${result.columnsExtracted} columns`);
          this.loadConnections();
        } else {
          this.message.error(`Schema extraction failed: ${result.message}`);
        }
      },
      error: (error) => {
        console.error('Error extracting schema:', error);
        this.message.error('Failed to extract schema');
      }
    });
  }

  getStatusColor(status?: string): string {
    switch (status) {
      case 'Success': return 'green';
      case 'Failed': return 'red';
      case 'InProgress': return 'blue';
      default: return 'default';
    }
  }

  getStatusText(connection: DatabaseConnectionDto): string {
    if (!connection.isSchemaExtracted) {
      return 'Not Extracted';
    }
    return connection.schemaExtractionStatus || 'Unknown';
  }

  getDateString(dateValue: any): string | null {
    if (!dateValue) return null;

    // If it's already a string, return it
    if (typeof dateValue === 'string') return dateValue;

    // If it's a DateTime object with toJSDate method
    if (dateValue && typeof dateValue.toJSDate === 'function') {
      return dateValue.toJSDate().toISOString();
    }

    // If it's a DateTime object with toISO method
    if (dateValue && typeof dateValue.toISO === 'function') {
      return dateValue.toISO();
    }

    // If it's already a Date object
    if (dateValue instanceof Date) {
      return dateValue.toISOString();
    }

    // Try to convert to string
    return dateValue.toString();
  }


}
