<div class="h-full bg-[var(--background-white)] text-[var(--text-dark)] flex showSidebar transition-all" [ngClass]="{
    'is-admin-mode': isSettingsMode,
    'is-daily-insight-mode': isDailyInsightMode,
    'sidebar-collapsed': !togglingService.isNavbarOpen,
    'dark-theme': themeService.isDarkMode(),
    'light-theme': !themeService.isDarkMode()
  }">
  <div class="flex w-full">
    <!-- Left Icon Bar -->
    <div class="flex flex-col justify-between h-full sidebar-main" style="
        min-width: 4rem;
        max-width: 4rem;
        display: flex;
        align-items: center;
      ">
      <div class="*:cursor-pointer *:transition-all *:duration-300 w-full">
        <!-- Daily Insight Icon - Clean and eye-catching style -->
        <div class="sidebar-nav-item relative flex flex-col items-center" (click)="navigateToDailyInsight($event)"
          [class.active]="isDailyInsightMode">
          <div class="icon-container" [ngClass]="{ '': isDailyInsightMode }">
            <i class="ri-dashboard-line text-xl" [ngClass]="
                isDailyInsightMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "></i>
          </div>
          <span class="button-text text-center" [ngClass]="
              isDailyInsightMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            ">Daily<br />Insights</span>
        </div>
        <!--
        <div
          class="sidebar-nav-item relative flex flex-col items-center"
          (click)="navigateToNotes($event)"
          [class.active]="isNotesMode"
        >
          <div
            class="icon-container"
            [ngClass]="{ 'bg-[#343541]': isNotesMode }"
          >
            <i
              class="ri-sticky-note-line text-xl"
              [ngClass]="
                isNotesMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
          </div>
          <span
            class="button-text text-center"
            [ngClass]="
              isNotesMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            "
            >Notes</span
          >
        </div> -->

        <div class="sidebar-nav-item relative flex flex-col items-center" (click)="navigateToChat($event)"
          [class.active]="isChatMode">
          <div class="icon-container" [ngClass]="{ 'bg-[#343541]': isChatMode }">
            <i class="ri-chat-ai-line text-xl" [ngClass]="
                isChatMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "></i>
          </div>
          <span class="button-text text-center" [ngClass]="
              isChatMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            ">Chat</span>
        </div>
        <!-- My Request Icon -->
        <div class="sidebar-nav-item relative flex flex-col items-center" (click)="navigateToMyRequest($event)"
          [class.active]="isMyRequestMode">
          <div class="icon-container" [ngClass]="{ 'bg-[#343541]': isMyRequestMode }">
            <i class="ri-file-list-3-line text-xl" [ngClass]="
                isMyRequestMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "></i>
          </div>
          <span class="button-text text-center" [ngClass]="
              isMyRequestMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            ">My Request</span>
        </div>
      </div>
      <div class="*:cursor-pointer *:transition-all *:duration-300 w-full">
        <div class="relative">
          <div class="sidebar-nav-item relative flex flex-col items-center"
            [routerLink]="['/settings', 'prompt-library']" [class.active]="isSettingsMode" (click)="
              isSettingsMode && !togglingService.isNavbarOpen
                ? togglingService.toggleNavbar()
                : (chatListService.chatId = 0)
            ">
            <div class="icon-container" [ngClass]="{ 'bg-[#343541]': isSettingsMode }">
              <i class="ri-settings-3-line text-xl" [ngClass]="
                  isSettingsMode
                    ? themeService.isDarkMode()
                      ? 'text-[var(--secondary-purple)]'
                      : 'text-[var(--primary-purple)]'
                    : 'text-white'
                "></i>
            </div>
            <span class="button-text text-center" [ngClass]="
                isSettingsMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : ''
              ">Settings</span>
          </div>

          <!-- <div
            class="sidebar-nav-item relative flex flex-col items-center"
            [routerLink]="['/workspaces']"
            [class.active]="isWorkspaceMode"
            (click)="navigateToWorkspace($event)"
          >
            <div
              class="icon-container"
              [ngClass]="{ 'bg-[#343541]': isWorkspaceMode }"
            >
              <i
                class="ri-home-line text-xl"
                [ngClass]="
                  isWorkspaceMode
                    ? themeService.isDarkMode()
                      ? 'text-[var(--secondary-purple)]'
                      : 'text-[var(--primary-purple)]'
                    : 'text-white'
                "
              ></i>
            </div>
            <span
              class="button-text text-center"
              [ngClass]="
                isWorkspaceMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : ''
              "
              >Workspaces</span
            >
          </div>
           -->
        </div>
      </div>


    </div>

    <!-- Right Content Area -->
    <div class="flex-1 flex flex-col h-full sidebar-content !bg-slate-100 dark:!bg-[#343541] relative">
      <!-- CHAT CONTENT - Show when in daily insight mode or not in admin mode and not in notes mode and not in chat mode and not in my request mode -->
      <div *ngIf="!isSettingsMode && !isNotesMode && !isChatMode && !isMyRequestMode && !allMyRequestMode" class="flex-1 flex flex-col  h-full">
        <!-- Daily Insight Content -->
        <app-daily-insights-sidebar *ngIf="isDailyInsightMode"
          [workspaceName]="workspaceName"></app-daily-insights-sidebar>

      </div>

      <!-- MY REQUEST CONTENT - Show when in my request mode -->
      <div *ngIf="isMyRequestMode || allMyRequestMode" class="flex-1 flex flex-col h-full">
        <app-request-sidebar></app-request-sidebar>
      </div>

      <!-- NOTES CONTENT - Show when in notes mode -->
      <div *ngIf="isNotesMode" class="flex-1 flex flex-col h-full">
        <app-notes-sidebar [workspaceName]="workspaceName"></app-notes-sidebar>
      </div>

      <!-- AGENT CHAT CONTENT - Show when in chat mode -->
      <div *ngIf="isChatMode" class="flex-1 flex flex-col h-full">
        <app-agent-and-workspace-sidebar></app-agent-and-workspace-sidebar>
      </div>

      <!-- ADMIN CONTENT - Show when in admin mode -->
      <div *ngIf="isSettingsMode" class="flex-1 flex flex-col h-full">
        <app-settingsidebarcomponent [activeAdminTab]="activeAdminTab"></app-settingsidebarcomponent>
      </div>
    </div>
  </div>
</div>
