<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Semantic Kernel Process Framework Demo</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            padding: 30px;
            border-radius: 15px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section h2 i {
            color: #667eea;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
        }

        input,
        textarea,
        select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
            background: #fafbfc;
        }

        input:focus,
        textarea:focus,
        select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        textarea {
            height: 120px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 14px 28px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin-right: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .btn-success:hover {
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        }

        .btn-info:hover {
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }

        .progress-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            display: none;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
        }

        .progress-steps::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 0;
            right: 0;
            height: 2px;
            background: #e1e8ed;
            z-index: 1;
        }

        .progress-step {
            background: white;
            border: 3px solid #e1e8ed;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .progress-step.completed {
            border-color: #27ae60;
            background: #27ae60;
            color: white;
        }

        .result-container {
            margin-top: 30px;
            padding: 25px;
            border-radius: 12px;
            display: none;
            animation: fadeInUp 0.5s ease;
        }

        .result-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .result-info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .result-content {
            max-height: 500px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            background: rgba(255, 255, 255, 0.7);
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .feature-item:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateX(5px);
        }

        .feature-item i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, .3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-left: 10px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-warning {
            background: #fff3cd;
            color: #856404;
        }

        .status-info {
            background: #d1ecf1;
            color: #0c5460;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .section {
                padding: 20px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="container animate__animated animate__fadeIn">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> Semantic Kernel Process Framework</h1>
            <p>Advanced Workflow Orchestration & AI-Powered Project Management Demo</p>
        </div>

        <div class="content">
            <!-- Process Overview Section -->
            <div class="section animate__animated animate__fadeInUp">
                <h2><i class="fas fa-info-circle"></i> Process Framework Overview</h2>
                <p style="font-size: 1.1rem; margin-bottom: 25px; color: #5a6c7d;">
                    This demonstration showcases the power of Semantic Kernel's Process Framework with a complete
                    project registration workflow that combines AI intelligence, automated decision-making, and
                    comprehensive workflow orchestration.
                </p>

                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-check-circle"></i>
                        <span>Input Validation & Sanitization</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-brain"></i>
                        <span>AI-Powered Project Analysis</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-users"></i>
                        <span>Intelligent Workspace Assignment</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-database"></i>
                        <span>Automated Project Creation</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-bell"></i>
                        <span>Multi-Channel Notifications</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cogs"></i>
                        <span>Event-Driven Execution</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Real-Time Progress Tracking</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Built-in Error Handling</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Section -->
            <div class="section animate__animated animate__fadeInUp animate__delay-1s">
                <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
                <p style="margin-bottom: 25px; color: #5a6c7d;">
                    Get started quickly with pre-configured demonstrations or explore the process framework
                    capabilities.
                </p>

                <div class="grid">
                    <div class="card">
                        <h3 style="color: #27ae60; margin-bottom: 15px;">
                            <i class="fas fa-play-circle"></i> Test Workflow
                        </h3>
                        <p style="margin-bottom: 20px; color: #666;">
                            Execute a complete workflow with pre-filled sample data to see the Process Framework in
                            action.
                        </p>
                        <button class="btn btn-success" onclick="runTestWorkflow()">
                            <i class="fas fa-rocket"></i> Run Test Workflow
                        </button>
                    </div>

                    <div class="card">
                        <h3 style="color: #3498db; margin-bottom: 15px;">
                            <i class="fas fa-info-circle"></i> Process Information
                        </h3>
                        <p style="margin-bottom: 20px; color: #666;">
                            Get detailed information about the process framework architecture and capabilities.
                        </p>
                        <button class="btn btn-info" onclick="getProcessInfo()">
                            <i class="fas fa-info"></i> Get Process Info
                        </button>
                    </div>
                </div>
            </div>

            <!-- Custom Project Registration Section -->
            <div class="section animate__animated animate__fadeInUp animate__delay-2s">
                <h2><i class="fas fa-edit"></i> Custom Project Registration</h2>
                <p style="margin-bottom: 25px; color: #5a6c7d;">
                    Create a custom project registration request and watch the Process Framework handle the complete
                    workflow.
                </p>

                <form id="projectForm">
                    <div class="grid">
                        <div class="card">
                            <div class="form-group">
                                <label for="subject"><i class="fas fa-tag"></i> Project Subject *</label>
                                <input type="text" id="subject" name="subject" required
                                    placeholder="Enter project title (e.g., 'AI-Powered E-commerce Platform')">
                            </div>

                            <div class="form-group">
                                <label for="userEmail"><i class="fas fa-envelope"></i> Your Email *</label>
                                <input type="email" id="userEmail" name="userEmail" required
                                    placeholder="<EMAIL>">
                            </div>

                            <div class="form-group">
                                <label for="assignedEmail"><i class="fas fa-user"></i> Preferred Developer
                                    (Optional)</label>
                                <input type="email" id="assignedEmail" name="assignedEmail"
                                    placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="card">
                            <div class="form-group">
                                <label for="message"><i class="fas fa-file-alt"></i> Project Description *</label>
                                <textarea id="message" name="message" required
                                    placeholder="Describe your project requirements, goals, technologies, and any specific details..."></textarea>
                            </div>

                            <div class="form-group">
                                <label for="priority"><i class="fas fa-exclamation-triangle"></i> Priority</label>
                                <select id="priority" name="priority">
                                    <option value="Low">Low Priority</option>
                                    <option value="Medium" selected>Medium Priority</option>
                                    <option value="High">High Priority</option>
                                    <option value="Critical">Critical Priority</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="fileNames"><i class="fas fa-paperclip"></i> File Names (Optional)</label>
                                <input type="text" id="fileNames" name="fileNames"
                                    placeholder="requirements.pdf, mockups.png, specs.docx (comma-separated)">
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn">
                            <i class="fas fa-cogs"></i> Execute Workflow
                        </button>
                        <button type="button" class="btn btn-info" onclick="loadSampleData()">
                            <i class="fas fa-magic"></i> Load Sample Data
                        </button>
                        <button type="button" class="btn btn-info" onclick="clearForm()">
                            <i class="fas fa-eraser"></i> Clear Form
                        </button>
                    </div>
                </form>
            </div>

            <!-- Progress Section -->
            <div id="progressContainer" class="progress-container">
                <h3 style="margin-bottom: 20px; color: #2c3e50;">
                    <i class="fas fa-tasks"></i> Workflow Progress
                </h3>
                <div class="progress-steps">
                    <div class="progress-step" id="step1"><i class="fas fa-check"></i></div>
                    <div class="progress-step" id="step2"><i class="fas fa-brain"></i></div>
                    <div class="progress-step" id="step3"><i class="fas fa-users"></i></div>
                    <div class="progress-step" id="step4"><i class="fas fa-plus"></i></div>
                    <div class="progress-step" id="step5"><i class="fas fa-bell"></i></div>
                </div>
                <div id="progressText" style="text-align: center; color: #5a6c7d; font-weight: 500;">
                    Initializing workflow...
                </div>
            </div>

            <!-- Results Section -->
            <div id="resultContainer" class="result-container">
                <h3 style="margin-bottom: 15px;">
                    <i class="fas fa-chart-bar"></i> Workflow Results
                    <span id="resultStatus" class="status-badge"></span>
                </h3>
                <div id="resultContent" class="result-content"></div>
            </div>
        </div>
    </div>

    <script>
        const baseUrl = window.location.origin.replace(/:\d+/, ':7257'); // Adjust port as needed
        let currentWorkflowStep = 0;
        let progressInterval;

        // Sample data for quick testing
        const sampleData = {
            subject: "AI-Powered E-commerce Platform Development",
            message: "Develop a comprehensive e-commerce platform with AI-powered product recommendations, real-time inventory management, advanced analytics dashboard, and mobile-first responsive design. The system should include secure user authentication, integrated payment processing, order tracking, customer support chat, and seamless third-party API integrations for shipping and payment gateways. This project demonstrates the Semantic Kernel Process Framework capabilities with complex workflow orchestration.",
            userEmail: "<EMAIL>",
            assignedEmail: "<EMAIL>",
            priority: "High",
            fileNames: "requirements.pdf, ui-mockups.figma, technical-specs.docx, api-documentation.md, database-schema.sql"
        };

        async function runTestWorkflow() {
            showProgress('Running test workflow...');
            simulateProgress();

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/test-workflow`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                hideProgress();
                displayResult(result, response.ok, 'Test Workflow');
            } catch (error) {
                hideProgress();
                displayError(`Error: ${error.message}`, 'Test Workflow');
            }
        }

        async function getProcessInfo() {
            showProgress('Getting process information...');

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/process-info`, {
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const result = await response.json();
                hideProgress();
                displayResult(result, response.ok, 'Process Information');
            } catch (error) {
                hideProgress();
                displayError(`Error: ${error.message}`, 'Process Information');
            }
        }

        document.getElementById('projectForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const fileNames = formData.get('fileNames');

            const request = {
                subject: formData.get('subject'),
                message: formData.get('message'),
                userEmail: formData.get('userEmail'),
                assignedEmail: formData.get('assignedEmail') || null,
                priority: formData.get('priority'),
                fileNames: fileNames ? fileNames.split(',').map(f => f.trim()).filter(f => f) : [],
                preferredWorkspaceId: null,
                preferredCategoryId: null
            };

            showProgress('Executing project registration workflow...');
            simulateProgress();

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/execute-project-registration`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(request)
                });

                const result = await response.json();
                hideProgress();
                displayResult(result, response.ok, 'Project Registration');
            } catch (error) {
                hideProgress();
                displayError(`Error: ${error.message}`, 'Project Registration');
            }
        });

        function showProgress(message) {
            const container = document.getElementById('progressContainer');
            const text = document.getElementById('progressText');

            container.style.display = 'block';
            text.textContent = message;
            currentWorkflowStep = 0;

            // Reset all steps
            for (let i = 1; i <= 5; i++) {
                const step = document.getElementById(`step${i}`);
                step.className = 'progress-step';
            }

            // Scroll to progress section
            container.scrollIntoView({ behavior: 'smooth' });
        }

        function hideProgress() {
            clearInterval(progressInterval);
            const container = document.getElementById('progressContainer');
            setTimeout(() => {
                container.style.display = 'none';
            }, 1000);
        }

        function simulateProgress() {
            const steps = [
                'Validating input data...',
                'Analyzing project with AI...',
                'Assigning optimal workspace...',
                'Creating project record...',
                'Sending notifications...',
                'Workflow completed!'
            ];

            currentWorkflowStep = 0;

            progressInterval = setInterval(() => {
                if (currentWorkflowStep < steps.length) {
                    document.getElementById('progressText').textContent = steps[currentWorkflowStep];

                    if (currentWorkflowStep < 5) {
                        const step = document.getElementById(`step${currentWorkflowStep + 1}`);
                        step.className = 'progress-step active';

                        // Mark previous steps as completed
                        for (let i = 1; i < currentWorkflowStep + 1; i++) {
                            const prevStep = document.getElementById(`step${i}`);
                            prevStep.className = 'progress-step completed';
                        }
                    }

                    currentWorkflowStep++;
                } else {
                    clearInterval(progressInterval);
                }
            }, 1000);
        }

        function displayResult(result, isSuccess, title) {
            const container = document.getElementById('resultContainer');
            const content = document.getElementById('resultContent');
            const status = document.getElementById('resultStatus');

            container.className = `result-container ${isSuccess ? 'result-success' : 'result-error'}`;
            container.style.display = 'block';

            status.textContent = isSuccess ? 'Success' : 'Error';
            status.className = `status-badge ${isSuccess ? 'status-success' : 'status-warning'}`;

            content.textContent = JSON.stringify(result, null, 2);

            // Scroll to results
            container.scrollIntoView({ behavior: 'smooth' });
        }

        function displayError(message, title) {
            const container = document.getElementById('resultContainer');
            const content = document.getElementById('resultContent');
            const status = document.getElementById('resultStatus');

            container.className = 'result-container result-error';
            container.style.display = 'block';

            status.textContent = 'Error';
            status.className = 'status-badge status-warning';

            content.textContent = message;

            // Scroll to results
            container.scrollIntoView({ behavior: 'smooth' });
        }

        function loadSampleData() {
            document.getElementById('subject').value = sampleData.subject;
            document.getElementById('message').value = sampleData.message;
            document.getElementById('userEmail').value = sampleData.userEmail;
            document.getElementById('assignedEmail').value = sampleData.assignedEmail;
            document.getElementById('priority').value = sampleData.priority;
            document.getElementById('fileNames').value = sampleData.fileNames;

            // Add animation to show data was loaded
            const form = document.getElementById('projectForm');
            form.classList.add('animate__animated', 'animate__pulse');
            setTimeout(() => {
                form.classList.remove('animate__animated', 'animate__pulse');
            }, 1000);
        }

        function clearForm() {
            document.getElementById('projectForm').reset();

            // Hide results if visible
            const resultContainer = document.getElementById('resultContainer');
            resultContainer.style.display = 'none';
        }

        // Load sample data on page load
        window.addEventListener('load', function () {
            loadSampleData();
        });
    </script>
</body>

</html>