using System;

namespace ProjectApp.Core.Models
{
    public class AgentEvaluation
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        
        public string AgentName { get; set; }
        
        public string Prompt { get; set; }
        
        public string ExpectedOutput { get; set; }
        
        public string Output { get; set; }
        
        public double? Score { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? EvaluatedAt { get; set; }
    }
}
