using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using System.ComponentModel;
using System.Text.Json;
using ProjectApp.Infrastructure.Services;
using Microsoft.SemanticKernel.ChatCompletion;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class DatabaseSqlGeneratorPlugin
    {
        private readonly ILogger<DatabaseSqlGeneratorPlugin> _logger;
        private readonly DatabaseSchemaService _schemaService;
        private readonly IChatCompletionService _chatCompletionService;

        public DatabaseSqlGeneratorPlugin(ILogger<DatabaseSqlGeneratorPlugin> logger, DatabaseSchemaService schemaService, IChatCompletionService chatCompletionService)
        {
            _logger = logger;
            _schemaService = schemaService;
            _chatCompletionService = chatCompletionService;
        }

        [KernelFunction]
        [Description("Get database schema context for a specific connection")]
        public async Task<string> GetSchemaContext(
            [Description("Database connection name")] string connectionName)
        {
            try
            {
                _logger.LogInformation("Getting schema context for connection: {ConnectionName}", connectionName);

                var schemaContext = await _schemaService.GetSchemaContextAsync(connectionName);

                if (string.IsNullOrEmpty(schemaContext) || schemaContext.Contains("No database schema available"))
                {
                    return JsonSerializer.Serialize(new
                    {
                        success = false,
                        error = $"No schema found for connection '{connectionName}'. Please ensure the schema has been extracted.",
                        schema_context = ""
                    });
                }

                return JsonSerializer.Serialize(new
                {
                    success = true,
                    connection_name = connectionName,
                    schema_context = schemaContext
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting schema context for connection {ConnectionName}", connectionName);
                return JsonSerializer.Serialize(new
                {
                    success = false,
                    error = $"Failed to get schema context: {ex.Message}",
                    schema_context = ""
                });
            }
        }

        [KernelFunction]
        [Description("Generate SQL query based on natural language request and database schema")]
        public async Task<string> GenerateSqlQuery(
            [Description("The natural language request for data")] string userRequest,
            [Description("Database schema context")] string schemaContext = "")
        {
            try
            {
                _logger.LogInformation("Generating SQL query for request: {Request}", userRequest);

                // Basic SQL generation logic
                // In a real implementation, this would use AI to generate SQL
                var sqlQuery = await GenerateQueryFromRequest(userRequest, schemaContext);

                // No validation - trust AI to generate appropriate SQL

                _logger.LogInformation("Generated SQL query: {Query}", sqlQuery);

                return JsonSerializer.Serialize(new
                {
                    success = true,
                    query = sqlQuery,
                    sql_query = sqlQuery, // For compatibility with LoggingFilter
                    explanation = $"Generated SQL query for: {userRequest}",
                    safety_level = "read_only",
                    step_message = $"✅ **Generated SQL Query:**\n```sql\n{sqlQuery}\n```\n"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SQL query for request: {Request}", userRequest);
                return JsonSerializer.Serialize(new
                {
                    success = false,
                    error = $"Failed to generate SQL query: {ex.Message}"
                });
            }
        }

        // ValidateQuery function removed - no validation needed for maximum flexibility

        private async Task<string> GenerateQueryFromRequest(string userRequest, string schemaContext)
        {
            try
            {
                // Use AI to generate SQL query based on user request and database schema
                var prompt = $@"You are a SQL expert. Generate a SQL query based on the user request and database schema provided.

User Request: {userRequest}

Database Schema:
{schemaContext}

Requirements:
1. Generate ONLY the SQL query, no explanations or markdown
2. Use proper table and column names from the schema provided
3. Include appropriate WHERE clauses, JOINs, and ORDER BY as needed
4. For SELECT queries, limit results to reasonable amounts (TOP 100 or LIMIT 100)
5. Support all standard database operations: SELECT, INSERT, UPDATE, DELETE, MERGE
6. For UPDATE/DELETE operations, always include appropriate WHERE clauses to avoid affecting all rows
7. Return just the SQL query without any additional text

SQL Query:";

                var chatHistory = new ChatHistory();
                chatHistory.AddUserMessage(prompt);

                var response = await _chatCompletionService.GetChatMessageContentAsync(chatHistory);
                var sqlQuery = response.Content?.Trim();

                if (!string.IsNullOrEmpty(sqlQuery))
                {
                    // Clean up the response - remove any markdown formatting
                    sqlQuery = sqlQuery.Replace("```sql", "").Replace("```", "").Trim();

                    _logger.LogInformation("AI generated SQL query: {Query}", sqlQuery);
                    return sqlQuery;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating SQL query with AI, falling back to simple logic");
            }

            // Fallback to simple logic if AI fails
            return GenerateFallbackQuery(userRequest, schemaContext);
        }

        private string GenerateFallbackQuery(string userRequest, string schemaContext)
        {
            var request = userRequest.ToLower();

            if (request.Contains("product"))
            {
                return "SELECT TOP 100 * FROM Products ORDER BY ProductID";
            }

            if (request.Contains("user"))
            {
                return "SELECT TOP 100 * FROM Users ORDER BY UserID";
            }

            if (request.Contains("order"))
            {
                return "SELECT TOP 100 * FROM Orders ORDER BY OrderDate DESC";
            }

            if (request.Contains("count"))
            {
                var tableName = ExtractTableNameFromSchema(schemaContext, request);
                return $"SELECT COUNT(*) as TotalRecords FROM {tableName}";
            }

            // Default: try to find the most relevant table from schema
            var defaultTable = ExtractTableNameFromSchema(schemaContext, request);
            return $"SELECT TOP 10 * FROM {defaultTable}";
        }

        private string ExtractTableNameFromSchema(string schemaContext, string userRequest)
        {
            // Extract table names from schema context and find the most relevant one
            var tableNames = new List<string>();
            var lines = schemaContext.Split('\n');

            foreach (var line in lines)
            {
                if (line.Contains("Table:") || line.Contains("TABLE_NAME"))
                {
                    var tableName = line.Replace("Table:", "").Replace("TABLE_NAME", "").Trim();
                    if (!string.IsNullOrEmpty(tableName) && !tableName.Contains(" "))
                    {
                        tableNames.Add(tableName);
                    }
                }
            }

            // If no tables found in schema, return a default
            if (!tableNames.Any())
            {
                return "Users"; // Fallback
            }

            // Try to match table name with user request
            var request = userRequest.ToLower();
            foreach (var table in tableNames)
            {
                var tableLower = table.ToLower();
                if (request.Contains(tableLower.Replace("s", "")) || // "product" matches "Products"
                    request.Contains(tableLower) ||
                    tableLower.Contains(request.Split(' ').FirstOrDefault(w => w.Length > 3) ?? ""))
                {
                    return table;
                }
            }

            // Return first table if no match found
            return tableNames.First();
        }

        // All validation methods and classes removed for maximum database operation flexibility
    }
}
