import { NgModule } from '@angular/core';
import * as ApiServiceProxies from './service-proxies';

@NgModule({
  providers: [
    ApiServiceProxies.UserAccountServiceProxy,
    ApiServiceProxies.ChatServiceProxy,
    ApiServiceProxies.AgentDefinitionServiceProxy,
    ApiServiceProxies.ChatModelServiceProxy,
    ApiServiceProxies.ModelDetailsServiceProxy,
    ApiServiceProxies.ApiCredentialsServiceProxy,
    ApiServiceProxies.WorkspaceServiceProxy,
    ApiServiceProxies.ProjectMemoryServiceProxy,
    ApiServiceProxies.AssignWorkspaceServiceProxy,
    ApiServiceProxies.DocsServiceProxy,
    ApiServiceProxies.FileServiceProxy,
    ApiServiceProxies.AiServiceProxy,
    // ApiServiceProxies.EmbeddingConfigServiceProxy,
    ApiServiceProxies.PromptLibraryServiceProxy,
    ApiServiceProxies.AuthServiceProxy,
    ApiServiceProxies.PluginServiceProxy,
    ApiServiceProxies.DailyInsightServiceProxy,
    ApiServiceProxies.MemoryServiceProxy,
    ApiServiceProxies.SqlConnectionServiceProxy,
    ApiServiceProxies.EmailServiceProxy,
    ApiServiceProxies.AgentChatServiceProxy,
    ApiServiceProxies.NavigationServiceProxy,
    ApiServiceProxies.AgentEvaluationServiceProxy,
    ApiServiceProxies.DatabaseConnectionServiceProxy
  ],
})
export class ServiceProxyModule { }
