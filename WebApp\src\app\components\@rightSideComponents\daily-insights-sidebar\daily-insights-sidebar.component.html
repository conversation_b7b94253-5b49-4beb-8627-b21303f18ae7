<!-- Daily Insight Content -->
<div class="p-4 overflow-y-auto max-h-[calc(100vh-74px)]">
  <!-- Header Section -->
  <div class="flex items-center justify-between mb-4">
    <div class="flex items-center gap-1.5">
      <span
        class="w-1.5 h-5 bg-gradient-to-b from-[var(--primary-purple)] to-[var(--secondary-purple)] rounded-full"></span>
      <div class="flex flex-col">
        <span class="font-bold text-[var(--text-dark)] text-lg">Daily Insights</span>
        <span class="text-xs text-[var(--text-medium-gray)]">Welcome to your AI Hub</span>
      </div>
    </div>

    <!-- Refresh Button -->
    <!-- <div class="tooltip-container">
      <button
        class="w-7 h-7 rounded-[var(--border-radius-small)] hover:bg-[var(--hover-blue-gray)] transition-all duration-300 flex justify-center items-center outline-none border-none bg-transparent text-lg cursor-pointer"
        (click)="refreshDailyInsights()">
        <i
          class="ri-refresh-line text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)] transition-colors duration-300"></i>
      </button>
      <span class="custom-tooltip refresh-tooltip">Refresh</span>
    </div> -->
  </div>
  <div class="space-y-6">
    <!-- Quick Actions Section -->
    <div class="space-y-2 space-x-2">
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center gap-2">
          <i class="ri-flashlight-line text-[var(--primary-purple)] text-lg"></i>
          <h3 class="text-base font-semibold text-[var(--text-dark)]">Quick Actions</h3>
        </div>
        <div class="h-px flex-grow bg-[var(--hover-blue-gray)] ml-2 opacity-50"></div>
      </div>

      <!-- Action Cards with enhanced styling -->
      <div class="grid grid-cols-1 gap-4">
        <!-- New Chat Card -->

        <div>
          <div
            class="flex items-center gap-3 py-2  px-3 cursor-pointer hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
            (click)="goToChat()">
            <i class="ri-chat-new-line text-xl "></i>
            <span class="font-medium block ">New Chat</span>
          </div>

          <div
            class="flex items-center gap-3 py-2  px-3 cursor-pointer hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
            (click)="goToWorkspace()">
            <i class="ri-folder-add-line text-xl "></i>
            <span class="font-medium block ">Workspaces</span>
          </div>

          <div
            class="flex items-center gap-3 py-2  px-3 cursor-pointer hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
            (click)="goToPromptLibrary()">
            <i class="ri-code-line text-xl "></i>
            <span class="font-medium block ">Prompt Library</span>
          </div>

          <div
            class="flex items-center gap-3 py-2  px-3 cursor-pointer hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
            (click)="goToProjectSummary()">
            <i class="ri-file-chart-line text-xl "></i>
            <span class="font-medium block ">Project Summery</span>
          </div>

          <div
            class="flex items-center gap-3 py-2  px-3 cursor-pointer hover:bg-[var(--primary-purple)] rounded-md hover:text-white"
            (click)="goToTaskManagement()">
            <i class="ri-task-line text-xl "></i>
            <span class="font-medium block ">Task Management</span>
          </div>

        </div>

        <!-- <button (click)="goToChat()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-chat-new-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">New Chat</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Start a conversation</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button> -->


        <!-- Workspaces Card -->
        <!-- <button (click)="goToWorkspace()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-folder-add-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Workspaces</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Organize projects</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button> -->

        <!-- Prompt Library Card -->
        <!-- <button (click)="goToPromptLibrary()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-code-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Prompt Library</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Browse prompts</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button> -->

        <!-- Project Summary Card -->
        <!-- <button (click)="goToProjectSummary()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-file-chart-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Project Summary</span>
            <span class="text-xs text-[var(--text-medium-gray)]">View project details</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button> -->

        <!-- Task Management Card -->
        <!-- <button (click)="goToTaskManagement()"
          class="group w-full flex items-center gap-3 px-4 py-3 bg-[var(--background-light-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gradient-to-r hover:from-[var(--background-light-gray)] hover:to-[var(--hover-blue-gray)] transition-all duration-300 border border-[var(--hover-blue-gray)] relative overflow-hidden shadow-sm hover:shadow">
          <div
            class="absolute inset-0 bg-gradient-to-r from-[var(--secondary-purple)] to-[var(--primary-purple)] opacity-0 group-hover:opacity-5 transition-opacity duration-300">
          </div>
          <div
            class="w-10 h-10 rounded-full bg-[var(--secondary-purple)] flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-300">
            <i class="ri-task-line text-xl text-[var(--primary-purple)]"></i>
          </div>
          <div class="text-left">
            <span class="font-medium block text-[var(--text-dark)]">Task Management</span>
            <span class="text-xs text-[var(--text-medium-gray)]">Manage your tasks</span>
          </div>
          <i
            class="ri-arrow-right-line text-[var(--text-medium-gray)] ml-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300"></i>
        </button> -->
      </div>
    </div>



    <!-- Last Clicked Item Display -->
    <div *ngIf="lastClickedItem"
      class="mt-6 p-4 bg-[var(--background-light-gray)] rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)]">
      <div class="flex items-center gap-2 mb-2">
        <i class="ri-time-line text-[var(--primary-purple)]"></i>
        <span class="text-sm font-medium text-[var(--text-dark)]">Last Action</span>
      </div>
      <div class="text-xs text-[var(--text-medium-gray)]">
        <div class="font-medium text-[var(--text-dark)]">{{ lastClickedItem }}</div>
        <div class="mt-1">{{ lastClickedTime }}</div>
      </div>
    </div>
  </div>
</div>
