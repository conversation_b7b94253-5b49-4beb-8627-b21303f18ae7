{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-core-pipe.mjs", "../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-statistic.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Pipe, NgModule } from '@angular/core';\nimport { timeUnits } from 'ng-zorro-antd/core/time';\nimport { padStart } from 'ng-zorro-antd/core/util';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTimeRangePipe {\n  transform(value, format = 'HH:mm:ss') {\n    let duration = Number(value || 0);\n    return timeUnits.reduce((current, [name, unit]) => {\n      if (current.indexOf(name) !== -1) {\n        const v = Math.floor(duration / unit);\n        duration -= v * unit;\n        return current.replace(new RegExp(`${name}+`, 'g'), match => padStart(v.toString(), match.length, '0'));\n      }\n      return current;\n    }, format);\n  }\n  static {\n    this.ɵfac = function NzTimeRangePipe_Factory(t) {\n      return new (t || NzTimeRangePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"nzTimeRange\",\n      type: NzTimeRangePipe,\n      pure: true,\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTimeRangePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'nzTimeRange',\n      pure: true,\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPipesModule {\n  static {\n    this.ɵfac = function NzPipesModule_Factory(t) {\n      return new (t || NzPipesModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzPipesModule,\n      imports: [NzTimeRangePipe],\n      exports: [NzTimeRangePipe]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPipesModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTimeRangePipe],\n      exports: [NzTimeRangePipe]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzPipesModule, NzTimeRangePipe };\n", "import * as i0 from '@angular/core';\nimport { LOCALE_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Optional, EventEmitter, Output, NgModule } from '@angular/core';\nimport { Subject, interval } from 'rxjs';\nimport * as i3 from 'ng-zorro-antd/core/pipe';\nimport { NzPipesModule } from 'ng-zorro-antd/core/pipe';\nimport { getLocaleNumberSymbol, NumberSymbol, NgIf, NgTemplateOutlet, NgStyle } from '@angular/common';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i2 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i1$1 from '@angular/cdk/platform';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction NzStatisticNumberComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0, 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzValueTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.nzValue));\n  }\n}\nfunction NzStatisticNumberComponent_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.displayInt);\n  }\n}\nfunction NzStatisticNumberComponent_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.displayDecimal);\n  }\n}\nfunction NzStatisticNumberComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzStatisticNumberComponent_ng_container_2_span_1_Template, 2, 1, \"span\", 4)(2, NzStatisticNumberComponent_ng_container_2_span_2_Template, 2, 1, \"span\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayInt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.displayDecimal);\n  }\n}\nfunction NzStatisticComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTitle);\n  }\n}\nfunction NzStatisticComponent_span_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzPrefix);\n  }\n}\nfunction NzStatisticComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵtemplate(1, NzStatisticComponent_span_3_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzPrefix);\n  }\n}\nfunction NzStatisticComponent_span_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzSuffix);\n  }\n}\nfunction NzStatisticComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 7);\n    i0.ɵɵtemplate(1, NzStatisticComponent_span_5_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.nzSuffix);\n  }\n}\nfunction NzCountdownComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n    i0.ɵɵpipe(1, \"nzTimeRange\");\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(1, 1, ctx_r0.diff, ctx_r0.nzFormat));\n  }\n}\nclass NzStatisticNumberComponent {\n  constructor(locale_id) {\n    this.locale_id = locale_id;\n    this.displayInt = '';\n    this.displayDecimal = '';\n  }\n  ngOnChanges() {\n    this.formatNumber();\n  }\n  formatNumber() {\n    const decimalSeparator = typeof this.nzValue === 'number' ? '.' : getLocaleNumberSymbol(this.locale_id, NumberSymbol.Decimal);\n    const value = String(this.nzValue);\n    const [int, decimal] = value.split(decimalSeparator);\n    this.displayInt = int;\n    this.displayDecimal = decimal ? `${decimalSeparator}${decimal}` : '';\n  }\n  static {\n    this.ɵfac = function NzStatisticNumberComponent_Factory(t) {\n      return new (t || NzStatisticNumberComponent)(i0.ɵɵdirectiveInject(LOCALE_ID));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzStatisticNumberComponent,\n      selectors: [[\"nz-statistic-number\"]],\n      inputs: {\n        nzValue: \"nzValue\",\n        nzValueTemplate: \"nzValueTemplate\"\n      },\n      exportAs: [\"nzStatisticNumber\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[1, \"ant-statistic-content-value\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"ant-statistic-content-value-int\", 4, \"ngIf\"], [\"class\", \"ant-statistic-content-value-decimal\", 4, \"ngIf\"], [1, \"ant-statistic-content-value-int\"], [1, \"ant-statistic-content-value-decimal\"]],\n      template: function NzStatisticNumberComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵtemplate(1, NzStatisticNumberComponent_ng_container_1_Template, 1, 4, \"ng-container\", 1)(2, NzStatisticNumberComponent_ng_container_2_Template, 3, 2, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzValueTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.nzValueTemplate);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStatisticNumberComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      preserveWhitespaces: false,\n      selector: 'nz-statistic-number',\n      exportAs: 'nzStatisticNumber',\n      template: `\n    <span class=\"ant-statistic-content-value\">\n      <ng-container\n        *ngIf=\"nzValueTemplate\"\n        [ngTemplateOutlet]=\"nzValueTemplate\"\n        [ngTemplateOutletContext]=\"{ $implicit: nzValue }\"\n      ></ng-container>\n      <ng-container *ngIf=\"!nzValueTemplate\">\n        <span *ngIf=\"displayInt\" class=\"ant-statistic-content-value-int\">{{ displayInt }}</span>\n        <span *ngIf=\"displayDecimal\" class=\"ant-statistic-content-value-decimal\">{{ displayDecimal }}</span>\n      </ng-container>\n    </span>\n  `,\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOCALE_ID]\n    }]\n  }], {\n    nzValue: [{\n      type: Input\n    }],\n    nzValueTemplate: [{\n      type: Input\n    }]\n  });\n})();\nclass NzStatisticComponent {\n  constructor(cdr, directionality) {\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.nzValueStyle = {};\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzStatisticComponent_Factory(t) {\n      return new (t || NzStatisticComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzStatisticComponent,\n      selectors: [[\"nz-statistic\"]],\n      hostAttrs: [1, \"ant-statistic\"],\n      hostVars: 2,\n      hostBindings: function NzStatisticComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-statistic-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzPrefix: \"nzPrefix\",\n        nzSuffix: \"nzSuffix\",\n        nzTitle: \"nzTitle\",\n        nzValue: \"nzValue\",\n        nzValueStyle: \"nzValueStyle\",\n        nzValueTemplate: \"nzValueTemplate\"\n      },\n      exportAs: [\"nzStatistic\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 6,\n      consts: [[1, \"ant-statistic-title\"], [4, \"nzStringTemplateOutlet\"], [1, \"ant-statistic-content\", 3, \"ngStyle\"], [\"class\", \"ant-statistic-content-prefix\", 4, \"ngIf\"], [3, \"nzValue\", \"nzValueTemplate\"], [\"class\", \"ant-statistic-content-suffix\", 4, \"ngIf\"], [1, \"ant-statistic-content-prefix\"], [1, \"ant-statistic-content-suffix\"]],\n      template: function NzStatisticComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, NzStatisticComponent_ng_container_1_Template, 2, 1, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵtemplate(3, NzStatisticComponent_span_3_Template, 2, 1, \"span\", 3);\n          i0.ɵɵelement(4, \"nz-statistic-number\", 4);\n          i0.ɵɵtemplate(5, NzStatisticComponent_span_5_Template, 2, 1, \"span\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.nzTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.nzValueStyle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzPrefix);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzValue\", ctx.nzValue)(\"nzValueTemplate\", ctx.nzValueTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzSuffix);\n        }\n      },\n      dependencies: [NzStatisticNumberComponent, NgIf, NzOutletModule, i2.NzStringTemplateOutletDirective, NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStatisticComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-statistic',\n      exportAs: 'nzStatistic',\n      template: `\n    <div class=\"ant-statistic-title\">\n      <ng-container *nzStringTemplateOutlet=\"nzTitle\">{{ nzTitle }}</ng-container>\n    </div>\n    <div class=\"ant-statistic-content\" [ngStyle]=\"nzValueStyle\">\n      <span *ngIf=\"nzPrefix\" class=\"ant-statistic-content-prefix\">\n        <ng-container *nzStringTemplateOutlet=\"nzPrefix\">{{ nzPrefix }}</ng-container>\n      </span>\n      <nz-statistic-number [nzValue]=\"nzValue\" [nzValueTemplate]=\"nzValueTemplate\"></nz-statistic-number>\n      <span *ngIf=\"nzSuffix\" class=\"ant-statistic-content-suffix\">\n        <ng-container *nzStringTemplateOutlet=\"nzSuffix\">{{ nzSuffix }}</ng-container>\n      </span>\n    </div>\n  `,\n      host: {\n        class: 'ant-statistic',\n        '[class.ant-statistic-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzStatisticNumberComponent, NgIf, NzOutletModule, NgStyle],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzPrefix: [{\n      type: Input\n    }],\n    nzSuffix: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzValueStyle: [{\n      type: Input\n    }],\n    nzValueTemplate: [{\n      type: Input\n    }]\n  });\n})();\nconst REFRESH_INTERVAL = 1000 / 30;\nclass NzCountdownComponent extends NzStatisticComponent {\n  constructor(cdr, ngZone, platform, directionality) {\n    super(cdr, directionality);\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.nzFormat = 'HH:mm:ss';\n    this.nzCountdownFinish = new EventEmitter();\n    this.target = 0;\n  }\n  ngOnChanges(changes) {\n    if (changes.nzValue) {\n      this.target = Number(changes.nzValue.currentValue);\n      if (!changes.nzValue.isFirstChange()) {\n        this.syncTimer();\n      }\n    }\n  }\n  ngOnInit() {\n    super.ngOnInit();\n    this.syncTimer();\n  }\n  ngOnDestroy() {\n    this.stopTimer();\n  }\n  syncTimer() {\n    if (this.target >= Date.now()) {\n      this.startTimer();\n    } else {\n      this.stopTimer();\n    }\n  }\n  startTimer() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        this.stopTimer();\n        this.updater_ = interval(REFRESH_INTERVAL).subscribe(() => {\n          this.updateValue();\n          this.cdr.detectChanges();\n        });\n      });\n    }\n  }\n  stopTimer() {\n    if (this.updater_) {\n      this.updater_.unsubscribe();\n      this.updater_ = null;\n    }\n  }\n  /**\n   * Update time that should be displayed on the screen.\n   */\n  updateValue() {\n    this.diff = Math.max(this.target - Date.now(), 0);\n    if (this.diff === 0) {\n      this.stopTimer();\n      if (this.nzCountdownFinish.observers.length) {\n        this.ngZone.run(() => this.nzCountdownFinish.emit());\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzCountdownComponent_Factory(t) {\n      return new (t || NzCountdownComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCountdownComponent,\n      selectors: [[\"nz-countdown\"]],\n      inputs: {\n        nzFormat: \"nzFormat\"\n      },\n      outputs: {\n        nzCountdownFinish: \"nzCountdownFinish\"\n      },\n      exportAs: [\"nzCountdown\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 6,\n      consts: [[\"countDownTpl\", \"\"], [3, \"nzValue\", \"nzValueStyle\", \"nzValueTemplate\", \"nzTitle\", \"nzPrefix\", \"nzSuffix\"]],\n      template: function NzCountdownComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"nz-statistic\", 1);\n          i0.ɵɵtemplate(1, NzCountdownComponent_ng_template_1_Template, 2, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const countDownTpl_r2 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"nzValue\", ctx.diff)(\"nzValueStyle\", ctx.nzValueStyle)(\"nzValueTemplate\", ctx.nzValueTemplate || countDownTpl_r2)(\"nzTitle\", ctx.nzTitle)(\"nzPrefix\", ctx.nzPrefix)(\"nzSuffix\", ctx.nzSuffix);\n        }\n      },\n      dependencies: [NzStatisticComponent, NzPipesModule, i3.NzTimeRangePipe],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCountdownComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-countdown',\n      exportAs: 'nzCountdown',\n      template: `\n    <nz-statistic\n      [nzValue]=\"diff\"\n      [nzValueStyle]=\"nzValueStyle\"\n      [nzValueTemplate]=\"nzValueTemplate || countDownTpl\"\n      [nzTitle]=\"nzTitle\"\n      [nzPrefix]=\"nzPrefix\"\n      [nzSuffix]=\"nzSuffix\"\n    ></nz-statistic>\n\n    <ng-template #countDownTpl>{{ diff | nzTimeRange: nzFormat }}</ng-template>\n  `,\n      imports: [NzStatisticComponent, NzPipesModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.Platform\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzFormat: [{\n      type: Input\n    }],\n    nzCountdownFinish: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzStatisticModule {\n  static {\n    this.ɵfac = function NzStatisticModule_Factory(t) {\n      return new (t || NzStatisticModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzStatisticModule,\n      imports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent],\n      exports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzStatisticComponent, NzCountdownComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzStatisticModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent],\n      exports: [NzStatisticComponent, NzCountdownComponent, NzStatisticNumberComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCountdownComponent, NzStatisticComponent, NzStatisticModule, NzStatisticNumberComponent };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO,SAAS,YAAY;AACpC,QAAI,WAAW,OAAO,SAAS,CAAC;AAChC,WAAO,UAAU,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,MAAM;AACjD,UAAI,QAAQ,QAAQ,IAAI,MAAM,IAAI;AAChC,cAAM,IAAI,KAAK,MAAM,WAAW,IAAI;AACpC,oBAAY,IAAI;AAChB,eAAO,QAAQ,QAAQ,IAAI,OAAO,GAAG,IAAI,KAAK,GAAG,GAAG,WAAS,SAAS,EAAE,SAAS,GAAG,MAAM,QAAQ,GAAG,CAAC;AAAA,MACxG;AACA,aAAO;AAAA,IACT,GAAG,MAAM;AAAA,EACX;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAiB;AAAA,IACpC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,eAAe;AAAA,MACzB,SAAS,CAAC,eAAe;AAAA,IAC3B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;AC3DH,IAAM,MAAM,SAAO;AAAA,EACjB,WAAW;AACb;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,GAAG,CAAC;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,WAAW,oBAAoB,OAAO,eAAe,EAAE,2BAA8B,gBAAgB,GAAG,KAAK,OAAO,OAAO,CAAC;AAAA,EACjI;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,UAAU;AAAA,EACxC;AACF;AACA,SAAS,0DAA0D,IAAI,KAAK;AAC1E,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,OAAO,CAAC;AACX,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,cAAc;AAAA,EAC5C;AACF;AACA,SAAS,mDAAmD,IAAI,KAAK;AACnE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,WAAW,GAAG,2DAA2D,GAAG,GAAG,QAAQ,CAAC,EAAE,GAAG,2DAA2D,GAAG,GAAG,QAAQ,CAAC;AAC1K,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,UAAU;AACvC,IAAG,UAAU;AACb,IAAG,WAAW,QAAQ,OAAO,cAAc;AAAA,EAC7C;AACF;AACA,SAAS,6CAA6C,IAAI,KAAK;AAC7D,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,OAAO;AAAA,EACrC;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAC7F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,QAAQ;AAAA,EACzD;AACF;AACA,SAAS,oDAAoD,IAAI,KAAK;AACpE,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,OAAO,CAAC;AACX,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,kBAAkB,OAAO,QAAQ;AAAA,EACtC;AACF;AACA,SAAS,qCAAqC,IAAI,KAAK;AACrD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,IAAG,WAAW,GAAG,qDAAqD,GAAG,GAAG,gBAAgB,CAAC;AAC7F,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,QAAQ;AAAA,EACzD;AACF;AACA,SAAS,4CAA4C,IAAI,KAAK;AAC5D,MAAI,KAAK,GAAG;AACV,IAAG,OAAO,CAAC;AACX,IAAG,OAAO,GAAG,aAAa;AAAA,EAC5B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,kBAAqB,YAAY,GAAG,GAAG,OAAO,MAAM,OAAO,QAAQ,CAAC;AAAA,EACzE;AACF;AACA,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,cAAc;AACZ,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,eAAe;AACb,UAAM,mBAAmB,OAAO,KAAK,YAAY,WAAW,MAAM,sBAAsB,KAAK,WAAW,aAAa,OAAO;AAC5H,UAAM,QAAQ,OAAO,KAAK,OAAO;AACjC,UAAM,CAAC,KAAK,OAAO,IAAI,MAAM,MAAM,gBAAgB;AACnD,SAAK,aAAa;AAClB,SAAK,iBAAiB,UAAU,GAAG,gBAAgB,GAAG,OAAO,KAAK;AAAA,EACpE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,GAAG;AACzD,aAAO,KAAK,KAAK,6BAA+B,kBAAkB,SAAS,CAAC;AAAA,IAC9E;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAAA,MACnC,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,mBAAmB;AAAA,MAC9B,YAAY;AAAA,MACZ,UAAU,CAAI,sBAAyB,mBAAmB;AAAA,MAC1D,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,6BAA6B,GAAG,CAAC,GAAG,oBAAoB,2BAA2B,GAAG,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,SAAS,mCAAmC,GAAG,MAAM,GAAG,CAAC,SAAS,uCAAuC,GAAG,MAAM,GAAG,CAAC,GAAG,iCAAiC,GAAG,CAAC,GAAG,qCAAqC,CAAC;AAAA,MACrX,UAAU,SAAS,oCAAoC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,QAAQ,CAAC;AAC9B,UAAG,WAAW,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC,EAAE,GAAG,oDAAoD,GAAG,GAAG,gBAAgB,CAAC;AAC5K,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,eAAe;AACzC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,CAAC,IAAI,eAAe;AAAA,QAC5C;AAAA,MACF;AAAA,MACA,cAAc,CAAC,MAAM,gBAAgB;AAAA,MACrC,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaV,SAAS,CAAC,MAAM,gBAAgB;AAAA,MAChC,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,SAAS;AAAA,IAClB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,YAAY,KAAK,gBAAgB;AAC/B,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,eAAe,CAAC;AACrB,SAAK,MAAM;AACX,SAAK,WAAW,IAAI,QAAQ;AAAA,EAC9B;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,WAAW,CAAC,GAAG,eAAe;AAAA,MAC9B,UAAU;AAAA,MACV,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,qBAAqB,IAAI,QAAQ,KAAK;AAAA,QACvD;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,UAAU;AAAA,QACV,UAAU;AAAA,QACV,SAAS;AAAA,QACT,SAAS;AAAA,QACT,cAAc;AAAA,QACd,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB;AAAA,MACjC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,qBAAqB,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,yBAAyB,GAAG,SAAS,GAAG,CAAC,SAAS,gCAAgC,GAAG,MAAM,GAAG,CAAC,GAAG,WAAW,iBAAiB,GAAG,CAAC,SAAS,gCAAgC,GAAG,MAAM,GAAG,CAAC,GAAG,8BAA8B,GAAG,CAAC,GAAG,8BAA8B,CAAC;AAAA,MACvU,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,8CAA8C,GAAG,GAAG,gBAAgB,CAAC;AACtF,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AACtE,UAAG,UAAU,GAAG,uBAAuB,CAAC;AACxC,UAAG,WAAW,GAAG,sCAAsC,GAAG,GAAG,QAAQ,CAAC;AACtE,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,UAAU;AACb,UAAG,WAAW,0BAA0B,IAAI,OAAO;AACnD,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,YAAY;AACzC,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,QAAQ;AAClC,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,mBAAmB,IAAI,eAAe;AAC5E,UAAG,UAAU;AACb,UAAG,WAAW,QAAQ,IAAI,QAAQ;AAAA,QACpC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,4BAA4B,MAAM,gBAAmB,iCAAiC,OAAO;AAAA,MAC5G,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcV,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,6BAA6B;AAAA,MAC/B;AAAA,MACA,SAAS,CAAC,4BAA4B,MAAM,gBAAgB,OAAO;AAAA,MACnE,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAmB,MAAO;AAChC,IAAM,uBAAN,MAAM,8BAA6B,qBAAqB;AAAA,EACtD,YAAY,KAAK,QAAQ,UAAU,gBAAgB;AACjD,UAAM,KAAK,cAAc;AACzB,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,oBAAoB,IAAI,aAAa;AAC1C,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,YAAY,SAAS;AACnB,QAAI,QAAQ,SAAS;AACnB,WAAK,SAAS,OAAO,QAAQ,QAAQ,YAAY;AACjD,UAAI,CAAC,QAAQ,QAAQ,cAAc,GAAG;AACpC,aAAK,UAAU;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EACA,WAAW;AACT,UAAM,SAAS;AACf,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,cAAc;AACZ,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU,KAAK,IAAI,GAAG;AAC7B,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,UAAU;AAAA,IACjB;AAAA,EACF;AAAA,EACA,aAAa;AACX,QAAI,KAAK,SAAS,WAAW;AAC3B,WAAK,OAAO,kBAAkB,MAAM;AAClC,aAAK,UAAU;AACf,aAAK,WAAW,SAAS,gBAAgB,EAAE,UAAU,MAAM;AACzD,eAAK,YAAY;AACjB,eAAK,IAAI,cAAc;AAAA,QACzB,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,KAAK,UAAU;AACjB,WAAK,SAAS,YAAY;AAC1B,WAAK,WAAW;AAAA,IAClB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACZ,SAAK,OAAO,KAAK,IAAI,KAAK,SAAS,KAAK,IAAI,GAAG,CAAC;AAChD,QAAI,KAAK,SAAS,GAAG;AACnB,WAAK,UAAU;AACf,UAAI,KAAK,kBAAkB,UAAU,QAAQ;AAC3C,aAAK,OAAO,IAAI,MAAM,KAAK,kBAAkB,KAAK,CAAC;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,GAAG;AACnD,aAAO,KAAK,KAAK,uBAAyB,kBAAqB,iBAAiB,GAAM,kBAAqB,MAAM,GAAM,kBAAuB,QAAQ,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACrM;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,cAAc,CAAC;AAAA,MAC5B,QAAQ;AAAA,QACN,UAAU;AAAA,MACZ;AAAA,MACA,SAAS;AAAA,QACP,mBAAmB;AAAA,MACrB;AAAA,MACA,UAAU,CAAC,aAAa;AAAA,MACxB,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,sBAAyB,mBAAmB;AAAA,MACzF,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,WAAW,gBAAgB,mBAAmB,WAAW,YAAY,UAAU,CAAC;AAAA,MACnH,UAAU,SAAS,8BAA8B,IAAI,KAAK;AACxD,YAAI,KAAK,GAAG;AACV,UAAG,UAAU,GAAG,gBAAgB,CAAC;AACjC,UAAG,WAAW,GAAG,6CAA6C,GAAG,GAAG,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACvH;AACA,YAAI,KAAK,GAAG;AACV,gBAAM,kBAAqB,YAAY,CAAC;AACxC,UAAG,WAAW,WAAW,IAAI,IAAI,EAAE,gBAAgB,IAAI,YAAY,EAAE,mBAAmB,IAAI,mBAAmB,eAAe,EAAE,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,YAAY,IAAI,QAAQ;AAAA,QAC5M;AAAA,MACF;AAAA,MACA,cAAc,CAAC,sBAAsB,eAAkB,eAAe;AAAA,MACtE,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,iBAAiB,wBAAwB;AAAA,MACzC,eAAe,oBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAYV,SAAS,CAAC,sBAAsB,aAAa;AAAA,MAC7C,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,mBAAmB,CAAC;AAAA,MAClB,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAmB;AAAA,IACtC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,sBAAsB,sBAAsB,0BAA0B;AAAA,MAChF,SAAS,CAAC,sBAAsB,sBAAsB,0BAA0B;AAAA,IAClF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,sBAAsB,oBAAoB;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,sBAAsB,sBAAsB,0BAA0B;AAAA,MAChF,SAAS,CAAC,sBAAsB,sBAAsB,0BAA0B;AAAA,IAClF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}