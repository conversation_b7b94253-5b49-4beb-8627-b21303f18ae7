{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-radio.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { Injectable, forwardRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Input, booleanAttribute, Inject, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { ReplaySubject, Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/a11y';\nimport * as i3 from 'ng-zorro-antd/core/form';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-radio\", \"\"];\nclass NzRadioService {\n  constructor() {\n    this.selected$ = new ReplaySubject(1);\n    this.touched$ = new Subject();\n    this.disabled$ = new ReplaySubject(1);\n    this.name$ = new ReplaySubject(1);\n  }\n  touch() {\n    this.touched$.next();\n  }\n  select(value) {\n    this.selected$.next(value);\n  }\n  setDisabled(value) {\n    this.disabled$.next(value);\n  }\n  setName(value) {\n    this.name$.next(value);\n  }\n  static {\n    this.ɵfac = function NzRadioService_Factory(t) {\n      return new (t || NzRadioService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzRadioService,\n      factory: NzRadioService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioService, [{\n    type: Injectable\n  }], null, null);\n})();\nclass NzRadioGroupComponent {\n  constructor(cdr, nzRadioService, directionality) {\n    this.cdr = cdr;\n    this.nzRadioService = nzRadioService;\n    this.directionality = directionality;\n    this.value = null;\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzDisabled = false;\n    this.nzButtonStyle = 'outline';\n    this.nzSize = 'default';\n    this.nzName = null;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n      if (this.value !== value) {\n        this.value = value;\n        this.onChange(this.value);\n      }\n    });\n    this.nzRadioService.touched$.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      Promise.resolve().then(() => this.onTouched());\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzDisabled,\n      nzName\n    } = changes;\n    if (nzDisabled) {\n      this.nzRadioService.setDisabled(this.nzDisabled);\n    }\n    if (nzName) {\n      this.nzRadioService.setName(this.nzName);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.value = value;\n    this.nzRadioService.select(value);\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(isDisabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || isDisabled;\n    this.isNzDisableFirstChange = false;\n    this.nzRadioService.setDisabled(this.nzDisabled);\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzRadioGroupComponent_Factory(t) {\n      return new (t || NzRadioGroupComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzRadioService), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzRadioGroupComponent,\n      selectors: [[\"nz-radio-group\"]],\n      hostAttrs: [1, \"ant-radio-group\"],\n      hostVars: 8,\n      hostBindings: function NzRadioGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-radio-group-large\", ctx.nzSize === \"large\")(\"ant-radio-group-small\", ctx.nzSize === \"small\")(\"ant-radio-group-solid\", ctx.nzButtonStyle === \"solid\")(\"ant-radio-group-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzDisabled: \"nzDisabled\",\n        nzButtonStyle: \"nzButtonStyle\",\n        nzSize: \"nzSize\",\n        nzName: \"nzName\"\n      },\n      exportAs: [\"nzRadioGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzRadioService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioGroupComponent),\n        multi: true\n      }]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzRadioGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzRadioGroupComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-radio-group',\n      exportAs: 'nzRadioGroup',\n      preserveWhitespaces: false,\n      template: ` <ng-content></ng-content> `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [NzRadioService, {\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioGroupComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-radio-group',\n        '[class.ant-radio-group-large]': `nzSize === 'large'`,\n        '[class.ant-radio-group-small]': `nzSize === 'small'`,\n        '[class.ant-radio-group-solid]': `nzButtonStyle === 'solid'`,\n        '[class.ant-radio-group-rtl]': `dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzRadioService\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzDisabled: [{\n      type: Input\n    }],\n    nzButtonStyle: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzName: [{\n      type: Input\n    }]\n  });\n})();\nclass NzRadioComponent {\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(ngZone, elementRef, cdr, focusMonitor, directionality, nzRadioService, nzFormStatusService) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this.nzRadioService = nzRadioService;\n    this.nzFormStatusService = nzFormStatusService;\n    this.isNgModel = false;\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n    this.isChecked = false;\n    this.name = null;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzValue = null;\n    this.nzDisabled = false;\n    this.nzAutoFocus = false;\n    this.isRadioButton = false;\n    this.dir = 'ltr';\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  writeValue(value) {\n    this.isChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.isNgModel = true;\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  ngOnInit() {\n    if (this.nzRadioService) {\n      this.nzRadioService.name$.pipe(takeUntil(this.destroy$)).subscribe(name => {\n        this.name = name;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.disabled$.pipe(takeUntil(this.destroy$)).subscribe(disabled => {\n        this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n      });\n      this.nzRadioService.selected$.pipe(takeUntil(this.destroy$)).subscribe(value => {\n        const isChecked = this.isChecked;\n        this.isChecked = this.nzValue === value;\n        // We don't have to run `onChange()` on each `nz-radio` button whenever the `selected$` emits.\n        // If we have 8 `nz-radio` buttons within the `nz-radio-group` and they're all connected with\n        // `ngModel` or `formControl` then `onChange()` will be called 8 times for each `nz-radio` button.\n        // We prevent this by checking if `isChecked` has been changed or not.\n        if (this.isNgModel && isChecked !== this.isChecked &&\n        // We're only intereted if `isChecked` has been changed to `false` value to emit `false` to the ascendant form,\n        // since we already emit `true` within the `setupClickListener`.\n        this.isChecked === false) {\n          this.onChange(false);\n        }\n        this.cdr.markForCheck();\n      });\n    }\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n        if (this.nzRadioService) {\n          this.nzRadioService.touch();\n        }\n      }\n    });\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.setupClickListener();\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    this.focusMonitor.stopMonitoring(this.elementRef);\n  }\n  setupClickListener() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        /** prevent label click triggered twice. **/\n        event.stopPropagation();\n        event.preventDefault();\n        if (this.nzDisabled || this.isChecked) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.focus();\n          this.nzRadioService?.select(this.nzValue);\n          if (this.isNgModel) {\n            this.isChecked = true;\n            this.onChange(true);\n          }\n          this.cdr.markForCheck();\n        });\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NzRadioComponent_Factory(t) {\n      return new (t || NzRadioComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(NzRadioService, 8), i0.ɵɵdirectiveInject(i3.NzFormStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzRadioComponent,\n      selectors: [[\"\", \"nz-radio\", \"\"], [\"\", \"nz-radio-button\", \"\"]],\n      viewQuery: function NzRadioComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      hostVars: 18,\n      hostBindings: function NzRadioComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-radio-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-radio-wrapper\", !ctx.isRadioButton)(\"ant-radio-button-wrapper\", ctx.isRadioButton)(\"ant-radio-wrapper-checked\", ctx.isChecked && !ctx.isRadioButton)(\"ant-radio-button-wrapper-checked\", ctx.isChecked && ctx.isRadioButton)(\"ant-radio-wrapper-disabled\", ctx.nzDisabled && !ctx.isRadioButton)(\"ant-radio-button-wrapper-disabled\", ctx.nzDisabled && ctx.isRadioButton)(\"ant-radio-wrapper-rtl\", !ctx.isRadioButton && ctx.dir === \"rtl\")(\"ant-radio-button-wrapper-rtl\", ctx.isRadioButton && ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzValue: \"nzValue\",\n        nzDisabled: \"nzDisabled\",\n        nzAutoFocus: \"nzAutoFocus\",\n        isRadioButton: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"nz-radio-button\", \"isRadioButton\", booleanAttribute]\n      },\n      exportAs: [\"nzRadio\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioComponent),\n        multi: true\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 24,\n      consts: [[\"inputElement\", \"\"], [\"type\", \"radio\", 3, \"disabled\", \"checked\"]],\n      template: function NzRadioComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\");\n          i0.ɵɵelement(1, \"input\", 1, 0)(3, \"span\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-radio\", !ctx.isRadioButton)(\"ant-radio-checked\", ctx.isChecked && !ctx.isRadioButton)(\"ant-radio-disabled\", ctx.nzDisabled && !ctx.isRadioButton)(\"ant-radio-button\", ctx.isRadioButton)(\"ant-radio-button-checked\", ctx.isChecked && ctx.isRadioButton)(\"ant-radio-button-disabled\", ctx.nzDisabled && ctx.isRadioButton);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-radio-input\", !ctx.isRadioButton)(\"ant-radio-button-input\", ctx.isRadioButton);\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"checked\", ctx.isChecked);\n          i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"name\", ctx.name);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"ant-radio-inner\", !ctx.isRadioButton)(\"ant-radio-button-inner\", ctx.isRadioButton);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzRadioComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzRadioComponent.prototype, \"nzAutoFocus\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-radio],[nz-radio-button]',\n      exportAs: 'nzRadio',\n      preserveWhitespaces: false,\n      template: `\n    <span\n      [class.ant-radio]=\"!isRadioButton\"\n      [class.ant-radio-checked]=\"isChecked && !isRadioButton\"\n      [class.ant-radio-disabled]=\"nzDisabled && !isRadioButton\"\n      [class.ant-radio-button]=\"isRadioButton\"\n      [class.ant-radio-button-checked]=\"isChecked && isRadioButton\"\n      [class.ant-radio-button-disabled]=\"nzDisabled && isRadioButton\"\n    >\n      <input\n        #inputElement\n        type=\"radio\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [class.ant-radio-input]=\"!isRadioButton\"\n        [class.ant-radio-button-input]=\"isRadioButton\"\n        [disabled]=\"nzDisabled\"\n        [checked]=\"isChecked\"\n        [attr.name]=\"name\"\n      />\n      <span [class.ant-radio-inner]=\"!isRadioButton\" [class.ant-radio-button-inner]=\"isRadioButton\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzRadioComponent),\n        multi: true\n      }],\n      host: {\n        '[class.ant-radio-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-radio-wrapper]': '!isRadioButton',\n        '[class.ant-radio-button-wrapper]': 'isRadioButton',\n        '[class.ant-radio-wrapper-checked]': 'isChecked && !isRadioButton',\n        '[class.ant-radio-button-wrapper-checked]': 'isChecked && isRadioButton',\n        '[class.ant-radio-wrapper-disabled]': 'nzDisabled && !isRadioButton',\n        '[class.ant-radio-button-wrapper-disabled]': 'nzDisabled && isRadioButton',\n        '[class.ant-radio-wrapper-rtl]': `!isRadioButton && dir === 'rtl'`,\n        '[class.ant-radio-button-wrapper-rtl]': `isRadioButton && dir === 'rtl'`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: NzRadioService,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [NzRadioService]\n    }]\n  }, {\n    type: i3.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input\n    }],\n    isRadioButton: [{\n      type: Input,\n      args: [{\n        alias: 'nz-radio-button',\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRadioModule {\n  static {\n    this.ɵfac = function NzRadioModule_Factory(t) {\n      return new (t || NzRadioModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzRadioModule,\n      imports: [NzRadioComponent, NzRadioGroupComponent],\n      exports: [NzRadioComponent, NzRadioGroupComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRadioModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzRadioComponent, NzRadioGroupComponent],\n      exports: [NzRadioComponent, NzRadioGroupComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzRadioComponent, NzRadioGroupComponent, NzRadioModule, NzRadioService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAM,MAAM,CAAC,GAAG;AAChB,IAAM,MAAM,CAAC,cAAc;AAC3B,IAAM,MAAM,CAAC,YAAY,EAAE;AAC3B,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,cAAc;AACZ,SAAK,YAAY,IAAI,cAAc,CAAC;AACpC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,YAAY,IAAI,cAAc,CAAC;AACpC,SAAK,QAAQ,IAAI,cAAc,CAAC;AAAA,EAClC;AAAA,EACA,QAAQ;AACN,SAAK,SAAS,KAAK;AAAA,EACrB;AAAA,EACA,OAAO,OAAO;AACZ,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,YAAY,OAAO;AACjB,SAAK,UAAU,KAAK,KAAK;AAAA,EAC3B;AAAA,EACA,QAAQ,OAAO;AACb,SAAK,MAAM,KAAK,KAAK;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,gBAAe;AAAA,IAC1B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,YAAY,KAAK,gBAAgB,gBAAgB;AAC/C,SAAK,MAAM;AACX,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,QAAQ;AACb,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,aAAa;AAClB,SAAK,gBAAgB;AACrB,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AACT,SAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC9E,UAAI,KAAK,UAAU,OAAO;AACxB,aAAK,QAAQ;AACb,aAAK,SAAS,KAAK,KAAK;AAAA,MAC1B;AAAA,IACF,CAAC;AACD,SAAK,eAAe,SAAS,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC1E,cAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAAA,IAC/C,CAAC;AACD,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAAA,EACjC;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,YAAY;AACd,WAAK,eAAe,YAAY,KAAK,UAAU;AAAA,IACjD;AACA,QAAI,QAAQ;AACV,WAAK,eAAe,QAAQ,KAAK,MAAM;AAAA,IACzC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK,IAAI;AACvB,SAAK,SAAS,SAAS;AAAA,EACzB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,QAAQ;AACb,SAAK,eAAe,OAAO,KAAK;AAChC,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,iBAAiB,YAAY;AAC3B,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,eAAe,YAAY,KAAK,UAAU;AAC/C,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,GAAG;AACpD,aAAO,KAAK,KAAK,wBAA0B,kBAAqB,iBAAiB,GAAM,kBAAkB,cAAc,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACtK;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,gBAAgB,CAAC;AAAA,MAC9B,WAAW,CAAC,GAAG,iBAAiB;AAAA,MAChC,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,yBAAyB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,WAAW,OAAO,EAAE,yBAAyB,IAAI,kBAAkB,OAAO,EAAE,uBAAuB,IAAI,QAAQ,KAAK;AAAA,QACnN;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,YAAY;AAAA,QACZ,eAAe;AAAA,QACf,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AAAA,MACA,UAAU,CAAC,cAAc;AAAA,MACzB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC,gBAAgB;AAAA,QAChD,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,sBAAqB;AAAA,QACnD,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,sBAAyB,mBAAmB;AAAA,MACpD,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU,SAAS,+BAA+B,IAAI,KAAK;AACzD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,aAAa,CAAC;AAAA,QACnB;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,sBAAsB,WAAW,cAAc,MAAM;AAAA,CACjF,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC,gBAAgB;AAAA,QAC1B,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,qBAAqB;AAAA,QACnD,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,OAAO;AAAA,QACP,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,iCAAiC;AAAA,QACjC,+BAA+B;AAAA,MACjC;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACrB,QAAQ;AACN,SAAK,aAAa,SAAS,KAAK,cAAc,UAAU;AAAA,EAC1D;AAAA,EACA,OAAO;AACL,SAAK,aAAa,cAAc,KAAK;AAAA,EACvC;AAAA,EACA,YAAY,QAAQ,YAAY,KAAK,cAAc,gBAAgB,gBAAgB,qBAAqB;AACtG,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,MAAM;AACX,SAAK,eAAe;AACpB,SAAK,iBAAiB;AACtB,SAAK,iBAAiB;AACtB,SAAK,sBAAsB;AAC3B,SAAK,YAAY;AACjB,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,yBAAyB;AAC9B,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,WAAW,MAAM;AAAA,IAAC;AACvB,SAAK,YAAY,MAAM;AAAA,IAAC;AACxB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,gBAAgB;AACrB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,iBAAiB,UAAU;AACzB,SAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,SAAK,yBAAyB;AAC9B,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,WAAW,OAAO;AAChB,SAAK,YAAY;AACjB,SAAK,IAAI,aAAa;AAAA,EACxB;AAAA,EACA,iBAAiB,IAAI;AACnB,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,kBAAkB,IAAI;AACpB,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,WAAW;AACT,QAAI,KAAK,gBAAgB;AACvB,WAAK,eAAe,MAAM,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,UAAQ;AACzE,aAAK,OAAO;AACZ,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,cAAY;AACjF,aAAK,aAAa,KAAK,0BAA0B,KAAK,cAAc;AACpE,aAAK,yBAAyB;AAC9B,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AACD,WAAK,eAAe,UAAU,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAC9E,cAAM,YAAY,KAAK;AACvB,aAAK,YAAY,KAAK,YAAY;AAKlC,YAAI,KAAK,aAAa,cAAc,KAAK;AAAA;AAAA,QAGzC,KAAK,cAAc,OAAO;AACxB,eAAK,SAAS,KAAK;AAAA,QACrB;AACA,aAAK,IAAI,aAAa;AAAA,MACxB,CAAC;AAAA,IACH;AACA,SAAK,aAAa,QAAQ,KAAK,YAAY,IAAI,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,iBAAe;AACvG,UAAI,CAAC,aAAa;AAChB,gBAAQ,QAAQ,EAAE,KAAK,MAAM,KAAK,UAAU,CAAC;AAC7C,YAAI,KAAK,gBAAgB;AACvB,eAAK,eAAe,MAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,eAAe,OAAO,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAC/E,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe;AAC/B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,kBAAkB;AAChB,QAAI,KAAK,aAAa;AACpB,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,SAAK,aAAa,eAAe,KAAK,UAAU;AAAA,EAClD;AAAA,EACA,qBAAqB;AACnB,SAAK,OAAO,kBAAkB,MAAM;AAClC,gBAAU,KAAK,WAAW,eAAe,OAAO,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAElG,cAAM,gBAAgB;AACtB,cAAM,eAAe;AACrB,YAAI,KAAK,cAAc,KAAK,WAAW;AACrC;AAAA,QACF;AACA,aAAK,OAAO,IAAI,MAAM;AACpB,eAAK,MAAM;AACX,eAAK,gBAAgB,OAAO,KAAK,OAAO;AACxC,cAAI,KAAK,WAAW;AAClB,iBAAK,YAAY;AACjB,iBAAK,SAAS,IAAI;AAAA,UACpB;AACA,eAAK,IAAI,aAAa;AAAA,QACxB,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yBAAyB,GAAG;AAC/C,aAAO,KAAK,KAAK,mBAAqB,kBAAqB,MAAM,GAAM,kBAAqB,UAAU,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,YAAY,GAAM,kBAAqB,gBAAgB,CAAC,GAAM,kBAAkB,gBAAgB,CAAC,GAAM,kBAAqB,qBAAqB,CAAC,CAAC;AAAA,IAClU;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MAC7D,WAAW,SAAS,uBAAuB,IAAI,KAAK;AAClD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,eAAe,GAAG;AAAA,QACrE;AAAA,MACF;AAAA,MACA,UAAU;AAAA,MACV,cAAc,SAAS,8BAA8B,IAAI,KAAK;AAC5D,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,kCAAkC,CAAC,CAAC,IAAI,mBAAmB,EAAE,qBAAqB,CAAC,IAAI,aAAa,EAAE,4BAA4B,IAAI,aAAa,EAAE,6BAA6B,IAAI,aAAa,CAAC,IAAI,aAAa,EAAE,oCAAoC,IAAI,aAAa,IAAI,aAAa,EAAE,8BAA8B,IAAI,cAAc,CAAC,IAAI,aAAa,EAAE,qCAAqC,IAAI,cAAc,IAAI,aAAa,EAAE,yBAAyB,CAAC,IAAI,iBAAiB,IAAI,QAAQ,KAAK,EAAE,gCAAgC,IAAI,iBAAiB,IAAI,QAAQ,KAAK;AAAA,QACtkB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,eAAe,CAAI,WAAa,4BAA4B,mBAAmB,iBAAiB,gBAAgB;AAAA,MAClH;AAAA,MACA,UAAU,CAAC,SAAS;AAAA,MACpB,YAAY;AAAA,MACZ,UAAU,CAAI,mBAAmB,CAAC;AAAA,QAChC,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,iBAAgB;AAAA,QAC9C,OAAO;AAAA,MACT,CAAC,CAAC,GAAM,0BAA6B,mBAAmB;AAAA,MACxD,OAAO;AAAA,MACP,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,gBAAgB,EAAE,GAAG,CAAC,QAAQ,SAAS,GAAG,YAAY,SAAS,CAAC;AAAA,MAC1E,UAAU,SAAS,0BAA0B,IAAI,KAAK;AACpD,YAAI,KAAK,GAAG;AACV,UAAG,gBAAgB;AACnB,UAAG,eAAe,GAAG,MAAM;AAC3B,UAAG,UAAU,GAAG,SAAS,GAAG,CAAC,EAAE,GAAG,MAAM;AACxC,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,MAAM;AAC3B,UAAG,aAAa,CAAC;AACjB,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,CAAC,IAAI,aAAa,EAAE,qBAAqB,IAAI,aAAa,CAAC,IAAI,aAAa,EAAE,sBAAsB,IAAI,cAAc,CAAC,IAAI,aAAa,EAAE,oBAAoB,IAAI,aAAa,EAAE,4BAA4B,IAAI,aAAa,IAAI,aAAa,EAAE,6BAA6B,IAAI,cAAc,IAAI,aAAa;AAC7U,UAAG,UAAU;AACb,UAAG,YAAY,mBAAmB,CAAC,IAAI,aAAa,EAAE,0BAA0B,IAAI,aAAa;AACjG,UAAG,WAAW,YAAY,IAAI,UAAU,EAAE,WAAW,IAAI,SAAS;AAClE,UAAG,YAAY,aAAa,IAAI,cAAc,cAAc,IAAI,EAAE,QAAQ,IAAI,IAAI;AAClF,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,mBAAmB,CAAC,IAAI,aAAa,EAAE,0BAA0B,IAAI,aAAa;AAAA,QACnG;AAAA,MACF;AAAA,MACA,eAAe;AAAA,MACf,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,cAAc,MAAM;AAC7E,WAAW,CAAC,aAAa,CAAC,GAAG,iBAAiB,WAAW,eAAe,MAAM;AAAA,CAC7E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kBAAkB,CAAC;AAAA,IACzF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,qBAAqB;AAAA,MACrB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAuBV,eAAe,oBAAkB;AAAA,MACjC,iBAAiB,wBAAwB;AAAA,MACzC,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,aAAa,WAAW,MAAM,gBAAgB;AAAA,QAC9C,OAAO;AAAA,MACT,CAAC;AAAA,MACD,MAAM;AAAA,QACJ,0CAA0C;AAAA,QAC1C,6BAA6B;AAAA,QAC7B,oCAAoC;AAAA,QACpC,qCAAqC;AAAA,QACrC,4CAA4C;AAAA,QAC5C,sCAAsC;AAAA,QACtC,6CAA6C;AAAA,QAC7C,iCAAiC;AAAA,QACjC,wCAAwC;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,cAAc;AAAA,IACvB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,QACrB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,MACN,MAAM,CAAC;AAAA,QACL,OAAO;AAAA,QACP,WAAW;AAAA,MACb,CAAC;AAAA,IACH,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAMH,IAAM,gBAAN,MAAM,eAAc;AAAA,EAClB,OAAO;AACL,SAAK,OAAO,SAAS,sBAAsB,GAAG;AAC5C,aAAO,KAAK,KAAK,gBAAe;AAAA,IAClC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,MACjD,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,eAAe,CAAC;AAAA,IACtF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,MACjD,SAAS,CAAC,kBAAkB,qBAAqB;AAAA,IACnD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}