import { DateTime } from 'luxon';

// Base interface for performance metrics
export interface PerformanceMetrics {
  totalTasks: number;
  successRate: number;
  averageTime: number;
  failedAttempts: number;
  failureRate: number;
}

// Interface for time-based performance steps
export interface TaskStep {
  stepName: string;
  averageTime: number;
  description?: string;
}

// Interface for quality metrics
export interface QualityMetrics {
  grammarQuality: number;
  readabilityScore: number;
  copiedContent: number;
  overallQuality: number;
}

// Interface for failed tasks
export interface FailedTask {
  taskId: string;
  reason: string;
  timestamp: DateTime;
  details?: string;
}

// Interface for performance insights
export interface PerformanceInsight {
  type: 'positive' | 'negative' | 'neutral';
  title: string;
  description: string;
  suggestion?: string;
}

// Interface for time-based activity
export interface ActivityPeriod {
  period: string;
  taskCount: number;
  averageTime: number;
  successRate: number;
}

// Main agent performance interface
export interface AgentPerformance {
  agentId: string;
  agentName: string;
  agentType: 'AI' | 'Human';
  workArea: string;
  reportDuration: {
    startDate: DateTime;
    endDate: DateTime;
  };

  // Core metrics
  metrics: PerformanceMetrics;

  // Detailed step breakdown
  taskSteps: TaskStep[];

  // Quality assessment
  quality: QualityMetrics;

  // Failed tasks
  failedTasks: FailedTask[];

  // Performance insights
  insights: PerformanceInsight[];

  // Activity patterns
  activityPeriods: ActivityPeriod[];

  // Additional metadata
  lastUpdated: DateTime;
  status: 'active' | 'inactive' | 'maintenance';
}

// Interface for task-specific performance
export interface TaskPerformance {
  taskType: string;
  taskName: string;
  description: string;
  humanAgent?: AgentPerformance;
  aiAgent?: AgentPerformance;
  comparisonResult?: TaskComparisonResult;
}

// Interface for task comparison result
export interface TaskComparisonResult {
  winner: 'human' | 'ai' | 'tie';
  speedAdvantage: {
    winner: 'human' | 'ai' | 'tie';
    difference: number; // percentage or absolute difference
    humanTime: number;
    aiTime: number;
  };
  qualityAdvantage: {
    winner: 'human' | 'ai' | 'tie';
    difference: number;
    humanQuality: number;
    aiQuality: number;
  };
  reliabilityAdvantage: {
    winner: 'human' | 'ai' | 'tie';
    difference: number;
    humanReliability: number;
    aiReliability: number;
  };
  costEfficiency: {
    winner: 'human' | 'ai' | 'tie';
    reasoning: string;
  };
  overallRecommendation: string;
}

// Interface for agent comparison
export interface AgentComparison {
  agent1: AgentPerformance;
  agent2: AgentPerformance;
  comparisonMetrics: {
    taskEfficiency: number; // Percentage difference
    qualityDifference: number;
    reliabilityDifference: number;
    speedDifference: number;
  };
  recommendations: string[];
  taskComparison?: TaskComparisonResult; // Added for same-task comparisons
}

// Interface for dashboard summary
export interface AgentAnalyticsSummary {
  totalAgents: number;
  activeAgents: number;
  averagePerformance: number;
  topPerformers: AgentPerformance[];
  underPerformers: AgentPerformance[];
  recentComparisons: AgentComparison[];
}

// Interface for chart data
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string[];
    borderWidth?: number;
  }[];
}

// Interface for filter options
export interface AnalyticsFilter {
  dateRange: {
    startDate: DateTime;
    endDate: DateTime;
  };
  agentTypes: ('AI' | 'Human')[];
  workAreas: string[];
  status: ('active' | 'inactive' | 'maintenance')[];
  performanceThreshold?: number;
}

// Enum for metric types
export enum MetricType {
  EFFICIENCY = 'efficiency',
  QUALITY = 'quality',
  RELIABILITY = 'reliability',
  SPEED = 'speed'
}

// Interface for metric comparison
export interface MetricComparison {
  metricType: MetricType;
  agent1Value: number;
  agent2Value: number;
  difference: number;
  percentageDifference: number;
  winner: 'agent1' | 'agent2' | 'tie';
}
