﻿using Azure.Search.Documents.Models;
using Dapper;
using DocumentFormat.OpenXml.Office.SpreadSheetML.Y2023.MsForms;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Mvc;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using OllamaSharp;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using ProjectApp.Infrastructure.Services;
using System.Data;
using System.Globalization;
using System.Text;

namespace ProjectApp.Infrastructure
{
    public class AIService(Kernel _kernel, IKernelMemory _memory,
        IChatCompletionService _chatCompletionService, IDbConnection _dbConnection,
        IAssignWorkspaceRepository _assignWorkspaceRepository, IProjectCategoryRepository _projectCategoryRepository, AIAgentFactory agentFactory,
        IAgentDefinitionRepository _agentDefinitionRepository, IChatHistoryRepository _chatHistoryRepository, AIAgentFactory _aiAgentFactory)
    {
        public async Task<List<string>> SearchMemory(string question, string index, List<MemoryTag> tags = null, double minRelevance = 0.4)
        {
            var filter = new MemoryFilter();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    filter.ByTag(memoryTag.Name, memoryTag.Value);
                }
            }

            var memoryAnswer = await _memory.SearchAsync(question, index: index, filter: filter, minRelevance: minRelevance);
            var answer = memoryAnswer.Results.SelectMany(result => result.Partitions.Select(p => p.Text)).ToList();
            return answer;
        }

        public async IAsyncEnumerable<ResponseMessage> GetAIAnswer(string question, string index, string sessionId, List<MemoryTag> tags = null)
        {
            // Get chat history
            var chatHistory = await _chatHistoryRepository.GetChatHistoryBySessionId(sessionId);
            var previousContext = chatHistory.Select(ch => $"User: {ch.Question}\nAssistant: {ch.Answer}").ToList();

            // Format previous chat history context
            var chatHistoryContext = previousContext.Any()
                ? "\nPrevious Response History:\n" + string.Join("\n\n", previousContext)
                : "";

            // Search in documents
            var filter = new MemoryFilter();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    filter.ByTag(memoryTag.Name, memoryTag.Value);
                }
            }

            var memoryAnswer = await _memory.SearchAsync(question, minRelevance: 0.4, index: index, filter: filter);
            var agentDefinitions = await _agentDefinitionRepository.GetByAgentName("AnswerAgent");

            if (agentDefinitions == null)
            {
                yield return new ResponseMessage
                {
                    IsError = true,
                    Message = "Answer agent configuration not found."
                };
                yield break;
            }

            var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
            {
                AgentName = agentDefinitions.AgentName,
                Instructions = agentDefinitions.Instructions,
                ModelName = agentDefinitions.ModelName
            });

            // Prepare the prompt based on available information
            var documentContext = memoryAnswer.NoResult ? "" :
                "Reference Documentation:\n" + string.Join("\n", memoryAnswer.Results.Select(r => r.Partitions[0].Text));

            var fullPrompt = $"""
                {(string.IsNullOrEmpty(documentContext) ? "" : $"{documentContext}\n\n")}
                {chatHistoryContext}

                Current Question: {question}

                Please provide a comprehensive answer based on {(memoryAnswer.NoResult ? "our previous conversation context" : "the reference documentation and previous conversation context")}.
                If you don't have enough information to provide a complete answer, please say so.
                """;

            ChatHistory chat = [];
            chat.AddUserMessage(fullPrompt);

            var answer = new StringBuilder();
            await foreach (var chunk in agent.Result.InvokeStreamingAsync(chat))
            {
                if (!string.IsNullOrEmpty(chunk.Content))
                {
                    answer.Append(chunk.Content);
                    yield return new ResponseMessage { Message = chunk.Content };
                }
            }

            // Save the chat history
            await _chatHistoryRepository.SaveChatHistory(new ChatHistoryModel
            {
                SessionId = sessionId,
                Question = question,
                Answer = answer.ToString(),
                Timestamp = DateTime.UtcNow
            });
        }




        //public async Task<SuggestWorkspaceResDto> GenerateWorkspace(string subject, string description, List<string> fileNames)
        //{
        //    var agentDefinitions = await _agentDefinitionRepository.GetByAgentName("WorkspaceAgent");
        //    if (agentDefinitions == null)
        //    {
        //        return new SuggestWorkspaceResDto
        //        {
        //            WorkspaceId = 0,
        //            DeveloperEmail = "unassigned",
        //            ProjectCategoryId = 0
        //        };
        //    }

        //    // Get memory results first
        //    var input = $"Project Title: {subject}\nProject Description: {description}\n";
        //    var memoryAnswer = await _memory.SearchAsync(input, minRelevance: 0.4, index: "workspaces");
        //    var documentIds = memoryAnswer.Results.Select(result => result.DocumentId).ToList();

        //    // Search in SQL with the extracted document IDs
        //    string sqlQuery = "SELECT Workspace, ProjectCategory FROM [dbo].[ProjectMemories] WHERE Id IN @Ids";
        //    var projectMemories = await _dbConnection.QueryAsync<ProjectMemory>(sqlQuery, new { Ids = documentIds });

        //    // Create distinct workspace list using GroupBy to prevent duplicates
        //    var workspaceGroup = projectMemories
        //        .GroupBy(m => m.Workspace)
        //        .ToDictionary(g => g.Key, g => g.First().ProjectCategory);

        //    // Build workspace list input for agent
        //    var workspaceList = string.Join("\n", workspaceGroup.Keys.Select(name => $"- {name}"));
        //    input += $"Available workspaces:\n{workspaceList}\n";

        //    // Get file names and descriptions
        //    var files = await _dbConnection.QueryAsync<FileDescription>(
        //        "SELECT FileName, Description FROM [dbo].[Files] WHERE FileName IN @FileNames",
        //        new { FileNames = fileNames }
        //    );

        //    // Add file names and descriptions to input
        //    foreach (var file in files)
        //    {
        //        input += $"File Name: {file.FileName}\nFile Description: {file.Description}\n";
        //    }

        //    // Create and invoke agent
        //    var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
        //    {
        //        AgentName = agentDefinitions.AgentName,
        //        Instructions = agentDefinitions.Instructions,
        //        ModelName = agentDefinitions.ModelName
        //    });

        //    ChatHistory chat = [];
        //    chat.AddUserMessage(input);

        //    var suggestedName = "";
        //    await foreach (var response in agent.InvokeAsync(chat))
        //    {
        //        suggestedName = response.Content?.Trim() ?? "";
        //    }

        //    // Handle valid suggestion
        //    if (workspaceGroup.ContainsKey(suggestedName))
        //    {
        //        var projectCategoryName = workspaceGroup[suggestedName] ?? "Uncategorized";
        //        var projectCategory = await _projectCategoryRepository.GetByNameAsync(projectCategoryName);

        //        var result = await FindBestUserForWorkspace(suggestedName, subject, description, fileNames);
        //        return new SuggestWorkspaceResDto
        //        {
        //            WorkspaceId = result.WorkspaceId,
        //            DeveloperEmail = result.DeveloperEmail,
        //            ProjectCategoryId = projectCategory?.Id ?? 0
        //        };
        //    }

        //    // Fallback to first available workspace
        //    var defaultEntry = workspaceGroup.FirstOrDefault();
        //    var defaultCategory = await _projectCategoryRepository.GetByNameAsync(defaultEntry.Value ?? "Uncategorized");

        //    if (defaultEntry.Key != null)
        //    {
        //        var result = await FindBestUserForWorkspace(defaultEntry.Key, subject, description, fileNames);
        //        return new SuggestWorkspaceResDto
        //        {
        //            WorkspaceId = result.WorkspaceId,
        //            DeveloperEmail = result.DeveloperEmail,
        //            ProjectCategoryId = defaultCategory?.Id ?? 0
        //        };
        //    }

        //    return new SuggestWorkspaceResDto
        //    {
        //        WorkspaceId = 0,
        //        DeveloperEmail = "unassigned",
        //        ProjectCategoryId = 0
        //    };
        //}



        //public async Task<SuggestWorkspaceResDto> FindBestUserForWorkspace(string workspaceName, string subject, string description, List<string> fileNames)
        //{
        //    var agentDefinitions = await _agentDefinitionRepository.GetByAgentName("SkillMatcherAgent");
        //    if (agentDefinitions == null)
        //    {
        //        return new SuggestWorkspaceResDto { WorkspaceId = 0, DeveloperEmail = "unassigned" };
        //    }

        //    var workspace = await _workspaceRepository.GetByTitle(workspaceName);
        //    if (workspace == null)
        //        return new SuggestWorkspaceResDto { WorkspaceId = 0, DeveloperEmail = "unassigned" };

        //    var workspaceUsers = await _assignWorkspaceRepository.GetUsersByWorkspaceId(workspace.Id);
        //    if (!workspaceUsers.Any())
        //        return new SuggestWorkspaceResDto { WorkspaceId = workspace.Id, DeveloperEmail = "unassigned" };

        //    var input = $"Project Title: {subject}\nProject Description: {description}\nAvailable users:\n";
        //    input += string.Join("\n", workspaceUsers.Select(u => $"- {u.Email}: {u.Skills}"));

        //    // Get file names and descriptions
        //    var files = await _dbConnection.QueryAsync<FileDescription>(
        //        "SELECT FileName, Description FROM [dbo].[Files] WHERE FileName IN @FileNames",
        //        new { FileNames = fileNames }
        //    );

        //    // Add file names and descriptions to input
        //    foreach (var file in files)
        //    {
        //        input += $"File Name: {file.FileName}\nFile Description: {file.Description}\n";
        //    }

        //    var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
        //    {
        //        AgentName = agentDefinitions.AgentName,
        //        Instructions = agentDefinitions.Instructions,
        //        ModelName = agentDefinitions.ModelName
        //    });

        //    ChatHistory chat = [];
        //    chat.AddUserMessage(input);

        //    var recommendedEmail = "";
        //    await foreach (var response in agent.InvokeAsync(chat))
        //    {
        //        recommendedEmail = response.Content?.Trim() ?? "";
        //    }

        //    if (workspaceUsers.Any(u => u.Email == recommendedEmail))
        //    {
        //        return new SuggestWorkspaceResDto { WorkspaceId = workspace.Id, DeveloperEmail = recommendedEmail };
        //    }

        //    return new SuggestWorkspaceResDto { WorkspaceId = workspace.Id, DeveloperEmail = "unassigned" };
        //}

        //public async Task<string> SummarizeProject(string subject, string description, List<string> fileNames)
        //{
        //    var agentDetinations = await _agentDefinitionRepository.GetByAgentName("Summarizer");
        //    var input = $"Project Title: {subject}\n Project Description: {description}\n";

        //    // Get file names and descriptions
        //    var files = await _dbConnection.QueryAsync<FileDescription>(
        //        "SELECT FileName, Description FROM [dbo].[Files] WHERE FileName IN @FileNames",
        //        new { FileNames = fileNames }
        //    );

        //    // Add file names and descriptions to input
        //    foreach (var file in files)
        //    {
        //        input += $"File Name: {file.FileName}\nFile Description: {file.Description}\n";
        //    }

        //    var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto { AgentName = agentDetinations.AgentName, Instructions = agentDetinations.Instructions, ModelName = agentDetinations.ModelName });

        //    ChatHistory chat = [];
        //    ChatMessageContent message = new(AuthorRole.User, input);
        //    chat.Add(message);

        //    var answer = "";

        //    await foreach (ChatMessageContent response in agent.InvokeAsync(chat))
        //    {
        //        answer = response.Content;
        //    }

        //    return answer;
        //}

        public async IAsyncEnumerable<ResponseMessage> CallAgent(string agentName, string question)
        {
            // Use the agentFactory to stream responses
            await foreach (var message in agentFactory.StreamAgentResponseAsync(agentName, question))
            {
                yield return message;
            }
        }

        public async Task<ResponseMessage> CallAgentManually(string agentName, string question)
        {
            try
            {
                // Get the agent definition from the repository using the agent name
                var agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);
                if (agentDefinition == null)
                {
                    return new ResponseMessage { IsError = true, Message = $"Agent with name '{agentName}' not found." };
                }

                // Convert AgentDefinition to AgentDefinitionDto
                var agentDefinitionDto = new AgentDefinitionDto
                {
                    AgentName = agentDefinition.AgentName,
                    Instructions = agentDefinition.Instructions,
                    UserInstructions = agentDefinition.UserInstructions,
                    ModelName = agentDefinition.ModelName,
                    Workspace = agentDefinition.Workspace,
                    Tools = agentDefinition.ToolsArray
                };

                var agent = _aiAgentFactory.CreateAIAgent(agentDefinitionDto);
                var chat = new ChatHistory();
                chat.AddUserMessage(question);

                var response = new StringBuilder();
                await foreach (var chunk in agent.Result.InvokeAsync(chat))
                {
                    if (!string.IsNullOrEmpty(chunk.Content))
                    {
                        response.Append(chunk.Content);
                    }
                }

                var responseText = response.ToString();

                if (string.IsNullOrEmpty(responseText))
                {
                    return new ResponseMessage { IsError = true, Message = "No response from agent." };
                }
                return new ResponseMessage { IsError = false, Message = responseText };
            }
            catch (Exception ex)
            {
                return new ResponseMessage { IsError = true, Message = ex.Message };
            }
        }

        public async Task<ResponseMessageList> GetPluginClassNames()
        {
            var answers = await agentFactory.GetPluginClassNames();
            return answers;
        }


        //public async Task<List<TaskDateDto>> GenerateProjectTasks(string subject, string description, int workspaceId, DateTime StartDate, DateTime completionDate, List<string> fileNames)
        //{
        //    var agentDefinitions = await _agentDefinitionRepository.GetByAgentName("TaskGeneratorAgent");
        //    if (agentDefinitions == null)
        //    {
        //        return new List<TaskDateDto> { new TaskDateDto {
        //            Task = "Task generator agent configuration not found",
        //            DueDate = StartDate,
        //            Complexity = "Low"
        //        }};
        //    }

        //    // Get relevant documentation from memory
        //    var message = $"Project Title: {subject}\nProject Description: {description}\n";
        //    var filter = new MemoryFilter().ByTag("WorkspaceId", workspaceId.ToString());
        //    var memoryAnswer = await _memory.SearchAsync(message, minRelevance: 0, index: "DocsData", filter: filter);

        //    // Build input with project details and documentation context
        //    var input = $"""
        //        Project Title: {subject}
        //        Project Description: {description}
        //        Timeline: {StartDate:MMM dd} to {completionDate:MMM dd}
        //        """;

        //    if (memoryAnswer.Results.Any())
        //    {
        //        input += "\nRelevant Documentation:\n";
        //        input += string.Join("\n", memoryAnswer.Results.Select(r => r.Partitions[0].Text));
        //    }

        //    // Get file names and descriptions
        //    var files = await _dbConnection.QueryAsync<FileDescription>(
        //        "SELECT FileName, Description FROM [dbo].[Files] WHERE FileName IN @FileNames",
        //        new { FileNames = fileNames }
        //    );

        //    // Add file names and descriptions to input
        //    foreach (var file in files)
        //    {
        //        input += $"File Name: {file.FileName}\nFile Description: {file.Description}\n";
        //    }

        //    var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
        //    {
        //        AgentName = agentDefinitions.AgentName,
        //        Instructions = agentDefinitions.Instructions,
        //        ModelName = agentDefinitions.ModelName
        //    });

        //    ChatHistory chat = [];
        //    chat.AddUserMessage(input);

        //    var responseContent = "";
        //    await foreach (var response in agent.InvokeAsync(chat))
        //    {
        //        responseContent += response.Content ?? "";
        //    }

        //    var taskDtos = ProcessAgentResponse(responseContent, StartDate, completionDate);

        //    return taskDtos.Any() ? taskDtos : GetFallbackTasks(StartDate);
        //}


        public async Task AddProjectMemory()
        {
            string sqlQuery = "SELECT Id, Workspace, Status, ProjectDescription, ProjectCategory FROM [dbo].[ProjectMemories] WHERE Status != @Status";
            var draftNotes = _dbConnection.Query<ProjectMemory>(sqlQuery, new { Status = "Published" });

            foreach (var note in draftNotes)
            {
                try
                {
                    if (note.Status == "Updated")
                    {
                        await DeleteSingleMemory(note.Id.ToString(), "workspaces");
                    }
                    else if (note.Status == "Deleted")
                    {
                        await DeleteSingleMemory(note.Id.ToString(), "workspaces");
                        string deleteQuery = "DELETE FROM [dbo].[ProjectMemories] WHERE Id = @Id";
                        await _dbConnection.ExecuteAsync(deleteQuery, new { Id = note.Id });
                        continue;
                    }

                    string result = await AddMemory($"Workspace: {note.Workspace} \n Description: {note.ProjectDescription} \n ProjectCategory: {note.ProjectCategory}", note.Id.ToString(), "workspaces");

                    string updateQuery = "UPDATE [dbo].[ProjectMemories] SET Status = @Status WHERE Id = @Id";
                    await _dbConnection.ExecuteAsync(updateQuery, new { Status = "Published", Id = note.Id });
                }
                catch (Exception ex)
                {
                    string updateQuery = "UPDATE [dbo].[ProjectMemories] SET Status = @Status WHERE Id = @Id";
                    await _dbConnection.ExecuteAsync(updateQuery, new { Status = "Error", Id = note.Id });
                }
            }
        }

        public async Task UpdateProjectMemory(ProjectMemory projectMemory)
        {
            var text = $"Workspace: {projectMemory.Workspace}\nProject Description: {projectMemory.ProjectDescription}\nCategory: {projectMemory.ProjectCategory}";
            if (projectMemory.Status == "Updated")
            {
                await DeleteSingleMemory(projectMemory.Id.ToString(), "projects");
                await AddMemory(text, projectMemory.Id.ToString(), "projects");
            }
            else if (projectMemory.Status == "Created")
            {
                await AddMemory(text, projectMemory.Id.ToString(), "projects");
            }
            else if (projectMemory.Status == "Deleted")
            {
                await DeleteSingleMemory(projectMemory.Id.ToString(), "projects");
            }
        }

        public async Task<string> AddMemory(string text, string id, string index, List<MemoryTag> tags = null)
        {
            var tag = new TagCollection();
            if (tags != null)
            {
                foreach (var memoryTag in tags)
                {
                    tag.Add(memoryTag.Name, memoryTag.Value);
                }
            }
            var res = await _memory.ImportTextAsync(text, id, tags: tag, index: index);

            return res;
        }

        public async Task DeleteSingleMemory(string id, string index)
        {
            await _memory.DeleteDocumentAsync(id, index);
        }

        public async Task DeleteIndexAsync(string index)
        {
            await _memory.DeleteIndexAsync(index);
        }
        
        /// <summary>
        /// Deletes multiple navigation entries in batch
        /// </summary>
        /// <param name="ids">List of document IDs to delete</param>
        /// <returns>Number of successfully deleted entries</returns>
        public async Task<int> DeleteNavigationEntries(List<string> ids)
        {
            if (ids == null || !ids.Any())
                return 0;
                
            int successCount = 0;
            foreach (var id in ids)
            {
                try
                {
                    await _memory.DeleteDocumentAsync(id, "Navigation");
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Failed to delete navigation entry {id}: {ex.Message}");
                    // Continue with other deletions even if one fails
                }
            }
            
            return successCount;
        }
        
        /// <summary>
        /// Checks if a navigation entry exists with the given ID
        /// </summary>
        /// <param name="id">Document ID to check</param>
        /// <returns>True if entry exists, false otherwise</returns>
        public async Task<bool> NavigationEntryExists(string id)
        {
            try
            {
                // We can't directly check if a document exists in Kernel Memory
                // So we'll try to search with a very specific query that should match only this document
                var filter = new MemoryFilter().ByDocument(id);
                var result = await _memory.SearchAsync("", filter: filter, limit: 1, index: "Navigation");
                return !result.NoResult && result.Results.Any(r => r.DocumentId == id);
            }
            catch
            {
                return false; // If any error occurs, assume it doesn't exist
            }
        }
        
        /// <summary>
        /// Updates a navigation entry by deleting and recreating it
        /// </summary>
        /// <param name="navigationEntry">The navigation entry to update</param>
        /// <returns>True if successful</returns>
        public async Task<bool> UpdateNavigationEntry(NavigationEntryDto navigationEntry)
        {
            if (navigationEntry == null || string.IsNullOrEmpty(navigationEntry.Id))
                return false;
                
            try
            {
                // Delete the existing entry first
                await DeleteSingleMemory(navigationEntry.Id, "Navigation");
                
                // Then add it as a new entry
                await AddNavigationEntry(navigationEntry);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<EmailTemplateResult> GenerateEmailTemplate(string purpose, Dictionary<string, string> parameters)
        {
            string predefinedTemplate = @"<!DOCTYPE html>
                <html>
                <head>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                        }
                        .workspace {
                            margin-bottom: 20px;
                        }
                        .workspace h3 {
                            color: #2E8B57;
                        }
                        .tasks, .projects {
                            margin-left: 20px;
                        }
                        table, th, td {
                            border: 1px solid #ddd;
                        }
                        th, td {
                            padding: 8px;
                            text-align: left;
                        }
                        @media only screen and (max-width: 600px) {
                            .content {
                                padding: 10px;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class=""content"">
                        <h2>Daily Project Updates</h2>
                        {{WorkspaceDetails}}
                    </div>
                </body>
                </html>";

            // Insert the pre-formatted HTML into the predefined template
            string htmlBody = predefinedTemplate
                .Replace("{{WorkspaceDetails}}", parameters["WorkspaceDetails"]);

            return new EmailTemplateResult
            {
                Subject = "Daily Project Updates",
                HtmlBody = htmlBody
            };
        }

        public class EmailTemplateResult
        {
            public string Subject { get; set; }
            public string HtmlBody { get; set; }
        }
        //public async Task<DateTime> GenerateCompletionDate(string subject, string description, string priority, DateTime startDate)
        //{
        //    // Prepare the chat history
        //    var chatHistory = new ChatHistory();
        //    chatHistory.AddSystemMessage(
        //        "You are a project completion date estimator. Based on the project title, description, and priority level, " +
        //        "estimate the number of days required to complete the project. Consider that:" +
        //        "\n- High priority projects should be completed more quickly" +
        //        "\n- Low priority projects can have longer timelines" +
        //        "\n- Medium priority projects should have balanced timelines" +
        //        "\nAnalyze the complexity and scope of the project, then return ONLY an integer representing the estimated days to completion."
        //    );

        //    // Add project details to chat history
        //    chatHistory.AddUserMessage(
        //        $"Project Title: {subject}\n" +
        //        $"Project Description: {description}\n" +
        //        $"Priority Level: {priority}\n" +
        //        "Based on these details and especially considering the priority level, how many days will this project take?"
        //    );

        //    // Get the chat completion result
        //    ChatMessageContent response = await _chatCompletionService.GetChatMessageContentAsync(
        //        chatHistory: chatHistory,
        //        kernel: _kernel
        //    );

        //    // Parse the response to get the estimated days
        //    if (int.TryParse(response.Content.Trim(), out int estimatedDays))
        //    {
        //        // Ensure we have at least 1 day
        //        estimatedDays = Math.Max(1, estimatedDays);
        //        return startDate.AddDays(estimatedDays);
        //    }

        //    // Default fallback if response parsing fails
        //    return startDate.AddDays(14); // Default to 2 weeks if AI response cannot be parsed
        //}

        public async Task<string> AnalyzeImageAndGenerateDescription(byte[] imageBytes, string fileType = "image/jpeg")
        {
            // Determine agent name based on file type
            string agentName = fileType == "application/pdf" ? "PDFAnalyzer" : "ImageAnalyzer";

            // Get the agent definition
            var agentDefinitions = await _agentDefinitionRepository.GetByAgentName(agentName);
            if (agentDefinitions == null)
            {
                return $"Agent {agentName} is not configured. Please add it to the database.";
            }

            Console.WriteLine($"Using agent: {agentDefinitions.AgentName} with model: {agentDefinitions.ModelName}");

            var agent = agentFactory.CreateAIAgent(new AgentDefinitionDto
            {
                AgentName = agentDefinitions.AgentName,
                Instructions = agentDefinitions.Instructions,
                ModelName = agentDefinitions.ModelName
            });

            ChatHistory chat = new();

            try
            {
                if (fileType == "application/pdf")
                {
                    Console.WriteLine($"Processing PDF file of size: {imageBytes.Length} bytes using PdfTextExtractor");

                    // Use PdfTextExtractor to extract text from the PDF
                    string extractedText = PdfTextExtractor.ExtractText(imageBytes);

                    if (string.IsNullOrWhiteSpace(extractedText) || extractedText.StartsWith("Error extracting text from PDF"))
                    {
                        Console.WriteLine("PDF text extraction failed, falling back to base64 encoding");

                        // Fallback to base64 encoding if text extraction fails
                        chat.AddUserMessage("Analyze this PDF document encoded in base64 format:");
                        string base64Content = Convert.ToBase64String(imageBytes);
                        chat.AddUserMessage(base64Content);
                    }
                    else
                    {
                        Console.WriteLine($"Successfully extracted {extractedText.Length} characters from PDF");
                        chat.AddUserMessage("Analyze this extracted text from a PDF document:");
                        chat.AddUserMessage(extractedText);
                    }
                }
                else
                {
                    // For image files
                    chat.AddUserMessage("Analyze this image and generate a detailed description:");
                    chat.AddUserMessage(Convert.ToBase64String(imageBytes));
                }

                Console.WriteLine("Starting AI processing of file...");
                var description = new StringBuilder();

                // Process the response from the AI
                await foreach (var response in agent.Result.InvokeStreamingAsync(chat))
                {
                    if (!string.IsNullOrWhiteSpace(response.Content))
                    {
                        description.Append(response.Content);
                    }
                }

                // Format the result
                string result = description.ToString().Trim();
                if (string.IsNullOrWhiteSpace(result))
                {
                    return fileType == "application/pdf"
                        ? "Could not extract text from this PDF. The file may be corrupted, password-protected, or contain only scanned images without OCR."
                        : "Could not generate description for this file";
                }

                // Clean up the formatting issues
                result = FormatAIResponse(result);

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error analyzing file: {ex.Message}");
                return $"Error processing file: {ex.Message}";
            }
        }


        /// <summary>
        /// Formats the AI response to fix common formatting issues
        /// </summary>
        private static string FormatAIResponse(string response)
        {
            if (string.IsNullOrWhiteSpace(response))
                return response;

            // Replace markdown headers with proper formatting
            response = System.Text.RegularExpressions.Regex.Replace(
                response,
                @"#{1,6}\s*([^#\n]+)",
                m => $"\n{new string(' ', m.Groups[0].Value.Count(c => c == '#'))}{m.Groups[1].Value.Trim()}\n"
            );

            // Fix missing spaces after punctuation
            response = System.Text.RegularExpressions.Regex.Replace(
                response,
                @"([.,:;!?])([A-Za-z0-9])",
                "$1 $2"
            );

            // Fix missing spaces between words (camelCase or PascalCase detection)
            response = System.Text.RegularExpressions.Regex.Replace(
                response,
                @"([a-z])([A-Z])",
                "$1 $2"
            );

            // Remove excessive asterisks used for emphasis
            response = response.Replace("**", "");

            // Fix missing spaces after hyphens in lists
            response = System.Text.RegularExpressions.Regex.Replace(
                response,
                @"(-|\*)([A-Za-z0-9])",
                "$1 $2"
            );

            // Add proper line breaks for readability
            response = response.Replace("####", "\n\n");
            response = response.Replace("###", "\n\n");
            response = response.Replace("##", "\n\n");
            response = response.Replace("#", "\n\n");

            // Fix table formatting
            response = System.Text.RegularExpressions.Regex.Replace(
                response,
                @"\|([^\|]*)\|",
                m => $"| {m.Groups[1].Value.Trim()} |"
            );

            // Remove backticks
            response = response.Replace("`", "");

            // Ensure proper spacing
            response = response.Replace("\n\n\n", "\n\n");

            return response;
        }

        public async Task<List<MatchedDocument>> GetMatchedDocumentTitlesAndIds(string subject, string message, int workspaceId)
        {
            var question = $"Project Title: {subject}\nProject Description: {message}";
            var index = "DocsData";
            var filter = new MemoryFilter().ByTag("WorkspaceId", workspaceId.ToString());

            var memoryAnswer = await _memory.SearchAsync(question, minRelevance: 0.4, index: index, filter: filter);

            if (memoryAnswer.NoResult)
                return [];

            var documentIds = memoryAnswer.Results.Select(result => result.DocumentId).ToList();
            const string sqlQuery = "SELECT Title, Id FROM [dbo].[DocsData] WHERE Id IN @Ids";
            return (await _dbConnection.QueryAsync<MatchedDocument>(sqlQuery, new { Ids = documentIds })).AsList();
        }        /// <summary>
        /// Adds a new navigation entry to Kernel Memory
        /// </summary>
        /// <param name="navigationEntry">Navigation entry details</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task AddNavigationEntry(NavigationEntryDto navigationEntry)
        {
            if (navigationEntry == null)
                throw new ArgumentNullException(nameof(navigationEntry), "Navigation entry cannot be null");
                
            if (string.IsNullOrEmpty(navigationEntry.Id))
                navigationEntry.Id = Guid.NewGuid().ToString();
                
            // Format the document content for optimal searchability
            var contentBuilder = new StringBuilder();
            contentBuilder.AppendLine($"Title: {navigationEntry.Title}");
            contentBuilder.AppendLine($"Description: {navigationEntry.Description}");
            contentBuilder.AppendLine($"Type: {navigationEntry.NavigationType}");
            contentBuilder.AppendLine($"Route: {navigationEntry.Route}");
            contentBuilder.AppendLine($"Icon: {navigationEntry.Icon}");

            try 
            {
                await _memory.ImportTextAsync(contentBuilder.ToString(), navigationEntry.Id, index: "Navigation");
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to add navigation entry '{navigationEntry.Title}': {ex.Message}", ex);
            }
        }        /// <summary>
        /// Searches the Navigation index in Kernel Memory for entries matching the query
        /// </summary>
        /// <param name="query">Search query text</param>
        /// <param name="minRelevance">Minimum relevance score (0.0 to 1.0)</param>
        /// <param name="filter">Optional filter for specific navigation types</param>
        /// <returns>List of matching navigation results, ordered by relevance</returns>        /// <summary>
        /// Searches the Navigation index in Kernel Memory for entries matching the query
        /// </summary>
        /// <param name="query">Search query text</param>
        /// <param name="minRelevance">Minimum relevance score (0.0 to 1.0)</param>
        /// <returns>List of matching navigation results</returns>
        public async Task<List<NavigationResult>> SearchNavigationMemory(string query, double minRelevance = 0.3)
        {
            try
            {
                // Execute search
                var memoryAnswer = await _memory.SearchAsync(
                    query, 
                    minRelevance: minRelevance, 
                    index: "Navigation");

                if (memoryAnswer.NoResult)
                    return new List<NavigationResult>();

                var results = new List<NavigationResult>();

                foreach (var result in memoryAnswer.Results)
                {
                    try
                    {
                        var lines = result.Partitions[0].Text.Split('\n');
                        
                        var navigationResult = new NavigationResult
                        {
                            Id = result.DocumentId,
                            NavigationType = "",
                            Title = "",
                            Description = "",
                            Route = "",
                            Icon = ""
                        };
                        
                        // Parse content lines
                        foreach (var line in lines)
                        {
                            var trimmedLine = line.Trim();
                            
                            // Skip empty lines and metadata sections
                            if (string.IsNullOrWhiteSpace(trimmedLine) || trimmedLine == "Metadata:")
                                continue;
                                
                            // Process main content lines
                            if (trimmedLine.StartsWith("Title: "))
                                navigationResult.Title = trimmedLine.Substring(7);
                            else if (trimmedLine.StartsWith("Description: "))
                                navigationResult.Description = trimmedLine.Substring(13);
                            else if (trimmedLine.StartsWith("Route: "))
                                navigationResult.Route = trimmedLine.Substring(7);
                            else if (trimmedLine.StartsWith("Type: "))
                                navigationResult.NavigationType = trimmedLine.Substring(6);
                            else if (trimmedLine.StartsWith("Icon: "))
                                navigationResult.Icon = trimmedLine.Substring(6);
                        }
                        
                        // Only add results with valid data
                        if (!string.IsNullOrEmpty(navigationResult.Title) && !string.IsNullOrEmpty(navigationResult.Route))
                        {
                            results.Add(navigationResult);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error parsing search result: {ex.Message}");
                        // Continue with other results
                    }
                }

                return results;
            }            catch (Exception ex)
            {
                Console.WriteLine($"Error searching navigation memory: {ex.Message}");
                return new List<NavigationResult>();
            }
        }

        // Method to expose the database connection for use in seeding operations
        public IDbConnection GetDbConnection()
        {
            return _dbConnection;
        }

        public async IAsyncEnumerable<ResponseMessage> GetResponse(string question, string agentName, string modelName, string workspace, List<ChatHistories> historyDto)
        {
            var agentDefinition = new AgentDefinition();
            agentDefinition = await _agentDefinitionRepository.GetByAgentName(agentName);

            //var agentTask = _aiAgentFactory.CreateAIAgent(new AgentDefinitionDto
            //{
            //    AgentName = agentDefinition.AgentName,
            //    Instructions = agentDefinition.Instructions,
            //    ModelName = agentDefinition.ModelName,
            //    Tools = agentDefinition.ToolsArray,
            //});

            List<MemoryTag> tags = null;
            string index = "DocsData";
            if (!string.IsNullOrEmpty(workspace))
            {
                tags = new List<MemoryTag> { new MemoryTag { Name = "WorkspaceName", Value = workspace } };
            }
            await foreach (var message in agentFactory.StreamAgentResponseAsync(agentName, question, modelName, historyDto, tags, index))
            {
                yield return message;
            }

            //var agent = await agentTask;

            //ChatHistory chat = new ChatHistory();
            //chat.AddUserMessage(question);

            //if (historyDto != null)
            //{
            //    var message = GetChatStringForAI(historyDto);
            //    chat.AddAssistantMessage(message);
            //}

            //var answer = new StringBuilder();
            //await foreach (var chunk in agent.InvokeStreamingAsync(chat))
            //{
            //    Console.WriteLine(chunk.Content);
            //    if (!string.IsNullOrEmpty(chunk.Content))
            //    {
            //        answer.Append(chunk.Content);
            //        yield return new ResponseMessage { IsError = false, Message = chunk.Content };
            //    }
            //}
        }

        public static string GetChatStringForAI(List<ChatHistories> chatHistories)
        {
            var messages = chatHistories.OrderBy(c => c.CreatedAt).ToList(); // Sort by time

            StringBuilder sb = new StringBuilder();

            foreach (var chat in messages)
            {
                if (chat.IsEdited)
                {
                    sb.AppendLine($"User (edited): {chat.Message} (Sent at: {chat.CreatedAt})");
                }
                else
                {
                    sb.AppendLine($"User: {chat.Message} (Sent at: {chat.CreatedAt})");
                }

                foreach (var response in chat.Responses.OrderBy(r => r.CreatedAt))
                {
                    sb.AppendLine($"AI: {response.Response} (Replied at: {response.CreatedAt})");
                }
            }

            return sb.ToString();
        }
    }
}
