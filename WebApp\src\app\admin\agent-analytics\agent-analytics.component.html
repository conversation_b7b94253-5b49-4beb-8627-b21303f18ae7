<!-- Agent Analytics Main Container -->
<div class="h-full flex flex-col" [ngClass]="isDarkMode ? 'bg-[var(--background-light-gray)]' : 'bg-gray-50'">

  <!-- Header Section -->
  <div class="flex-shrink-0 p-6 border-b" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)] bg-[var(--background-white)]' : 'border-gray-200 bg-white'">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3">
        <i class="ri-bar-chart-line text-2xl" [ngClass]="isDarkMode ? 'text-[var(--primary-purple)]' : 'text-[var(--primary-purple)]'"></i>
        <div>
          <h1 class="text-2xl font-semibold" [ngClass]="textClass">Agent Analytics</h1>
          <p class="mt-1" [ngClass]="secondaryTextClass">Monitor and analyze agent performance metrics</p>
        </div>
      </div>

      <div class="flex items-center gap-3">
        <button nz-button nzType="default" (click)="showFilters = !showFilters">
          <i class="ri-filter-line mr-2"></i>
          Filters
        </button>
        <button nz-button nzType="primary" (click)="exportData()">
          <i class="ri-download-line mr-2"></i>
          Export
        </button>
      </div>
    </div>

    <!-- Filter Panel -->
    <div *ngIf="showFilters" class="mt-4 p-4 rounded-lg border" [ngClass]="isDarkMode ? 'bg-[var(--hover-blue-gray)] border-[var(--hover-blue-gray)]' : 'bg-gray-50 border-gray-200'">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium mb-2" [ngClass]="textClass">Agent Type</label>
          <nz-select [(ngModel)]="currentFilter.agentTypes" nzMode="multiple" nzPlaceHolder="Select types" class="w-full">
            <nz-option nzValue="AI" nzLabel="AI Agent"></nz-option>
            <nz-option nzValue="Human" nzLabel="Human Agent"></nz-option>
          </nz-select>
        </div>

        <div>
          <label class="block text-sm font-medium mb-2" [ngClass]="textClass">Status</label>
          <nz-select [(ngModel)]="currentFilter.status" nzMode="multiple" nzPlaceHolder="Select status" class="w-full">
            <nz-option nzValue="active" nzLabel="Active"></nz-option>
            <nz-option nzValue="inactive" nzLabel="Inactive"></nz-option>
            <nz-option nzValue="maintenance" nzLabel="Maintenance"></nz-option>
          </nz-select>
        </div>

        <div>
          <label class="block text-sm font-medium mb-2" [ngClass]="textClass">Performance Threshold</label>
          <nz-select [(ngModel)]="currentFilter.performanceThreshold" nzPlaceHolder="Min success rate" class="w-full">
            <nz-option [nzValue]="0" nzLabel="All"></nz-option>
            <nz-option [nzValue]="70" nzLabel="70%+"></nz-option>
            <nz-option [nzValue]="80" nzLabel="80%+"></nz-option>
            <nz-option [nzValue]="90" nzLabel="90%+"></nz-option>
          </nz-select>
        </div>

        <div class="flex items-end gap-2">
          <button nz-button nzType="primary" (click)="applyFilters()">Apply</button>
          <button nz-button nzType="default" (click)="resetFilters()">Reset</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Tab Navigation -->
  <div class="flex-shrink-0 px-6 pt-4">
    <div class="flex space-x-1 border-b" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)]' : 'border-gray-200'">
      <button
        class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'overview',
          'hover:bg-gray-100': activeTab !== 'overview' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'overview' && isDarkMode,
          'text-gray-600': activeTab !== 'overview' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'overview' && isDarkMode
        }"
        (click)="onTabChange('overview')">
        Overview
      </button>
      <button
        class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'performance',
          'hover:bg-gray-100': activeTab !== 'performance' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'performance' && isDarkMode,
          'text-gray-600': activeTab !== 'performance' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'performance' && isDarkMode
        }"
        (click)="onTabChange('performance')">
        Performance
      </button>
      <button
        class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'comparison',
          'hover:bg-gray-100': activeTab !== 'comparison' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'comparison' && isDarkMode,
          'text-gray-600': activeTab !== 'comparison' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'comparison' && isDarkMode
        }"
        (click)="onTabChange('comparison')">
        Comparison
      </button>
      <button
        class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'task-comparison',
          'hover:bg-gray-100': activeTab !== 'task-comparison' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'task-comparison' && isDarkMode,
          'text-gray-600': activeTab !== 'task-comparison' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'task-comparison' && isDarkMode
        }"
        (click)="onTabChange('task-comparison')">
        Human vs AI
      </button>
      <button
        class="px-4 py-2 text-sm font-medium rounded-t-lg transition-colors"
        [ngClass]="{
          'bg-[var(--primary-purple)] text-white': activeTab === 'insights',
          'hover:bg-gray-100': activeTab !== 'insights' && !isDarkMode,
          'hover:bg-[var(--hover-blue-gray)]': activeTab !== 'insights' && isDarkMode,
          'text-gray-600': activeTab !== 'insights' && !isDarkMode,
          'text-[var(--text-medium-gray)]': activeTab !== 'insights' && isDarkMode
        }"
        (click)="onTabChange('insights')">
        Insights
      </button>
    </div>
  </div>

  <!-- Content Area -->
  <div class="flex-1 p-6 overflow-auto">
    <div *ngIf="loading" class="flex justify-center items-center h-64">
      <nz-spin nzSize="large"></nz-spin>
    </div>

    <!-- Overview Tab -->
    <div *ngIf="activeTab === 'overview' && !loading">
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic
            nzTitle="Total Agents"
            [nzValue]="summary?.totalAgents || 0"
            [nzValueStyle]="{'color': isDarkMode ? 'var(--text-dark)' : '#1f2937'}">
          </nz-statistic>
        </nz-card>

        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic
            nzTitle="Active Agents"
            [nzValue]="summary?.activeAgents || 0"
            [nzValueStyle]="{'color': '#10A37F'}">
          </nz-statistic>
        </nz-card>

        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <nz-statistic
            nzTitle="Average Performance"
            [nzValue]="summary?.averagePerformance || 0"
            nzSuffix="%"
            [nzValueStyle]="{'color': '#6B46C1'}">
          </nz-statistic>
        </nz-card>

        <nz-card [nzBodyStyle]="{'padding': '20px'}" [ngClass]="cardClass">
          <div class="text-center">
            <div class="text-sm font-medium mb-2" [ngClass]="secondaryTextClass">System Health</div>
            <div class="text-2xl font-bold text-green-500">Excellent</div>
          </div>
        </nz-card>
      </div>

      <!-- Top Performers -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <nz-card nzTitle="Top Performers" [ngClass]="cardClass">
          <div *ngFor="let agent of summary?.topPerformers" class="flex items-center justify-between py-3 border-b last:border-b-0" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)]' : 'border-gray-100'">
            <div class="flex items-center gap-3">
              <div class="w-8 h-8 rounded-full flex items-center justify-center" [ngClass]="agent.agentType === 'AI' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'">
                <i [class]="agent.agentType === 'AI' ? 'ri-robot-line' : 'ri-user-line'"></i>
              </div>
              <div>
                <div class="font-medium" [ngClass]="textClass">{{ agent.agentName }}</div>
                <div class="text-sm" [ngClass]="secondaryTextClass">{{ agent.workArea }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="font-semibold text-green-600">{{ agent.metrics.successRate }}%</div>
              <div class="text-sm" [ngClass]="secondaryTextClass">{{ formatDuration(agent.metrics.averageTime) }}</div>
            </div>
          </div>
        </nz-card>

        <nz-card nzTitle="Recent Activity" [ngClass]="cardClass">
          <div class="space-y-4">
            <div *ngFor="let agent of agents.slice(0, 5)" class="flex items-center gap-3">
              <nz-tag [nzColor]="getStatusColor(agent.status)">{{ agent.status | titlecase }}</nz-tag>
              <span class="flex-1" [ngClass]="textClass">{{ agent.agentName }}</span>
              <span class="text-sm" [ngClass]="secondaryTextClass">{{ agent.lastUpdated.toRelative() }}</span>
            </div>
          </div>
        </nz-card>
      </div>
    </div>

    <!-- Performance Tab -->
    <div *ngIf="activeTab === 'performance' && !loading">
      <nz-table #performanceTable [nzData]="agents" [nzPageSize]="10" [nzShowSizeChanger]="true">
        <thead>
          <tr>
            <th nzShowCheckbox [nzChecked]="selectedAgents.length === agents.length && agents.length > 0" (nzCheckedChange)="clearSelection()"></th>
            <th *ngFor="let col of agentColumns" [nzSortFn]="col.sortable ? true : null">{{ col.title }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let agent of performanceTable.data">
            <td nzShowCheckbox
                [nzChecked]="selectedAgents.includes(agent.agentId)"
                (nzCheckedChange)="onAgentSelect(agent.agentId)"
                [nzDisabled]="selectedAgents.length >= 2 && !selectedAgents.includes(agent.agentId)">
            </td>
            <td>
              <div class="flex items-center gap-2">
                <div class="w-6 h-6 rounded-full flex items-center justify-center text-xs"
                     [ngClass]="agent.agentType === 'AI' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'">
                  <i [class]="agent.agentType === 'AI' ? 'ri-robot-line' : 'ri-user-line'"></i>
                </div>
                <span class="font-medium">{{ agent.agentName }}</span>
              </div>
            </td>
            <td>
              <nz-tag [nzColor]="agent.agentType === 'AI' ? 'blue' : 'green'">{{ agent.agentType }}</nz-tag>
            </td>
            <td>
              <span class="text-sm">{{ agent.workArea }}</span>
            </td>
            <td>
              <div class="flex items-center gap-2">
                <nz-progress
                  [nzPercent]="agent.metrics.successRate"
                  [nzStrokeColor]="getPerformanceColor(agent.metrics.successRate)"
                  nzSize="small"
                  [nzShowInfo]="false"
                  class="flex-1">
                </nz-progress>
                <span class="text-sm font-medium">{{ agent.metrics.successRate }}%</span>
              </div>
            </td>
            <td>{{ formatDuration(agent.metrics.averageTime) }}</td>
            <td>
              <nz-tag [nzColor]="getStatusColor(agent.status)">{{ agent.status | titlecase }}</nz-tag>
            </td>
            <td>
              <button nz-button nzType="link" nzSize="small" (click)="viewAgentDetails(agent.agentId)">
                <i class="ri-eye-line"></i>
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
    </div>

    <!-- Comparison Tab -->
    <div *ngIf="activeTab === 'comparison' && !loading">
      <div *ngIf="!comparison" class="text-center py-12">
        <i class="ri-scales-line text-4xl mb-4" [ngClass]="secondaryTextClass"></i>
        <h3 class="text-lg font-medium mb-2" [ngClass]="textClass">Select Two Agents to Compare</h3>
        <p [ngClass]="secondaryTextClass">Choose agents from the Performance tab to see detailed comparison</p>
      </div>

      <div *ngIf="comparison" class="space-y-6">
        <!-- Comparison Header -->
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold" [ngClass]="textClass">Agent Comparison</h3>
          <button nz-button nzType="default" (click)="clearSelection()">
            <i class="ri-close-line mr-2"></i>
            Clear Selection
          </button>
        </div>

        <!-- Agent Comparison Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <nz-card [nzTitle]="comparison.agent1.agentName" [ngClass]="cardClass">
            <div class="space-y-4">
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Type:</span>
                <nz-tag [nzColor]="comparison.agent1.agentType === 'AI' ? 'blue' : 'green'">{{ comparison.agent1.agentType }}</nz-tag>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Success Rate:</span>
                <span class="font-semibold">{{ comparison.agent1.metrics.successRate }}%</span>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Average Time:</span>
                <span class="font-semibold">{{ formatDuration(comparison.agent1.metrics.averageTime) }}</span>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Total Tasks:</span>
                <span class="font-semibold">{{ comparison.agent1.metrics.totalTasks }}</span>
              </div>
            </div>
          </nz-card>

          <nz-card [nzTitle]="comparison.agent2.agentName" [ngClass]="cardClass">
            <div class="space-y-4">
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Type:</span>
                <nz-tag [nzColor]="comparison.agent2.agentType === 'AI' ? 'blue' : 'green'">{{ comparison.agent2.agentType }}</nz-tag>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Success Rate:</span>
                <span class="font-semibold">{{ comparison.agent2.metrics.successRate }}%</span>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Average Time:</span>
                <span class="font-semibold">{{ formatDuration(comparison.agent2.metrics.averageTime) }}</span>
              </div>
              <div class="flex justify-between">
                <span [ngClass]="secondaryTextClass">Total Tasks:</span>
                <span class="font-semibold">{{ comparison.agent2.metrics.totalTasks }}</span>
              </div>
            </div>
          </nz-card>
        </div>

        <!-- Recommendations -->
        <nz-card nzTitle="Recommendations" [ngClass]="cardClass" *ngIf="comparison.recommendations.length > 0">
          <nz-alert
            *ngFor="let recommendation of comparison.recommendations"
            [nzMessage]="recommendation"
            nzType="info"
            nzShowIcon
            class="mb-2 last:mb-0">
          </nz-alert>
        </nz-card>
      </div>
    </div>

    <!-- Task Comparison Tab (Human vs AI) -->
    <div *ngIf="activeTab === 'task-comparison' && !loading">
      <div class="mb-6">
        <h3 class="text-lg font-semibold mb-2" [ngClass]="textClass">Human vs AI Performance Comparison</h3>
        <p [ngClass]="secondaryTextClass">Compare performance between human and AI agents working on the same tasks</p>
      </div>

      <div *ngIf="taskComparisons.length === 0" class="text-center py-12">
        <i class="ri-scales-line text-4xl mb-4" [ngClass]="secondaryTextClass"></i>
        <h3 class="text-lg font-medium mb-2" [ngClass]="textClass">No Task Comparisons Available</h3>
        <p [ngClass]="secondaryTextClass">No matching human and AI agents found working on the same tasks</p>
      </div>

      <div *ngIf="taskComparisons.length > 0" class="space-y-6">
        <div *ngFor="let taskComparison of taskComparisons" class="border rounded-lg p-6" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)] bg-[var(--background-white)]' : 'border-gray-200 bg-white'">
          <!-- Task Header -->
          <div class="flex items-center justify-between mb-6">
            <div>
              <h4 class="text-xl font-semibold" [ngClass]="textClass">{{ taskComparison.taskName }}</h4>
              <p class="mt-1" [ngClass]="secondaryTextClass">{{ taskComparison.description }}</p>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium" [ngClass]="secondaryTextClass">Overall Winner</div>
              <div class="text-lg font-bold" [ngClass]="getWinnerDisplay(taskComparison.comparisonResult?.winner || 'tie').color">
                {{ getWinnerDisplay(taskComparison.comparisonResult?.winner || 'tie').text }}
              </div>
            </div>
          </div>

          <!-- Agent Comparison Cards -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <!-- Human Agent Card -->
            <div class="border rounded-lg p-4" [ngClass]="isDarkMode ? 'border-green-500/30 bg-green-500/5' : 'border-green-200 bg-green-50'">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 rounded-full bg-green-100 text-green-600 flex items-center justify-center">
                  <i class="ri-user-line text-lg"></i>
                </div>
                <div>
                  <h5 class="font-semibold" [ngClass]="textClass">{{ taskComparison.humanAgent?.agentName }}</h5>
                  <p class="text-sm" [ngClass]="secondaryTextClass">Human Agent</p>
                </div>
              </div>

              <div class="space-y-3">
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Success Rate:</span>
                  <span class="font-semibold">{{ taskComparison.humanAgent?.metrics?.successRate || 0 }}%</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Average Time:</span>
                  <span class="font-semibold">{{ formatDuration(taskComparison.humanAgent?.metrics?.averageTime || 0) }}</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Quality Score:</span>
                  <span class="font-semibold">{{ taskComparison.humanAgent?.quality?.overallQuality || 0 }}%</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Total Tasks:</span>
                  <span class="font-semibold">{{ taskComparison.humanAgent?.metrics?.totalTasks || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- AI Agent Card -->
            <div class="border rounded-lg p-4" [ngClass]="isDarkMode ? 'border-blue-500/30 bg-blue-500/5' : 'border-blue-200 bg-blue-50'">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
                  <i class="ri-robot-line text-lg"></i>
                </div>
                <div>
                  <h5 class="font-semibold" [ngClass]="textClass">{{ taskComparison.aiAgent?.agentName }}</h5>
                  <p class="text-sm" [ngClass]="secondaryTextClass">AI Agent</p>
                </div>
              </div>

              <div class="space-y-3">
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Success Rate:</span>
                  <span class="font-semibold">{{ taskComparison.aiAgent?.metrics?.successRate || 0 }}%</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Average Time:</span>
                  <span class="font-semibold">{{ formatDuration(taskComparison.aiAgent?.metrics?.averageTime || 0) }}</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Quality Score:</span>
                  <span class="font-semibold">{{ taskComparison.aiAgent?.quality?.overallQuality || 0 }}%</span>
                </div>
                <div class="flex justify-between">
                  <span [ngClass]="secondaryTextClass">Total Tasks:</span>
                  <span class="font-semibold">{{ taskComparison.aiAgent?.metrics?.totalTasks || 0 }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Comparison Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div class="text-center p-4 rounded-lg" [ngClass]="isDarkMode ? 'bg-[var(--hover-blue-gray)]' : 'bg-gray-50'">
              <div class="text-sm font-medium mb-1" [ngClass]="secondaryTextClass">Speed Winner</div>
              <div class="text-lg font-bold" [ngClass]="getWinnerDisplay(taskComparison.comparisonResult?.speedAdvantage?.winner || 'tie').color">
                {{ getAdvantageText(taskComparison.comparisonResult?.speedAdvantage || {winner: 'tie', difference: 0}) }}
              </div>
            </div>

            <div class="text-center p-4 rounded-lg" [ngClass]="isDarkMode ? 'bg-[var(--hover-blue-gray)]' : 'bg-gray-50'">
              <div class="text-sm font-medium mb-1" [ngClass]="secondaryTextClass">Quality Winner</div>
              <div class="text-lg font-bold" [ngClass]="getWinnerDisplay(taskComparison.comparisonResult?.qualityAdvantage?.winner || 'tie').color">
                {{ getAdvantageText(taskComparison.comparisonResult?.qualityAdvantage || {winner: 'tie', difference: 0}) }}
              </div>
            </div>

            <div class="text-center p-4 rounded-lg" [ngClass]="isDarkMode ? 'bg-[var(--hover-blue-gray)]' : 'bg-gray-50'">
              <div class="text-sm font-medium mb-1" [ngClass]="secondaryTextClass">Reliability Winner</div>
              <div class="text-lg font-bold" [ngClass]="getWinnerDisplay(taskComparison.comparisonResult?.reliabilityAdvantage?.winner || 'tie').color">
                {{ getAdvantageText(taskComparison.comparisonResult?.reliabilityAdvantage || {winner: 'tie', difference: 0}) }}
              </div>
            </div>
          </div>

          <!-- Recommendation -->
          <div class="border-t pt-4" [ngClass]="isDarkMode ? 'border-[var(--hover-blue-gray)]' : 'border-gray-200'">
            <h6 class="font-semibold mb-2" [ngClass]="textClass">Recommendation</h6>
            <nz-alert
              [nzMessage]="taskComparison.comparisonResult?.overallRecommendation || 'No recommendation available'"
              nzType="info"
              nzShowIcon>
            </nz-alert>
          </div>
        </div>
      </div>
    </div>

    <!-- Insights Tab -->
    <div *ngIf="activeTab === 'insights' && !loading">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <nz-card nzTitle="Performance Insights" [ngClass]="cardClass">
          <div *ngFor="let agent of agents.slice(0, 3)" class="mb-6 last:mb-0">
            <h4 class="font-medium mb-3" [ngClass]="textClass">{{ agent.agentName }}</h4>
            <div *ngFor="let insight of agent.insights" class="mb-2 last:mb-0">
              <nz-alert
                [nzMessage]="insight.title"
                [nzDescription]="insight.description + (insight.suggestion ? ' - ' + insight.suggestion : '')"
                [nzType]="insight.type === 'positive' ? 'success' : insight.type === 'negative' ? 'warning' : 'info'"
                nzShowIcon>
              </nz-alert>
            </div>
          </div>
        </nz-card>

        <nz-card nzTitle="System Recommendations" [ngClass]="cardClass">
          <div class="space-y-3">
            <nz-alert
              nzMessage="Optimize Processing Speed"
              nzDescription="Consider upgrading hardware for agents with average processing time > 20 seconds"
              nzType="info"
              nzShowIcon>
            </nz-alert>
            <nz-alert
              nzMessage="Monitor Quality Metrics"
              nzDescription="Set up automated alerts for agents with quality scores below 85%"
              nzType="warning"
              nzShowIcon>
            </nz-alert>
            <nz-alert
              nzMessage="Schedule Maintenance"
              nzDescription="Regular maintenance windows can improve overall system performance"
              nzType="success"
              nzShowIcon>
            </nz-alert>
          </div>
        </nz-card>
      </div>
    </div>
  </div>
</div>
