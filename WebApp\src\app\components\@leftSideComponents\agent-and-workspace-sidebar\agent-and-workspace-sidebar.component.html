<!-- Agent and Workspace Sidebar Container -->
<div class="flex-1 flex flex-col h-full border-r" [ngClass]="{
  ' border-[#3a3a45]': themeService.isDarkMode(),
  ' border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
}">
<!-- Accordion Content Section -->
<div class="flex-1 flex flex-col overflow-y-auto">

  <!-- AGENTS SECTION -->
  <div class="flex flex-col">
    <!-- Agents Accordion Header -->
    <div (click)="toggleAccordionSection('agents')"
      class="px-[var(--padding-small)] py-[var(--padding-small)] border-b flex justify-between items-center cursor-pointer transition-all duration-200"
      [ngClass]="{
        'border-[#3a3a45] hover:bg-[#3a3a45]': themeService.isDarkMode(),
        'border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
      }">
      <div class="flex items-center gap-2">
        <i class="ri-robot-line text-lg" [ngClass]="{
            'text-[#10A37F]': themeService.isDarkMode(),
            'text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-bold text-lg" [ngClass]="{
            'text-white': themeService.isDarkMode(),
            'text-[var(--text-dark)]': !themeService.isDarkMode()
          }">Agents</span>
      </div>
      <!-- Accordion Toggle Icon -->
      <i class="text-sm transition-transform duration-200" [ngClass]="{
           'ri-arrow-down-s-line': !isAccordionExpanded('agents'),
           'ri-arrow-up-s-line': isAccordionExpanded('agents'),
           'text-[#ACACBE]': themeService.isDarkMode(),
           'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
         }"></i>
    </div>

    <!-- Agents Collapsible Content -->
    <div *ngIf="isAccordionExpanded('agents')" class="transition-all duration-300 ease-in-out pt-0 px-4">
      <!-- Loading State for Agents -->
      <div *ngIf="isLoadingAgents" class="flex items-center justify-center py-4">
        <div class="flex items-center gap-2">
          <div
            class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
          </div>
          <span class="text-sm font-medium" [ngClass]="{
              'text-[#ACACBE]': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
            Loading agents...
          </span>
        </div>
      </div>

      <!-- Agents List -->
      <div *ngIf="!isLoadingAgents && !agentsError && agents.length > 0" class="space-y-1">
        @for (agentName of agents; track $index) {
        <div (click)="navigateToAgentChat(agentName)"
          class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
            'bg-[var(--secondary-purple)] hover:bg-[var(--primary-purple)]': activeAgent && ( router.url.includes(agentName)) && !themeService.isDarkMode(),
            'bg-[var(--active-tab-bg)]': activeAgent && ( router.url.includes(agentName)) && themeService.isDarkMode(),
            'hover:bg-[var(--primary-purple)]': true,
            'hover:text-white': true
          }">
          <!-- Active indicator bar -->
          <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
              'opacity-100': router.url.includes(agentName),
              'opacity-0': !router.url.includes(agentName),
              'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
              'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
            }"></div>

          <i class="ri-robot-line text-lg transition-colors duration-200" [ngClass]="{
              'text-black': router.url.includes(agentName) && !themeService.isDarkMode(),
              'text-white': router.url.includes(agentName) && themeService.isDarkMode(),
              'text-[#ACACBE]': !router.url.includes(agentName) && themeService.isDarkMode(),
              '': !router.url.includes(agentName) && !themeService.isDarkMode(),
              'group-hover:text-white': themeService.isDarkMode(),
              'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
            }"></i>
          <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
              'text-black': router.url.includes(agentName) && !themeService.isDarkMode(),
              'text-white': router.url.includes(agentName) && themeService.isDarkMode(),
              'text-[#ACACBE]': !router.url.includes(agentName) && themeService.isDarkMode(),
              'text-[var(--text-dark)]': !router.url.includes(agentName) && !themeService.isDarkMode(),
              'group-hover:text-white': themeService.isDarkMode(),
              'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
            }">
            {{ formatAgentName(agentName) }}
          </span>
        </div>
        }
      </div>

      <!-- Error State for Agents -->
      <div *ngIf="agentsError" class="px-3 py-2">
        <div class="text-red-500 text-sm font-medium">{{ agentsError }}</div>
      </div>

      <!-- Empty State for Agents -->
      <div *ngIf="!isLoadingAgents && !agentsError && agents.length === 0"
        class="flex flex-col items-center justify-center py-8 text-center">
        <span class="text-sm font-medium" [ngClass]="{
            'text-[#ACACBE]': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }">No agents available.</span>
        <i class="ri-robot-line text-2xl mt-2 opacity-50" [ngClass]="{
            'text-[#ACACBE]': themeService.isDarkMode(),
            'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
          }"></i>
      </div>
    </div>
  </div>

  <!-- WORKSPACES SECTION -->
  <div class="flex flex-col">
    <!-- Workspaces Accordion Header -->
    <div (click)="toggleAccordionSection('workspaces')"
      class="px-[var(--padding-small)] py-[var(--padding-small)] border-b flex justify-between items-center cursor-pointer transition-all duration-200"
      [ngClass]="{
        'border-[#3a3a45] hover:bg-[#3a3a45]': themeService.isDarkMode(),
        'border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
      }">
      <div class="flex items-center gap-2">
        <i class="ri-building-line text-lg" [ngClass]="{
            'text-[#10A37F]': themeService.isDarkMode(),
            'text-[var(--primary-purple)]': !themeService.isDarkMode()
          }"></i>
        <span class="font-bold text-lg" [ngClass]="{
            'text-white': themeService.isDarkMode(),
            'text-[var(--text-dark)]': !themeService.isDarkMode()
          }">Workspaces</span>
      </div>
      <!-- Accordion Toggle Icon -->
      <i class="text-sm transition-transform duration-200" [ngClass]="{
           'ri-arrow-down-s-line': !isAccordionExpanded('workspaces'),
           'ri-arrow-up-s-line': isAccordionExpanded('workspaces'),
           'text-[#ACACBE]': themeService.isDarkMode(),
           'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
         }"></i>
    </div>

    <!-- Workspaces Collapsible Content -->
    <div *ngIf="isAccordionExpanded('workspaces')" class="transition-all duration-300 ease-in-out pt-0  px-4">
      <!-- Scrollable content for workspaces -->
      <div class="overflow-y-auto" style="max-height: calc(100vh - 150px);">
        <!-- Loading State for Workspaces -->
        <div *ngIf="isLoadingWorkspaces" class="flex items-center justify-center py-4">
          <div class="flex items-center gap-2">
            <div
              class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
            </div>
            <span class="text-sm font-medium" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              Loading workspaces...
            </span>
          </div>
        </div>

        <!-- Workspaces List -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0" class="space-y-1">
          @for (workspace of workspaces; track $index) {
          <div (click)="navigateToWorkspace(workspace)"
            class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
              'bg-[var(--secondary-purple)]': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && !themeService.isDarkMode(),
              'bg-[var(--active-tab-bg)]': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && themeService.isDarkMode(),
              'hover:bg-[var(--primary-purple)]': true,
              'hover:text-white': true
            }">
            <!-- Active indicator bar -->
            <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all" [ngClass]="{
                'opacity-100': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace),
                'opacity-0': !(workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace)),
                'bg-[var(--primary-purple)]': !themeService.isDarkMode(),
                'bg-[var(--active-tab-bg)]': themeService.isDarkMode()
              }"></div>

            <i class="ri-building-line text-lg transition-colors duration-200" [ngClass]="{
                'text-black': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && !themeService.isDarkMode(),
                'text-white': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && themeService.isDarkMode(),
                'text-[#ACACBE]': !(workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace)) && themeService.isDarkMode(),
                '': !(workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace)) && !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
              }"></i>
            <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
                'text-black': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && !themeService.isDarkMode(),
                'text-white': workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace) && themeService.isDarkMode(),
                'text-[#ACACBE]': !(workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace)) && themeService.isDarkMode(),
                'text-[var(--text-dark)]': !(workspace && router.url === '/chat/workspace/' + encodeURIComponent(workspace)) && !themeService.isDarkMode(),
                'group-hover:text-white': themeService.isDarkMode(),
                'group-hover:text-[var(--primary-purple)]': !themeService.isDarkMode()
              }">
              {{ workspace ?? 'Untitled Workspace' }}
            </span>
          </div>
          }
        </div>

        <!-- Error State for Workspaces -->
        <div *ngIf="workspacesError" class="px-3 py-2">
          <div class="text-red-500 text-sm font-medium">{{ workspacesError }}</div>
        </div>

        <!-- Empty State for Workspaces -->
        <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length === 0"
          class="flex flex-col items-center justify-center py-8 text-center">
          <span class="text-sm font-medium" [ngClass]="{
              'text-[#ACACBE]': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">No workspaces available.</span>
          <i class="ri-building-line text-2xl mt-2 opacity-50" [ngClass]="{
              'text-[#ACACBE]': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }"></i>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
