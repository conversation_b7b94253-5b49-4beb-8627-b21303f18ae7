using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class AgentEvaluationController : ControllerBase
    {
        private readonly AgentEvaluationService _agentEvaluationService;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;

        public AgentEvaluationController(
            AgentEvaluationService agentEvaluationService,
            IAgentDefinitionRepository agentDefinitionRepository)
        {
            _agentEvaluationService = agentEvaluationService;
            _agentDefinitionRepository = agentDefinitionRepository;
        }

        [HttpGet("GetAll")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<AgentEvaluationDto>>> GetAll()
        {
            var evaluations = await _agentEvaluationService.GetAllEvaluationsAsync();
            return Ok(evaluations);
        }

        [HttpGet("GetByAgentName/{agentName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<AgentEvaluationDto>>> GetByAgentName(string agentName)
        {
            var evaluations = await _agentEvaluationService.GetEvaluationsByAgentNameAsync(agentName);
            return Ok(evaluations);
        }

        [HttpPost("RunEvaluation")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AgentEvaluationDto>> RunEvaluation(CreateAgentEvaluationDto evaluationDto)
        {
            try
            {
                // Verify agent exists
                var agent = await _agentDefinitionRepository.GetByAgentName(evaluationDto.AgentName);
                if (agent == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent with name {evaluationDto.AgentName} not found." });
                }

                var result = await _agentEvaluationService.RunEvaluationAsync(evaluationDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error running evaluation: {ex.Message}" });
            }
        }

        [HttpPost("ExecuteByAgentName/{agentName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<AgentEvaluationDto>>> ExecuteByAgentName(string agentName)
        {
            try
            {
                // Verify agent exists
                var agent = await _agentDefinitionRepository.GetByAgentName(agentName);
                if (agent == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent with name {agentName} not found." });
                }

                var results = await _agentEvaluationService.ExecuteAllEvaluationsForAgentAsync(agentName);
                return Ok(results);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error executing evaluations: {ex.Message}" });
            }
        }
        
        [HttpPost("ReEvaluateByAgentName/{agentName}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<EvaluationResultDto>> ReEvaluateByAgentName(string agentName)
        {
            try
            {
                // Verify agent exists
                var agent = await _agentDefinitionRepository.GetByAgentName(agentName);
                if (agent == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent with name {agentName} not found." });
                }

                // Get all evaluations for this agent
                var evaluations = await _agentEvaluationService.GetEvaluationsByAgentNameAsync(agentName);
                var evaluationIds = new List<Guid>();
                
                foreach (var evaluation in evaluations)
                {
                    if (!string.IsNullOrEmpty(evaluation.Output))
                    {
                        evaluationIds.Add(evaluation.Id);
                    }
                }
                
                if (evaluationIds.Count == 0)
                {
                    return BadRequest(new ResponseMessage { IsError = true, Message = "No executed evaluations found for this agent." });
                }

                var result = await _agentEvaluationService.ReEvaluateAgentAsync(agentName, evaluationIds);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error re-evaluating agent: {ex.Message}" });
            }
        }

        [HttpPut("Update/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<AgentEvaluationDto>> UpdateEvaluation(Guid id, UpdateAgentEvaluationDto updateDto)
        {
            try
            {
                var result = await _agentEvaluationService.UpdateEvaluationAsync(id, updateDto);
                if (result == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Evaluation with ID {id} not found." });
                }
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error updating evaluation: {ex.Message}" });
            }
        }

        [HttpDelete("Delete/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<ResponseMessage>> DeleteEvaluation(Guid id)
        {
            try
            {
                var success = await _agentEvaluationService.DeleteEvaluationAsync(id);
                if (!success)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Evaluation with ID {id} not found." });
                }
                return Ok(new ResponseMessage { IsError = false, Message = "Evaluation deleted successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error deleting evaluation: {ex.Message}" });
            }
        }

        [HttpPost("SubmitFeedback")]
        [Authorize]
        public async Task<ActionResult<AgentEvaluationDto>> SubmitFeedback(AgentFeedbackDto feedbackDto)
        {
            try
            {
                // Verify agent exists
                var agent = await _agentDefinitionRepository.GetByAgentName(feedbackDto.AgentName);
                if (agent == null)
                {
                    return NotFound(new ResponseMessage { IsError = true, Message = $"Agent with name {feedbackDto.AgentName} not found." });
                }

                var result = await _agentEvaluationService.SubmitFeedbackAsync(feedbackDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ResponseMessage { IsError = true, Message = $"Error submitting feedback: {ex.Message}" });
            }
        }
    }
}
