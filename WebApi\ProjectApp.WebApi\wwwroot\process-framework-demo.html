<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Semantic Kernel Process Framework Demo</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #fafafa;
        }

        .section h2 {
            color: #34495e;
            margin-top: 0;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }

        input,
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        textarea {
            height: 100px;
            resize: vertical;
        }

        button {
            background-color: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }

        button:hover {
            background-color: #2980b9;
        }

        button:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
        }

        .test-button {
            background-color: #27ae60;
        }

        .test-button:hover {
            background-color: #229954;
        }

        .info-button {
            background-color: #f39c12;
        }

        .info-button:hover {
            background-color: #e67e22;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }

        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .loading {
            text-align: center;
            color: #666;
        }

        .step-list {
            list-style-type: none;
            padding: 0;
        }

        .step-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .step-list li:last-child {
            border-bottom: none;
        }

        .feature-list {
            columns: 2;
            column-gap: 30px;
        }

        .feature-list li {
            margin-bottom: 8px;
            break-inside: avoid;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🚀 Semantic Kernel Process Framework Demo</h1>

        <div class="section">
            <h2>📋 Project Registration Workflow</h2>
            <p>This demo showcases the Semantic Kernel Process Framework with a complete project registration workflow
                that includes:</p>
            <ul class="feature-list">
                <li>✅ Input validation and sanitization</li>
                <li>🤖 AI-powered project analysis</li>
                <li>🎯 Intelligent workspace assignment</li>
                <li>💾 Automated project creation</li>
                <li>📧 Multi-party notifications</li>
                <li>🔄 Event-driven step execution</li>
                <li>⚡ Real-time progress tracking</li>
                <li>🛡️ Built-in error handling</li>
            </ul>
        </div>

        <div class="section">
            <h2>🧪 Quick Test</h2>
            <p>Try the workflow with pre-filled test data:</p>
            <button class="test-button" onclick="runTestWorkflow()">Run Test Workflow</button>
            <button class="info-button" onclick="getProcessInfo()">Get Process Info</button>
        </div>

        <div class="section">
            <h2>📝 Custom Project Registration</h2>
            <form id="projectForm">
                <div class="form-group">
                    <label for="subject">Project Subject *</label>
                    <input type="text" id="subject" name="subject" required
                        placeholder="Enter project title (e.g., 'E-commerce Website Development')">
                </div>

                <div class="form-group">
                    <label for="message">Project Description *</label>
                    <textarea id="message" name="message" required
                        placeholder="Describe your project requirements, goals, and any specific details..."></textarea>
                </div>

                <div class="form-group">
                    <label for="userEmail">Your Email *</label>
                    <input type="email" id="userEmail" name="userEmail" required placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="assignedEmail">Preferred Developer (Optional)</label>
                    <input type="email" id="assignedEmail" name="assignedEmail" placeholder="<EMAIL>">
                </div>

                <div class="form-group">
                    <label for="priority">Priority</label>
                    <select id="priority" name="priority">
                        <option value="Low">Low</option>
                        <option value="Medium" selected>Medium</option>
                        <option value="High">High</option>
                        <option value="Critical">Critical</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="fileNames">File Names (Optional)</label>
                    <input type="text" id="fileNames" name="fileNames"
                        placeholder="requirements.pdf, mockups.png, specs.docx (comma-separated)">
                </div>

                <button type="submit">Execute Workflow</button>
            </form>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        const baseUrl = window.location.origin.replace(/:\d+/, ':7257'); // Adjust port as needed

        async function runTestWorkflow() {
            showLoading('Running test workflow...');

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/test-workflow`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    }
                });

                const result = await response.json();
                displayResult(result, response.ok);
            } catch (error) {
                displayError(`Error: ${error.message}`);
            }
        }

        async function getProcessInfo() {
            showLoading('Getting process information...');

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/process-info`, {
                    headers: {
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    }
                });

                const result = await response.json();
                displayResult(result, response.ok);
            } catch (error) {
                displayError(`Error: ${error.message}`);
            }
        }

        document.getElementById('projectForm').addEventListener('submit', async function (e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const fileNames = formData.get('fileNames');

            const request = {
                subject: formData.get('subject'),
                message: formData.get('message'),
                userEmail: formData.get('userEmail'),
                assignedEmail: formData.get('assignedEmail') || null,
                priority: formData.get('priority'),
                fileNames: fileNames ? fileNames.split(',').map(f => f.trim()).filter(f => f) : [],
                preferredWorkspaceId: null,
                preferredCategoryId: null
            };

            showLoading('Executing project registration workflow...');

            try {
                const response = await fetch(`${baseUrl}/api/ProcessWorkflow/execute-project-registration`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Add authorization header if needed
                        // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
                    },
                    body: JSON.stringify(request)
                });

                const result = await response.json();
                displayResult(result, response.ok);
            } catch (error) {
                displayError(`Error: ${error.message}`);
            }
        });

        function showLoading(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result loading';
            resultDiv.style.display = 'block';
            resultDiv.textContent = message;
        }

        function displayResult(result, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.style.display = 'block';
            resultDiv.textContent = JSON.stringify(result, null, 2);
        }

        function displayError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result error';
            resultDiv.style.display = 'block';
            resultDiv.textContent = message;
        }
    </script>
</body>

</html>