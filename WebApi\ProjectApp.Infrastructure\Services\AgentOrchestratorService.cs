using Microsoft.Extensions.Logging;
using Microsoft.KernelMemory;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Agents;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.AIAgents;
using System.Text;
using System.Text.Json;
using System.Threading.Channels;

namespace ProjectApp.Infrastructure.Services
{
    /// <summary>
    /// Service for orchestrating communication and coordination between multiple AI agents
    /// </summary>
    public class AgentOrchestratorService
    {
        private readonly AIAgentFactory _aiAgentFactory;
        private readonly IAgentDefinitionRepository _agentDefinitionRepository;
        private readonly ILogger<AgentOrchestratorService> _logger;
        private readonly IChatSourceService _chatSourceService;
        private readonly IAgentChatHistoryRepository _agentChatHistoryRepository;
        private readonly IExtractEmailFromAccessor _emailExtractor;

        public AgentOrchestratorService(
            AIAgentFactory aiAgentFactory,
            IAgentDefinitionRepository agentDefinitionRepository,
            IAgentChatHistoryRepository agentChatHistoryRepository,
            IExtractEmailFromAccessor emailExtractor,
            IChatSourceService chatSourceService,
            ILogger<AgentOrchestratorService> logger)
        {
            _aiAgentFactory = aiAgentFactory;
            _agentDefinitionRepository = agentDefinitionRepository;
            _chatSourceService = chatSourceService;
            _agentChatHistoryRepository = agentChatHistoryRepository;
            _emailExtractor = emailExtractor;
            _logger = logger;
        }
        
        public async IAsyncEnumerable<ResponseMessage> OrchestrateMultiAgentWorkflow(
            string workspaceName, 
            string userQuestion, 
            List<string> agentNames = null,
            bool saveHistory = true,
            List<ProjectApp.Core.Models.ConversationItem> previousConversation = null)
        {
            // Track agent interactions for the workflow
            var agentInteractions = new List<AgentInteraction>();
            var userEmail = _emailExtractor.GetEmail();

            // Step 1: If no specific agents provided, get all agents from the workspace
            if (agentNames == null || agentNames.Count == 0)
            {
                var workspaceAgents = await _agentDefinitionRepository.GetAllByWorkspace(workspaceName);
                agentNames = workspaceAgents.Select(a => a.AgentName).ToList();
                
                if (agentNames.Count == 0)
                {
                    yield return new ResponseMessage
                    {
                        IsError = true,
                        Message = $"No agents found in workspace '{workspaceName}'."
                    };
                    yield break;
                }
            }

            _logger.LogInformation("Starting multi-agent workflow for {AgentCount} agents in workspace {Workspace}", 
                agentNames.Count, workspaceName);

            // Step 2: Initialize the planner agent to decompose the task
            yield return new ResponseMessage
            {
                IsError = false,
                Message = "🔄 Starting agent orchestration...\n\n",
                ResponseType = "agentOrchestration"
            };            // Format previous conversation context if available
            string conversationContext = "";
            if (previousConversation != null && previousConversation.Count > 0)
            {
                _logger.LogInformation("Including {Count} previous conversation items for context", previousConversation.Count);
                conversationContext = "\nPrevious conversation:\n" + 
                    string.Join("\n\n", previousConversation.Select(c => 
                        $"User: {c.UserMessage}\nAssistant: {c.AgentResponse}"));
                        
                _logger.LogInformation("Conversation context prepared: {Context}", 
                    conversationContext.Length > 200 ? conversationContext.Substring(0, 200) + "..." : conversationContext);
            }
            else
            {
                _logger.LogInformation("No previous conversation context available - previousConversation is {Status}", 
                    previousConversation == null ? "null" : "empty");
            }

            // Get the planner agent (or use first agent as planner if no dedicated planner)
            var plannerAgentName = agentNames.Contains("PlannerAgent") ? "PlannerAgent" : agentNames.First();
            var plannerAgentDefinition = await _agentDefinitionRepository.GetByAgentName(plannerAgentName);
            
            if (plannerAgentDefinition == null)
            {
                yield return new ResponseMessage
                {
                    IsError = true,
                    Message = $"Planner agent '{plannerAgentName}' not found."
                };
                yield break;
            }            // Step 3: Generate a plan with the planner agent, including conversation context
            // Use the agent's own instructions rather than hardcoded prompt
            var planPrompt = $@"
                Available agents in workspace '{workspaceName}':
                {string.Join("\n", agentNames.Select(name => $"- {name}"))}

                Current user question: {userQuestion}
                {conversationContext}
                ";  

            // Execute the planner using its own instructions
            var plannerMessage = new StringBuilder();
            await foreach (var message in ExecuteAgentStreamSafely(plannerAgentName, planPrompt))
            {
                if (!message.IsError && !string.IsNullOrEmpty(message.Message))
                {
                    plannerMessage.Append(message.Message);
                    
                    // Forward planner messages to client
                    yield return new ResponseMessage
                    {
                        IsError = false,
                        Message = message.Message,
                        ResponseType = "agentPlanning"
                    };
                }
                else if (message.IsError)
                {
                    yield return message;
                    yield break;
                }
            }

            var plan = ExtractJsonFromText(plannerMessage.ToString());
            if (string.IsNullOrEmpty(plan))
            {
                yield return new ResponseMessage
                {
                    IsError = true,
                    Message = "Failed to generate a valid execution plan."
                };
                yield break;
            }            
            // Step 4: Parse the plan and execute subtasks
            var planParseResult = TryParsePlan(plan);
            if (!planParseResult.IsSuccess)
            {
                yield return new ResponseMessage
                {
                    IsError = true,
                    Message = "Failed to parse the execution plan JSON."
                };
                yield break;
            }            
            
            var planObj = planParseResult.Plan;

            // Validate the plan object
            if (planObj == null || planObj.Subtasks == null || planObj.Subtasks.Count == 0)
            {
                yield return new ResponseMessage
                {
                    IsError = true,
                    Message = "The generated plan is invalid or contains no subtasks."
                };
                yield break;
            }

            yield return new ResponseMessage
            {
                IsError = false,
                Message = $"📋 **Plan Analysis**: {planObj.Analysis ?? "No analysis provided"}\n\n**Planned Tasks**:\n",
                ResponseType = "agentOrchestration"
            };

            foreach (var subtask in planObj.Subtasks)
            {
                yield return new ResponseMessage
                {
                    IsError = false,
                    Message = $"- Task {subtask.Id}: {subtask.Description} (Assigned to: {subtask.Agent})\n",
                    ResponseType = "agentOrchestration"
                };
            }

            yield return new ResponseMessage
            {
                IsError = false,
                Message = "\n🚀 **Executing plan**...\n\n",
                ResponseType = "agentOrchestration"
            };

            // Step 5: Execute each subtask in order, respecting dependencies
            var subtaskResults = new Dictionary<int, string>();
            foreach (var subtask in planObj.Subtasks)
            {
                // Check if dependencies are fulfilled
                if (subtask.Requires != null && subtask.Requires.Length > 0)
                {
                    foreach (var dependencyId in subtask.Requires)
                    {
                        if (!subtaskResults.ContainsKey(dependencyId))
                        {
                            yield return new ResponseMessage
                            {
                                IsError = true,
                                Message = $"Task {subtask.Id} depends on Task {dependencyId} which has not been completed."
                            };
                            yield break;
                        }
                    }
                }                // Build context from dependencies and conversation history
                var contextBuilder = new StringBuilder();
                
                // Add conversation history context
                if (!string.IsNullOrEmpty(conversationContext))
                {
                    contextBuilder.AppendLine("Conversation History:");
                    contextBuilder.AppendLine(conversationContext);
                    contextBuilder.AppendLine();
                }
                
                // Add dependency results
                if (subtask.Requires != null && subtask.Requires.Length > 0)
                {
                    contextBuilder.AppendLine("Results from previous tasks:");
                    foreach (var dependencyId in subtask.Requires)
                    {
                        contextBuilder.AppendLine($"Task {dependencyId} result:");
                        contextBuilder.AppendLine(subtaskResults[dependencyId]);
                        contextBuilder.AppendLine();
                    }
                }

                // Execute the subtask with the assigned agent, including full context
                var subtaskPrompt = $@"
                    You are helping with a multi-agent workflow to answer this question:
                    ""{userQuestion}""

                    Your specific task is:
                    {subtask.Description}

                    {contextBuilder}

                    Respond with a clear, detailed answer that accomplishes this specific task while considering the conversation history and any previous task results.
                    ";
                
                yield return new ResponseMessage
                {
                    IsError = false,
                    Message = $"🤖 **Agent {subtask.Agent}** working on Task {subtask.Id}: {subtask.Description}\n\n",
                    ResponseType = "agentOrchestration"
                };

                var subtaskResult = new StringBuilder();
                await foreach (var message in ExecuteAgentStreamSafely(subtask.Agent, subtaskPrompt))
                {
                    if (!message.IsError && !string.IsNullOrEmpty(message.Message))
                    {
                        subtaskResult.Append(message.Message);
                        yield return new ResponseMessage
                        {
                            IsError = false,
                            Message = message.Message,
                            ResponseType = "agentOrchestration"
                        };
                    }
                    else if (message.IsError)
                    {
                        yield return message;
                        yield break;
                    }
                }

                // Store the result for potential use by other subtasks
                subtaskResults[subtask.Id] = subtaskResult.ToString();
                
                // Track the interaction
                agentInteractions.Add(new AgentInteraction
                {
                    AgentName = subtask.Agent,
                    Task = subtask.Description,
                    Response = subtaskResult.ToString()
                });

                yield return new ResponseMessage
                {
                    IsError = false,
                    Message = "\n\n",
                    ResponseType = "agentOrchestration"
                };
            }

            // Step 6: Synthesize results with a final agent
            var synthesisAgentName = agentNames.Contains("SynthesisAgent") ? "SynthesisAgent" : agentNames.Last();
            
            yield return new ResponseMessage
            {
                IsError = false,
                Message = $"🔄 **Agent {synthesisAgentName}** synthesizing final response\n\n",
                ResponseType = "agentOrchestration"
            };            var synthesisPrompt = $@"
                You are a synthesis agent responsible for creating a final comprehensive response based on the work of multiple agents.

                Original user question:
                ""{userQuestion}""

                {(!string.IsNullOrEmpty(conversationContext) ? $"Conversation History:\n{conversationContext}\n\n" : "")}

                Results from each agent:
                {string.Join("\n\n", agentInteractions.Select(interaction => 
                    $"Agent {interaction.AgentName} (Task: {interaction.Task}):\n{interaction.Response}"))}

                Please synthesize a clear, cohesive final answer that addresses the user's original question,
                incorporating the insights from all agents. Consider the conversation history to ensure continuity 
                and avoid repeating information already established. Be comprehensive but avoid unnecessary repetition.
                ";

            var finalResponse = new StringBuilder();
            await foreach (var message in ExecuteAgentStreamSafely(synthesisAgentName, synthesisPrompt))
            {
                if (!message.IsError && !string.IsNullOrEmpty(message.Message))
                {
                    finalResponse.Append(message.Message);
                    yield return new ResponseMessage
                    {
                        IsError = false,
                        Message = message.Message,
                        ResponseType = "agentOrchestration"
                    };
                }
                else if (message.IsError)
                {
                    yield return message;
                    yield break;
                }
            }            // Save the entire orchestrated conversation if requested
            if (saveHistory)
            {
                await SaveOrchestrationHistory(userQuestion, workspaceName, planObj.Analysis, agentInteractions, finalResponse.ToString(), conversationContext);
            }
        }        
        
        
        private async IAsyncEnumerable<ResponseMessage> ExecuteAgentStreamSafely(string agentName, string prompt)
        {
            var messagesQueue = new List<ResponseMessage>();
            
            try
            {
                await foreach (var message in _aiAgentFactory.StreamAgentResponseAsync(agentName, prompt))
                {
                    messagesQueue.Add(message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing agent {Agent}", agentName);
                messagesQueue.Add(new ResponseMessage
                {
                    IsError = true,
                    Message = $"Error executing agent {agentName}: {ex.Message}"
                });
            }
            
            foreach (var message in messagesQueue)
            {
                yield return message;
            }
        }        
        
        private async Task SaveOrchestrationHistory(string userQuestion, string workspaceName, string analysis, List<AgentInteraction> interactions, string finalResponse, string conversationContext = "")
        {
            try
            {
                var chatSources = _chatSourceService.GenerateChatSources();
                
                var combinedResponse = 
                    $"Multi-Agent Orchestration in workspace '{workspaceName}'\n" +
                    $"Plan: {analysis}\n\n" +
                    (!string.IsNullOrEmpty(conversationContext) ? $"Conversation Context:\n{conversationContext}\n\n" : "") +
                    $"Agent Execution Results:\n{string.Join("\n\n", interactions.Select(i => $"Agent {i.AgentName}: {i.Task}\n{i.Response}"))}\n\n" +
                    $"Final Synthesis:\n{finalResponse}";
                    
                await _agentChatHistoryRepository.SaveAgentChatMessage(
                    userQuestion,
                    "AgentOrchestrator", 
                    combinedResponse,
                    chatSources);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving orchestration history");
            }
        }


        private string ExtractJsonFromText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                _logger.LogWarning("Input text for JSON extraction is null or empty");
                return string.Empty;
            }

            // Look for JSON content within the text using common markers
            int startIndex = text.IndexOf('{');
            int endIndex = text.LastIndexOf('}');
            
            if (startIndex >= 0 && endIndex > startIndex)
            {
                var extractedJson = text.Substring(startIndex, endIndex - startIndex + 1);
                _logger.LogDebug("Extracted JSON: {Json}", extractedJson);
                return extractedJson;
            }
            
            _logger.LogWarning("No valid JSON structure found in text: {Text}", text.Length > 500 ? text.Substring(0, 500) + "..." : text);
            return string.Empty;
        }
        private PlanParseResult TryParsePlan(string planJson)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(planJson))
                {
                    _logger.LogWarning("Plan JSON is null or empty");
                    return new PlanParseResult { IsSuccess = false, Plan = null };
                }

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    AllowTrailingCommas = true
                };

                var plan = JsonSerializer.Deserialize<AgentPlan>(planJson, options);
                
                // Validate the parsed plan
                if (plan == null)
                {
                    _logger.LogWarning("Deserialized plan is null");
                    return new PlanParseResult { IsSuccess = false, Plan = null };
                }

                // Ensure Subtasks is not null
                if (plan.Subtasks == null)
                {
                    _logger.LogWarning("Plan.Subtasks is null, initializing empty list");
                    plan.Subtasks = new List<SubTask>();
                }

                // Validate subtasks
                if (plan.Subtasks.Count == 0)
                {
                    _logger.LogWarning("Plan contains no subtasks");
                    return new PlanParseResult { IsSuccess = false, Plan = plan };
                }

                return new PlanParseResult { IsSuccess = true, Plan = plan };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing plan JSON: {PlanJson}", planJson);
                return new PlanParseResult { IsSuccess = false, Plan = null };
            }
        }
    }
    public class AgentInteraction
    {
        public string AgentName { get; set; } = string.Empty;
        public string Task { get; set; } = string.Empty;
        public string Response { get; set; } = string.Empty;
    }

    public class AgentPlan
    {
        public string Analysis { get; set; } = string.Empty;
        public List<SubTask> Subtasks { get; set; } = new List<SubTask>();
    }

    public class SubTask
    {
        public int Id { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Agent { get; set; } = string.Empty;
        public int[] Requires { get; set; } = Array.Empty<int>();
    }

    public class PlanParseResult
    {
        public bool IsSuccess { get; set; }
        public AgentPlan? Plan { get; set; }
    }
}