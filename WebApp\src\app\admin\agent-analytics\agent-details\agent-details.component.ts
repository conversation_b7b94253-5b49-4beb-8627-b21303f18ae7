import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

// Angular Material/ng-zorro imports
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzStatisticModule } from 'ng-zorro-antd/statistic';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';

// Services and models
import { AgentAnalyticsService } from '../../../services/agent-analytics.service';
import { ThemeService } from '../../../../shared/services/theam.service';
import { AgentPerformance } from '../../../models/agent-performance.models';

@Component({
  selector: 'app-agent-details',
  standalone: true,
  imports: [
    CommonModule,
    NzCardModule,
    NzStatisticModule,
    NzProgressModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzTagModule,
    NzSpinModule,
    NzDividerModule,
    NzAlertModule,
    NzBreadCrumbModule
  ],
  templateUrl: './agent-details.component.html',
  styleUrls: ['./agent-details.component.css']
})
export class AgentDetailsComponent implements OnInit, OnDestroy {
  // Injected services
  private analyticsService = inject(AgentAnalyticsService);
  private themeService = inject(ThemeService);
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private destroy$ = new Subject<void>();

  // Component state
  loading = false;
  agent: AgentPerformance | null = null;
  agentId: string = '';

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.agentId = params['agentId'];
      if (this.agentId) {
        this.loadAgentDetails();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load agent details
   */
  loadAgentDetails(): void {
    this.loading = true;
    this.analyticsService.getAgentPerformance(this.agentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (agent) => {
          this.agent = agent;
          this.loading = false;
        },
        error: (error) => {
          console.error('Error loading agent details:', error);
          this.loading = false;
        }
      });
  }

  /**
   * Navigate back to analytics
   */
  goBack(): void {
    this.router.navigate(['/settings/agent-analytics']);
  }

  /**
   * Get status color for agent
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'red';
      case 'maintenance': return 'orange';
      default: return 'default';
    }
  }

  /**
   * Get performance color based on success rate
   */
  getPerformanceColor(successRate: number): string {
    if (successRate >= 95) return '#52c41a'; // Green
    if (successRate >= 85) return '#faad14'; // Orange
    return '#ff4d4f'; // Red
  }

  /**
   * Format duration for display
   */
  formatDuration(seconds: number): string {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes < 60) {
      return `${minutes}m ${remainingSeconds.toFixed(1)}s`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h ${remainingMinutes}m`;
  }

  /**
   * Check if theme is dark mode
   */
  get isDarkMode(): boolean {
    return this.themeService.isDarkMode();
  }

  /**
   * Get theme-appropriate card class
   */
  get cardClass(): string {
    return this.isDarkMode 
      ? 'bg-[var(--background-white)] border-[var(--hover-blue-gray)]' 
      : 'bg-white border-gray-200';
  }

  /**
   * Get theme-appropriate text class
   */
  get textClass(): string {
    return this.isDarkMode ? 'text-[var(--text-dark)]' : 'text-gray-900';
  }

  /**
   * Get theme-appropriate secondary text class
   */
  get secondaryTextClass(): string {
    return this.isDarkMode ? 'text-[var(--text-medium-gray)]' : 'text-gray-600';
  }
}
