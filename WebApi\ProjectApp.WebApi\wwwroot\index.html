<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 AI Work Hub - Demo Center</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 3.5rem;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .content {
            padding: 60px 40px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .demo-card {
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .demo-card .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-card h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }

        .demo-card p {
            color: #5a6c7d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .demo-card .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .demo-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .features {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 40px;
            margin-top: 40px;
        }

        .features h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }

        .feature-item i {
            color: #667eea;
            font-size: 1.5rem;
        }

        .feature-item span {
            color: #2c3e50;
            font-weight: 500;
        }

        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            background: #27ae60;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .demo-card {
            position: relative;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header h1 {
                font-size: 2.5rem;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> AI Work Hub</h1>
            <p>Advanced AI-Powered Workflow Orchestration & Process Management Platform</p>
        </div>
        
        <div class="content">
            <div class="demo-grid">
                <!-- Process Framework Demo -->
                <div class="demo-card">
                    <div class="status-indicator">NEW</div>
                    <div class="icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3>Semantic Kernel Process Framework</h3>
                    <p>Experience the power of advanced workflow orchestration with AI-powered project registration, intelligent assignment, and automated notifications.</p>
                    <a href="process-framework-demo-enhanced.html" class="btn">
                        <i class="fas fa-play"></i> Launch Demo
                    </a>
                </div>

                <!-- Agent Chat Demo -->
                <div class="demo-card">
                    <div class="icon">
                        <i class="fas fa-comments"></i>
                    </div>
                    <h3>AI Agent Chat System</h3>
                    <p>Interactive chat interface with intelligent AI agents capable of complex conversations and task assistance.</p>
                    <a href="agent-chat-test.html" class="btn">
                        <i class="fas fa-comment"></i> Try Chat
                    </a>
                </div>

                <!-- Plugin Management -->
                <div class="demo-card">
                    <div class="icon">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <h3>Plugin Management</h3>
                    <p>Explore and manage AI plugins with detailed information and configuration options for enhanced functionality.</p>
                    <a href="plugins.html" class="btn">
                        <i class="fas fa-tools"></i> View Plugins
                    </a>
                </div>

                <!-- Workflow Designer -->
                <div class="demo-card">
                    <div class="icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3>Workflow Designer</h3>
                    <p>Visual workflow designer for creating and managing complex business processes with drag-and-drop interface.</p>
                    <a href="workflow.html" class="btn">
                        <i class="fas fa-edit"></i> Design Workflows
                    </a>
                </div>
            </div>

            <div class="features">
                <h2><i class="fas fa-star"></i> Platform Features</h2>
                <div class="feature-grid">
                    <div class="feature-item">
                        <i class="fas fa-brain"></i>
                        <span>AI-Powered Intelligence</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cogs"></i>
                        <span>Process Automation</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>Real-Time Analytics</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Enterprise Security</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-users"></i>
                        <span>Team Collaboration</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-mobile-alt"></i>
                        <span>Mobile Responsive</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-cloud"></i>
                        <span>Cloud Integration</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-rocket"></i>
                        <span>High Performance</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            
            cards.forEach((card, index) => {
                // Add staggered animation
                card.style.animationDelay = `${index * 0.1}s`;
                card.style.animation = 'fadeInUp 0.6s ease forwards';
                
                // Add click tracking
                card.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'A') {
                        const link = card.querySelector('a');
                        if (link) {
                            window.location.href = link.href;
                        }
                    }
                });
            });
        });

        // Add CSS animation keyframes
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .demo-card {
                opacity: 0;
                cursor: pointer;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
