{"version": 3, "sources": ["../../../../../node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-drawer.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, Type, Injector, TemplateRef, Component, ChangeDetectionStrategy, Optional, Inject, Input, Output, ViewChild, ContentChild, Injectable, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { ESCAPE } from '@angular/cdk/keycodes';\nimport * as i2 from '@angular/cdk/overlay';\nimport { OverlayConfig } from '@angular/cdk/overlay';\nimport * as i7 from '@angular/cdk/portal';\nimport { ComponentPortal, TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT, NgStyle, NgTemplateOutlet } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { drawerMaskMotion } from 'ng-zorro-antd/core/animation';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { NzNoAnimationDirective } from 'ng-zorro-antd/core/no-animation';\nimport * as i5 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { overlayZIndexSetter } from 'ng-zorro-antd/core/overlay';\nimport { toCssPixel, isTemplateRef, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i6 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i3 from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"drawerTemplate\"];\nfunction NzDrawerComponent_ng_template_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function NzDrawerComponent_ng_template_0_Conditional_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.maskClick());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"@drawerMaskMotion\", undefined)(\"ngStyle\", ctx_r1.nzMaskStyle);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const closeIcon_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzType\", closeIcon_r4);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NzDrawerComponent_ng_template_0_Conditional_5_Conditional_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.closeClick());\n    });\n    i0.ɵɵtemplate(1, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_2_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzCloseIcon);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.nzTitle, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtemplate(1, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_3_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzTitle);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.nzExtra, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_4_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzExtra);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"div\", 11);\n    i0.ɵɵtemplate(2, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_2_Template, 2, 1, \"button\", 12)(3, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_3_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, NzDrawerComponent_ng_template_0_Conditional_5_Conditional_4_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"ant-drawer-header-close-only\", !ctx_r1.nzTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ctx_r1.nzClosable ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r1.nzTitle ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(4, ctx_r1.nzExtra ? 4 : -1);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_ng_template_7_Template(rf, ctx) {}\nfunction NzDrawerComponent_ng_template_0_Conditional_8_Conditional_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_8_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzDrawerComponent_ng_template_0_Conditional_8_Conditional_0_ng_container_0_Template, 1, 0, \"ng-container\", 19);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzContent)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzDrawerComponent_ng_template_0_Conditional_8_Conditional_0_Template, 1, 2, \"ng-container\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r1.isNzContentTemplateRef ? 0 : -1);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_9_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzDrawerComponent_ng_template_0_Conditional_9_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzDrawerComponent_ng_template_0_Conditional_9_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.contentFromContentChild);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzDrawerComponent_ng_template_0_Conditional_9_Conditional_0_Template, 1, 1, null, 20);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵconditional(0, ctx_r1.contentFromContentChild && (ctx_r1.isOpen || ctx_r1.inAnimation) ? 0 : -1);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.nzFooter, i0.ɵɵsanitizeHtml);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtemplate(1, NzDrawerComponent_ng_template_0_Conditional_10_ng_container_1_Template, 2, 1, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r1.nzFooter);\n  }\n}\nfunction NzDrawerComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtemplate(1, NzDrawerComponent_ng_template_0_Conditional_1_Template, 1, 2, \"div\", 2);\n    i0.ɵɵelementStart(2, \"div\")(3, \"div\", 3)(4, \"div\", 4);\n    i0.ɵɵtemplate(5, NzDrawerComponent_ng_template_0_Conditional_5_Template, 5, 5, \"div\", 5);\n    i0.ɵɵelementStart(6, \"div\", 6);\n    i0.ɵɵtemplate(7, NzDrawerComponent_ng_template_0_ng_template_7_Template, 0, 0, \"ng-template\", 7)(8, NzDrawerComponent_ng_template_0_Conditional_8_Template, 1, 1)(9, NzDrawerComponent_ng_template_0_Conditional_9_Template, 1, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, NzDrawerComponent_ng_template_0_Conditional_10_Template, 2, 1, \"div\", 8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"transform\", ctx_r1.offsetTransform)(\"transition\", ctx_r1.placementChanging ? \"none\" : null)(\"z-index\", ctx_r1.nzZIndex);\n    i0.ɵɵclassProp(\"ant-drawer-rtl\", ctx_r1.dir === \"rtl\")(\"ant-drawer-open\", ctx_r1.isOpen)(\"no-mask\", !ctx_r1.nzMask)(\"ant-drawer-top\", ctx_r1.nzPlacement === \"top\")(\"ant-drawer-bottom\", ctx_r1.nzPlacement === \"bottom\")(\"ant-drawer-right\", ctx_r1.nzPlacement === \"right\")(\"ant-drawer-left\", ctx_r1.nzPlacement === \"left\");\n    i0.ɵɵproperty(\"nzNoAnimation\", ctx_r1.nzNoAnimation);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.nzMask && ctx_r1.isOpen ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"ant-drawer-content-wrapper \", ctx_r1.nzWrapClassName, \"\");\n    i0.ɵɵstyleProp(\"width\", ctx_r1.width)(\"height\", ctx_r1.height)(\"transform\", ctx_r1.transform)(\"transition\", ctx_r1.placementChanging ? \"none\" : null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r1.isLeftOrRight ? \"100%\" : null);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(5, ctx_r1.nzTitle || ctx_r1.nzClosable ? 5 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.nzBodyStyle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(8, ctx_r1.nzContent ? 8 : 9);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(10, ctx_r1.nzFooter ? 10 : -1);\n  }\n}\nclass NzDrawerContentDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static {\n    this.ɵfac = function NzDrawerContentDirective_Factory(t) {\n      return new (t || NzDrawerContentDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzDrawerContentDirective,\n      selectors: [[\"\", \"nzDrawerContent\", \"\"]],\n      exportAs: [\"nzDrawerContent\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDrawerContentDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzDrawerContent]',\n      exportAs: 'nzDrawerContent',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst DRAWER_DEFAULT_SIZE = 378;\nconst DRAWER_LARGE_SIZE = 736;\nconst NZ_DRAWER_DATA = new InjectionToken('NZ_DRAWER_DATA');\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDrawerRef {}\nconst DRAWER_ANIMATE_DURATION = 300;\nconst NZ_CONFIG_MODULE_NAME = 'drawer';\nclass NzDrawerComponent extends NzDrawerRef {\n  set nzVisible(value) {\n    this.isOpen = value;\n  }\n  get nzVisible() {\n    return this.isOpen;\n  }\n  get offsetTransform() {\n    if (!this.isOpen || this.nzOffsetX + this.nzOffsetY === 0) {\n      return null;\n    }\n    switch (this.nzPlacement) {\n      case 'left':\n        return `translateX(${this.nzOffsetX}px)`;\n      case 'right':\n        return `translateX(-${this.nzOffsetX}px)`;\n      case 'top':\n        return `translateY(${this.nzOffsetY}px)`;\n      case 'bottom':\n        return `translateY(-${this.nzOffsetY}px)`;\n    }\n  }\n  get transform() {\n    if (this.isOpen) {\n      return null;\n    }\n    switch (this.nzPlacement) {\n      case 'left':\n        return `translateX(-100%)`;\n      case 'right':\n        return `translateX(100%)`;\n      case 'top':\n        return `translateY(-100%)`;\n      case 'bottom':\n        return `translateY(100%)`;\n    }\n  }\n  get width() {\n    if (this.isLeftOrRight) {\n      const defaultWidth = this.nzSize === 'large' ? DRAWER_LARGE_SIZE : DRAWER_DEFAULT_SIZE;\n      return this.nzWidth === undefined ? toCssPixel(defaultWidth) : toCssPixel(this.nzWidth);\n    }\n    return null;\n  }\n  get height() {\n    if (!this.isLeftOrRight) {\n      const defaultHeight = this.nzSize === 'large' ? DRAWER_LARGE_SIZE : DRAWER_DEFAULT_SIZE;\n      return this.nzHeight === undefined ? toCssPixel(defaultHeight) : toCssPixel(this.nzHeight);\n    }\n    return null;\n  }\n  get isLeftOrRight() {\n    return this.nzPlacement === 'left' || this.nzPlacement === 'right';\n  }\n  get afterOpen() {\n    return this.nzAfterOpen.asObservable();\n  }\n  get afterClose() {\n    return this.nzAfterClose.asObservable();\n  }\n  get isNzContentTemplateRef() {\n    return isTemplateRef(this.nzContent);\n  }\n  constructor(cdr,\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  document, nzConfigService, renderer, overlay, injector, changeDetectorRef, focusTrapFactory, viewContainerRef, overlayKeyboardDispatcher, directionality) {\n    super();\n    this.cdr = cdr;\n    this.document = document;\n    this.nzConfigService = nzConfigService;\n    this.renderer = renderer;\n    this.overlay = overlay;\n    this.injector = injector;\n    this.changeDetectorRef = changeDetectorRef;\n    this.focusTrapFactory = focusTrapFactory;\n    this.viewContainerRef = viewContainerRef;\n    this.overlayKeyboardDispatcher = overlayKeyboardDispatcher;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzCloseIcon = 'close';\n    this.nzClosable = true;\n    this.nzMaskClosable = true;\n    this.nzMask = true;\n    this.nzCloseOnNavigation = true;\n    this.nzNoAnimation = false;\n    this.nzKeyboard = true;\n    this.nzPlacement = 'right';\n    this.nzSize = 'default';\n    this.nzMaskStyle = {};\n    this.nzBodyStyle = {};\n    this.nzZIndex = 1000;\n    this.nzOffsetX = 0;\n    this.nzOffsetY = 0;\n    this.componentInstance = null;\n    this.componentRef = null;\n    this.nzOnViewInit = new EventEmitter();\n    this.nzOnClose = new EventEmitter();\n    this.nzVisibleChange = new EventEmitter();\n    this.destroy$ = new Subject();\n    this.placementChanging = false;\n    this.isOpen = false;\n    this.inAnimation = false;\n    this.templateContext = {\n      $implicit: undefined,\n      drawerRef: this\n    };\n    this.nzAfterOpen = new Subject();\n    this.nzAfterClose = new Subject();\n    // from service config\n    this.nzDirection = undefined;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.nzDirection || this.directionality.value;\n    this.attachOverlay();\n    this.updateOverlayStyle();\n    this.updateBodyOverflow();\n    this.templateContext = {\n      $implicit: this.nzData || this.nzContentParams,\n      drawerRef: this\n    };\n    this.changeDetectorRef.detectChanges();\n  }\n  ngAfterViewInit() {\n    this.attachBodyContent();\n    // The `setTimeout` triggers change detection. There's no sense to schedule the DOM timer if anyone is\n    // listening to the `nzOnViewInit` event inside the template, for instance `<nz-drawer (nzOnViewInit)=\"...\">`.\n    if (this.nzOnViewInit.observers.length) {\n      setTimeout(() => {\n        this.nzOnViewInit.emit();\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      nzPlacement,\n      nzVisible\n    } = changes;\n    if (nzVisible) {\n      const value = changes.nzVisible.currentValue;\n      if (value) {\n        this.open();\n      } else {\n        this.close();\n      }\n    }\n    if (nzPlacement && !nzPlacement.isFirstChange()) {\n      this.triggerPlacementChangeCycleOnce();\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n    clearTimeout(this.placementChangeTimeoutId);\n    this.disposeOverlay();\n  }\n  getAnimationDuration() {\n    return this.nzNoAnimation ? 0 : DRAWER_ANIMATE_DURATION;\n  }\n  // Disable the transition animation temporarily when the placement changing\n  triggerPlacementChangeCycleOnce() {\n    if (!this.nzNoAnimation) {\n      this.placementChanging = true;\n      this.changeDetectorRef.markForCheck();\n      clearTimeout(this.placementChangeTimeoutId);\n      this.placementChangeTimeoutId = setTimeout(() => {\n        this.placementChanging = false;\n        this.changeDetectorRef.markForCheck();\n      }, this.getAnimationDuration());\n    }\n  }\n  close(result) {\n    this.isOpen = false;\n    this.inAnimation = true;\n    this.nzVisibleChange.emit(false);\n    this.updateOverlayStyle();\n    this.overlayKeyboardDispatcher.remove(this.overlayRef);\n    this.changeDetectorRef.detectChanges();\n    setTimeout(() => {\n      this.updateBodyOverflow();\n      this.restoreFocus();\n      this.inAnimation = false;\n      this.nzAfterClose.next(result);\n      this.nzAfterClose.complete();\n      this.componentInstance = null;\n      this.componentRef = null;\n    }, this.getAnimationDuration());\n  }\n  open() {\n    this.attachOverlay();\n    this.isOpen = true;\n    this.inAnimation = true;\n    this.nzVisibleChange.emit(true);\n    this.overlayKeyboardDispatcher.add(this.overlayRef);\n    this.updateOverlayStyle();\n    this.updateBodyOverflow();\n    this.savePreviouslyFocusedElement();\n    this.trapFocus();\n    this.changeDetectorRef.detectChanges();\n    setTimeout(() => {\n      this.inAnimation = false;\n      this.changeDetectorRef.detectChanges();\n      this.nzAfterOpen.next();\n    }, this.getAnimationDuration());\n  }\n  getContentComponent() {\n    return this.componentInstance;\n  }\n  getContentComponentRef() {\n    return this.componentRef;\n  }\n  closeClick() {\n    this.nzOnClose.emit();\n  }\n  maskClick() {\n    if (this.nzMaskClosable && this.nzMask) {\n      this.nzOnClose.emit();\n    }\n  }\n  attachBodyContent() {\n    this.bodyPortalOutlet.dispose();\n    if (this.nzContent instanceof Type) {\n      const childInjector = Injector.create({\n        parent: this.injector,\n        providers: [{\n          provide: NzDrawerRef,\n          useValue: this\n        }, {\n          provide: NZ_DRAWER_DATA,\n          useValue: this.nzData\n        }]\n      });\n      const componentPortal = new ComponentPortal(this.nzContent, null, childInjector);\n      this.componentRef = this.bodyPortalOutlet.attachComponentPortal(componentPortal);\n      this.componentInstance = this.componentRef.instance;\n      /**TODO\n       * When nzContentParam will be remove in the next major version, we have to remove the following line\n       * **/\n      Object.assign(this.componentRef.instance, this.nzData || this.nzContentParams);\n      this.componentRef.changeDetectorRef.detectChanges();\n    }\n  }\n  attachOverlay() {\n    if (!this.overlayRef) {\n      this.portal = new TemplatePortal(this.drawerTemplate, this.viewContainerRef);\n      this.overlayRef = this.overlay.create(this.getOverlayConfig());\n      overlayZIndexSetter(this.overlayRef, this.nzZIndex);\n    }\n    if (this.overlayRef && !this.overlayRef.hasAttached()) {\n      this.overlayRef.attach(this.portal);\n      this.overlayRef.keydownEvents().pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (event.keyCode === ESCAPE && this.isOpen && this.nzKeyboard) {\n          this.nzOnClose.emit();\n        }\n      });\n      this.overlayRef.detachments().pipe(takeUntil(this.destroy$)).subscribe(() => {\n        this.disposeOverlay();\n      });\n    }\n  }\n  disposeOverlay() {\n    this.overlayRef?.dispose();\n    this.overlayRef = null;\n  }\n  getOverlayConfig() {\n    return new OverlayConfig({\n      disposeOnNavigation: this.nzCloseOnNavigation,\n      positionStrategy: this.overlay.position().global(),\n      scrollStrategy: this.overlay.scrollStrategies.block()\n    });\n  }\n  updateOverlayStyle() {\n    if (this.overlayRef && this.overlayRef.overlayElement) {\n      this.renderer.setStyle(this.overlayRef.overlayElement, 'pointer-events', this.isOpen ? 'auto' : 'none');\n    }\n  }\n  updateBodyOverflow() {\n    if (this.overlayRef) {\n      if (this.isOpen) {\n        this.overlayRef.getConfig().scrollStrategy.enable();\n      } else {\n        this.overlayRef.getConfig().scrollStrategy.disable();\n      }\n    }\n  }\n  savePreviouslyFocusedElement() {\n    if (this.document && !this.previouslyFocusedElement) {\n      this.previouslyFocusedElement = this.document.activeElement;\n      // We need the extra check, because IE's svg element has no blur method.\n      if (this.previouslyFocusedElement && typeof this.previouslyFocusedElement.blur === 'function') {\n        this.previouslyFocusedElement.blur();\n      }\n    }\n  }\n  trapFocus() {\n    if (!this.focusTrap && this.overlayRef && this.overlayRef.overlayElement) {\n      this.focusTrap = this.focusTrapFactory.create(this.overlayRef.overlayElement);\n      this.focusTrap.focusInitialElement();\n    }\n  }\n  restoreFocus() {\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this.previouslyFocusedElement && typeof this.previouslyFocusedElement.focus === 'function') {\n      this.previouslyFocusedElement.focus();\n    }\n    if (this.focusTrap) {\n      this.focusTrap.destroy();\n    }\n  }\n  static {\n    this.ɵfac = function NzDrawerComponent_Factory(t) {\n      return new (t || NzDrawerComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.Overlay), i0.ɵɵdirectiveInject(i0.Injector), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FocusTrapFactory), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i2.OverlayKeyboardDispatcher), i0.ɵɵdirectiveInject(i4.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzDrawerComponent,\n      selectors: [[\"nz-drawer\"]],\n      contentQueries: function NzDrawerComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzDrawerContentDirective, 7, TemplateRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentFromContentChild = _t.first);\n        }\n      },\n      viewQuery: function NzDrawerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(CdkPortalOutlet, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.drawerTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bodyPortalOutlet = _t.first);\n        }\n      },\n      inputs: {\n        nzContent: \"nzContent\",\n        nzCloseIcon: \"nzCloseIcon\",\n        nzClosable: \"nzClosable\",\n        nzMaskClosable: \"nzMaskClosable\",\n        nzMask: \"nzMask\",\n        nzCloseOnNavigation: \"nzCloseOnNavigation\",\n        nzNoAnimation: \"nzNoAnimation\",\n        nzKeyboard: \"nzKeyboard\",\n        nzTitle: \"nzTitle\",\n        nzExtra: \"nzExtra\",\n        nzFooter: \"nzFooter\",\n        nzPlacement: \"nzPlacement\",\n        nzSize: \"nzSize\",\n        nzMaskStyle: \"nzMaskStyle\",\n        nzBodyStyle: \"nzBodyStyle\",\n        nzWrapClassName: \"nzWrapClassName\",\n        nzWidth: \"nzWidth\",\n        nzHeight: \"nzHeight\",\n        nzZIndex: \"nzZIndex\",\n        nzOffsetX: \"nzOffsetX\",\n        nzOffsetY: \"nzOffsetY\",\n        nzVisible: \"nzVisible\"\n      },\n      outputs: {\n        nzOnViewInit: \"nzOnViewInit\",\n        nzOnClose: \"nzOnClose\",\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzDrawer\"],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"drawerTemplate\", \"\"], [1, \"ant-drawer\", 3, \"nzNoAnimation\"], [1, \"ant-drawer-mask\", 3, \"ngStyle\"], [1, \"ant-drawer-content\"], [1, \"ant-drawer-wrapper-body\"], [1, \"ant-drawer-header\", 3, \"ant-drawer-header-close-only\"], [1, \"ant-drawer-body\", 3, \"ngStyle\"], [\"cdkPortalOutlet\", \"\"], [1, \"ant-drawer-footer\"], [1, \"ant-drawer-mask\", 3, \"click\", \"ngStyle\"], [1, \"ant-drawer-header\"], [1, \"ant-drawer-header-title\"], [\"aria-label\", \"Close\", 1, \"ant-drawer-close\"], [1, \"ant-drawer-title\"], [1, \"ant-drawer-extra\"], [\"aria-label\", \"Close\", 1, \"ant-drawer-close\", 3, \"click\"], [4, \"nzStringTemplateOutlet\"], [\"nz-icon\", \"\", 3, \"nzType\"], [3, \"innerHTML\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzDrawerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzDrawerComponent_ng_template_0_Template, 11, 39, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NzNoAnimationDirective, NgStyle, NzOutletModule, i5.NzStringTemplateOutletDirective, NzIconModule, i6.NzIconDirective, PortalModule, i7.CdkPortalOutlet, NgTemplateOutlet],\n      encapsulation: 2,\n      data: {\n        animation: [drawerMaskMotion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzDrawerComponent.prototype, \"nzClosable\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzDrawerComponent.prototype, \"nzMaskClosable\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzDrawerComponent.prototype, \"nzMask\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzDrawerComponent.prototype, \"nzCloseOnNavigation\", void 0);\n__decorate([InputBoolean()], NzDrawerComponent.prototype, \"nzNoAnimation\", void 0);\n__decorate([InputBoolean()], NzDrawerComponent.prototype, \"nzKeyboard\", void 0);\n__decorate([WithConfig()], NzDrawerComponent.prototype, \"nzDirection\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDrawerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-drawer',\n      exportAs: 'nzDrawer',\n      template: `\n    <ng-template #drawerTemplate>\n      <div\n        class=\"ant-drawer\"\n        [nzNoAnimation]=\"nzNoAnimation\"\n        [class.ant-drawer-rtl]=\"dir === 'rtl'\"\n        [class.ant-drawer-open]=\"isOpen\"\n        [class.no-mask]=\"!nzMask\"\n        [class.ant-drawer-top]=\"nzPlacement === 'top'\"\n        [class.ant-drawer-bottom]=\"nzPlacement === 'bottom'\"\n        [class.ant-drawer-right]=\"nzPlacement === 'right'\"\n        [class.ant-drawer-left]=\"nzPlacement === 'left'\"\n        [style.transform]=\"offsetTransform\"\n        [style.transition]=\"placementChanging ? 'none' : null\"\n        [style.zIndex]=\"nzZIndex\"\n      >\n        @if (nzMask && isOpen) {\n          <div @drawerMaskMotion class=\"ant-drawer-mask\" (click)=\"maskClick()\" [ngStyle]=\"nzMaskStyle\"></div>\n        }\n        <div\n          class=\"ant-drawer-content-wrapper {{ nzWrapClassName }}\"\n          [style.width]=\"width\"\n          [style.height]=\"height\"\n          [style.transform]=\"transform\"\n          [style.transition]=\"placementChanging ? 'none' : null\"\n        >\n          <div class=\"ant-drawer-content\">\n            <div class=\"ant-drawer-wrapper-body\" [style.height]=\"isLeftOrRight ? '100%' : null\">\n              @if (nzTitle || nzClosable) {\n                <div class=\"ant-drawer-header\" [class.ant-drawer-header-close-only]=\"!nzTitle\">\n                  <div class=\"ant-drawer-header-title\">\n                    @if (nzClosable) {\n                      <button (click)=\"closeClick()\" aria-label=\"Close\" class=\"ant-drawer-close\">\n                        <ng-container *nzStringTemplateOutlet=\"nzCloseIcon; let closeIcon\">\n                          <span nz-icon [nzType]=\"closeIcon\"></span>\n                        </ng-container>\n                      </button>\n                    }\n\n                    @if (nzTitle) {\n                      <div class=\"ant-drawer-title\">\n                        <ng-container *nzStringTemplateOutlet=\"nzTitle\">\n                          <div [innerHTML]=\"nzTitle\"></div>\n                        </ng-container>\n                      </div>\n                    }\n                  </div>\n                  @if (nzExtra) {\n                    <div class=\"ant-drawer-extra\">\n                      <ng-container *nzStringTemplateOutlet=\"nzExtra\">\n                        <div [innerHTML]=\"nzExtra\"></div>\n                      </ng-container>\n                    </div>\n                  }\n                </div>\n              }\n              <div class=\"ant-drawer-body\" [ngStyle]=\"nzBodyStyle\">\n                <ng-template cdkPortalOutlet />\n                @if (nzContent) {\n                  @if (isNzContentTemplateRef) {\n                    <ng-container *ngTemplateOutlet=\"$any(nzContent); context: templateContext\" />\n                  }\n                } @else {\n                  @if (contentFromContentChild && (isOpen || inAnimation)) {\n                    <ng-template [ngTemplateOutlet]=\"contentFromContentChild\" />\n                  }\n                }\n              </div>\n              @if (nzFooter) {\n                <div class=\"ant-drawer-footer\">\n                  <ng-container *nzStringTemplateOutlet=\"nzFooter\">\n                    <div [innerHTML]=\"nzFooter\"></div>\n                  </ng-container>\n                </div>\n              }\n            </div>\n          </div>\n        </div>\n      </div>\n    </ng-template>\n  `,\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [drawerMaskMotion],\n      imports: [NzNoAnimationDirective, NgStyle, NzOutletModule, NzIconModule, PortalModule, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i2.Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i3.FocusTrapFactory\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i2.OverlayKeyboardDispatcher\n  }, {\n    type: i4.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzContent: [{\n      type: Input\n    }],\n    nzCloseIcon: [{\n      type: Input\n    }],\n    nzClosable: [{\n      type: Input\n    }],\n    nzMaskClosable: [{\n      type: Input\n    }],\n    nzMask: [{\n      type: Input\n    }],\n    nzCloseOnNavigation: [{\n      type: Input\n    }],\n    nzNoAnimation: [{\n      type: Input\n    }],\n    nzKeyboard: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzExtra: [{\n      type: Input\n    }],\n    nzFooter: [{\n      type: Input\n    }],\n    nzPlacement: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzMaskStyle: [{\n      type: Input\n    }],\n    nzBodyStyle: [{\n      type: Input\n    }],\n    nzWrapClassName: [{\n      type: Input\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzHeight: [{\n      type: Input\n    }],\n    nzZIndex: [{\n      type: Input\n    }],\n    nzOffsetX: [{\n      type: Input\n    }],\n    nzOffsetY: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzOnViewInit: [{\n      type: Output\n    }],\n    nzOnClose: [{\n      type: Output\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }],\n    drawerTemplate: [{\n      type: ViewChild,\n      args: ['drawerTemplate', {\n        static: true\n      }]\n    }],\n    bodyPortalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: false\n      }]\n    }],\n    contentFromContentChild: [{\n      type: ContentChild,\n      args: [NzDrawerContentDirective, {\n        static: true,\n        read: TemplateRef\n      }]\n    }],\n    nzDirection: []\n  });\n})();\nclass DrawerBuilderForService {\n  constructor(overlay, options) {\n    this.overlay = overlay;\n    this.options = options;\n    this.unsubscribe$ = new Subject();\n    /** pick {@link NzDrawerOptions.nzOnCancel} and omit this option */\n    const {\n      nzOnCancel,\n      ...componentOption\n    } = this.options;\n    this.overlayRef = this.overlay.create();\n    this.drawerRef = this.overlayRef.attach(new ComponentPortal(NzDrawerComponent)).instance;\n    this.updateOptions(componentOption);\n    // Prevent repeatedly open drawer when tap focus element.\n    this.drawerRef.savePreviouslyFocusedElement();\n    this.drawerRef.nzOnViewInit.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.drawerRef.open();\n    });\n    this.drawerRef.nzOnClose.subscribe(() => {\n      if (nzOnCancel) {\n        nzOnCancel().then(canClose => {\n          if (canClose !== false) {\n            this.drawerRef.close();\n          }\n        });\n      } else {\n        this.drawerRef.close();\n      }\n    });\n    this.drawerRef.afterClose.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {\n      this.overlayRef.dispose();\n      this.drawerRef = null;\n      this.unsubscribe$.next();\n      this.unsubscribe$.complete();\n    });\n  }\n  getInstance() {\n    return this.drawerRef;\n  }\n  updateOptions(options) {\n    Object.assign(this.drawerRef, options);\n  }\n}\nclass NzDrawerService {\n  constructor(overlay) {\n    this.overlay = overlay;\n  }\n  create(options) {\n    return new DrawerBuilderForService(this.overlay, options).getInstance();\n  }\n  static {\n    this.ɵfac = function NzDrawerService_Factory(t) {\n      return new (t || NzDrawerService)(i0.ɵɵinject(i2.Overlay));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzDrawerService,\n      factory: NzDrawerService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDrawerService, [{\n    type: Injectable\n  }], () => [{\n    type: i2.Overlay\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzDrawerModule {\n  static {\n    this.ɵfac = function NzDrawerModule_Factory(t) {\n      return new (t || NzDrawerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzDrawerModule,\n      imports: [NzDrawerComponent, NzDrawerContentDirective],\n      exports: [NzDrawerComponent, NzDrawerContentDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [NzDrawerService],\n      imports: [NzDrawerComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzDrawerModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzDrawerComponent, NzDrawerContentDirective],\n      providers: [NzDrawerService],\n      exports: [NzDrawerComponent, NzDrawerContentDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DRAWER_ANIMATE_DURATION, DRAWER_DEFAULT_SIZE, DRAWER_LARGE_SIZE, DrawerBuilderForService, NZ_DRAWER_DATA, NzDrawerComponent, NzDrawerContentDirective, NzDrawerModule, NzDrawerRef, NzDrawerService };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,IAAM,MAAM,CAAC,gBAAgB;AAC7B,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,SAAS,SAAS,8EAA8E;AAC5G,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,UAAU,CAAC;AAAA,IAC1C,CAAC;AACD,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,qBAAqB,MAAS,EAAE,WAAW,OAAO,WAAW;AAAA,EAC7E;AACF;AACA,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,QAAQ,EAAE;AAC1B,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,eAAe,IAAI;AACzB,IAAG,UAAU;AACb,IAAG,WAAW,UAAU,YAAY;AAAA,EACtC;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,UAAM,MAAS,iBAAiB;AAChC,IAAG,eAAe,GAAG,UAAU,EAAE;AACjC,IAAG,WAAW,SAAS,SAAS,+FAA+F;AAC7H,MAAG,cAAc,GAAG;AACpB,YAAM,SAAY,cAAc,CAAC;AACjC,aAAU,YAAY,OAAO,WAAW,CAAC;AAAA,IAC3C,CAAC;AACD,IAAG,WAAW,GAAG,qFAAqF,GAAG,GAAG,gBAAgB,EAAE;AAC9H,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,WAAW;AAAA,EAC5D;AACF;AACA,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAY,cAAc;AAAA,EAC9D;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,qFAAqF,GAAG,GAAG,gBAAgB,EAAE;AAC9H,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,SAAY,cAAc;AAAA,EAC9D;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE;AAC9B,IAAG,WAAW,GAAG,qFAAqF,GAAG,GAAG,gBAAgB,EAAE;AAC9H,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,OAAO;AAAA,EACxD;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE;AAC5C,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,UAAU,EAAE,EAAE,GAAG,sEAAsE,GAAG,GAAG,OAAO,EAAE;AACnM,IAAG,aAAa;AAChB,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,OAAO,EAAE;AACtG,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,YAAY,gCAAgC,CAAC,OAAO,OAAO;AAC9D,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,GAAG,OAAO,aAAa,IAAI,EAAE;AAC9C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAC3C,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,IAAI,EAAE;AAAA,EAC7C;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AAAC;AAC1E,SAAS,oFAAoF,IAAI,KAAK;AACpG,MAAI,KAAK,GAAG;AACV,IAAG,mBAAmB,CAAC;AAAA,EACzB;AACF;AACA,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,qFAAqF,GAAG,GAAG,gBAAgB,EAAE;AAAA,EAChI;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,SAAS,EAAE,2BAA2B,OAAO,eAAe;AAAA,EACvG;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,cAAc;AAAA,EAC7G;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,GAAG,OAAO,yBAAyB,IAAI,EAAE;AAAA,EAC5D;AACF;AACA,SAAS,mFAAmF,IAAI,KAAK;AAAC;AACtG,SAAS,qEAAqE,IAAI,KAAK;AACrF,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,oFAAoF,GAAG,GAAG,eAAe,EAAE;AAAA,EAC9H;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,WAAW,oBAAoB,OAAO,uBAAuB;AAAA,EAClE;AACF;AACA,SAAS,uDAAuD,IAAI,KAAK;AACvE,MAAI,KAAK,GAAG;AACV,IAAG,WAAW,GAAG,sEAAsE,GAAG,GAAG,MAAM,EAAE;AAAA,EACvG;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,cAAc,GAAG,OAAO,4BAA4B,OAAO,UAAU,OAAO,eAAe,IAAI,EAAE;AAAA,EACtG;AACF;AACA,SAAS,uEAAuE,IAAI,KAAK;AACvF,MAAI,KAAK,GAAG;AACV,IAAG,wBAAwB,CAAC;AAC5B,IAAG,UAAU,GAAG,OAAO,EAAE;AACzB,IAAG,sBAAsB;AAAA,EAC3B;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,aAAa,OAAO,UAAa,cAAc;AAAA,EAC/D;AACF;AACA,SAAS,wDAAwD,IAAI,KAAK;AACxE,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wEAAwE,GAAG,GAAG,gBAAgB,EAAE;AACjH,IAAG,aAAa;AAAA,EAClB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc,CAAC;AACjC,IAAG,UAAU;AACb,IAAG,WAAW,0BAA0B,OAAO,QAAQ;AAAA,EACzD;AACF;AACA,SAAS,yCAAyC,IAAI,KAAK;AACzD,MAAI,KAAK,GAAG;AACV,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,OAAO,CAAC;AACvF,IAAG,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC;AACpD,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,OAAO,CAAC;AACvF,IAAG,eAAe,GAAG,OAAO,CAAC;AAC7B,IAAG,WAAW,GAAG,wDAAwD,GAAG,GAAG,eAAe,CAAC,EAAE,GAAG,wDAAwD,GAAG,CAAC,EAAE,GAAG,wDAAwD,GAAG,CAAC;AACjO,IAAG,aAAa;AAChB,IAAG,WAAW,IAAI,yDAAyD,GAAG,GAAG,OAAO,CAAC;AACzF,IAAG,aAAa,EAAE,EAAE,EAAE;AAAA,EACxB;AACA,MAAI,KAAK,GAAG;AACV,UAAM,SAAY,cAAc;AAChC,IAAG,YAAY,aAAa,OAAO,eAAe,EAAE,cAAc,OAAO,oBAAoB,SAAS,IAAI,EAAE,WAAW,OAAO,QAAQ;AACtI,IAAG,YAAY,kBAAkB,OAAO,QAAQ,KAAK,EAAE,mBAAmB,OAAO,MAAM,EAAE,WAAW,CAAC,OAAO,MAAM,EAAE,kBAAkB,OAAO,gBAAgB,KAAK,EAAE,qBAAqB,OAAO,gBAAgB,QAAQ,EAAE,oBAAoB,OAAO,gBAAgB,OAAO,EAAE,mBAAmB,OAAO,gBAAgB,MAAM;AAC9T,IAAG,WAAW,iBAAiB,OAAO,aAAa;AACnD,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,UAAU,OAAO,SAAS,IAAI,EAAE;AAC3D,IAAG,UAAU;AACb,IAAG,uBAAuB,+BAA+B,OAAO,iBAAiB,EAAE;AACnF,IAAG,YAAY,SAAS,OAAO,KAAK,EAAE,UAAU,OAAO,MAAM,EAAE,aAAa,OAAO,SAAS,EAAE,cAAc,OAAO,oBAAoB,SAAS,IAAI;AACpJ,IAAG,UAAU,CAAC;AACd,IAAG,YAAY,UAAU,OAAO,gBAAgB,SAAS,IAAI;AAC7D,IAAG,UAAU;AACb,IAAG,cAAc,GAAG,OAAO,WAAW,OAAO,aAAa,IAAI,EAAE;AAChE,IAAG,UAAU;AACb,IAAG,WAAW,WAAW,OAAO,WAAW;AAC3C,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,GAAG,OAAO,YAAY,IAAI,CAAC;AAC5C,IAAG,UAAU,CAAC;AACd,IAAG,cAAc,IAAI,OAAO,WAAW,KAAK,EAAE;AAAA,EAChD;AACF;AACA,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,aAAa;AACvB,SAAK,cAAc;AAAA,EACrB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,GAAG;AACvD,aAAO,KAAK,KAAK,2BAA6B,kBAAqB,WAAW,CAAC;AAAA,IACjF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,mBAAmB,EAAE,CAAC;AAAA,MACvC,UAAU,CAAC,iBAAiB;AAAA,MAC5B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB,IAAI,eAAe,gBAAgB;AAM1D,IAAM,cAAN,MAAkB;AAAC;AACnB,IAAM,0BAA0B;AAChC,IAAM,wBAAwB;AAC9B,IAAM,oBAAN,MAAM,2BAA0B,YAAY;AAAA,EAC1C,IAAI,UAAU,OAAO;AACnB,SAAK,SAAS;AAAA,EAChB;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK;AAAA,EACd;AAAA,EACA,IAAI,kBAAkB;AACpB,QAAI,CAAC,KAAK,UAAU,KAAK,YAAY,KAAK,cAAc,GAAG;AACzD,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,aAAa;AAAA,MACxB,KAAK;AACH,eAAO,cAAc,KAAK,SAAS;AAAA,MACrC,KAAK;AACH,eAAO,eAAe,KAAK,SAAS;AAAA,MACtC,KAAK;AACH,eAAO,cAAc,KAAK,SAAS;AAAA,MACrC,KAAK;AACH,eAAO,eAAe,KAAK,SAAS;AAAA,IACxC;AAAA,EACF;AAAA,EACA,IAAI,YAAY;AACd,QAAI,KAAK,QAAQ;AACf,aAAO;AAAA,IACT;AACA,YAAQ,KAAK,aAAa;AAAA,MACxB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,KAAK,eAAe;AACtB,YAAM,eAAe,KAAK,WAAW,UAAU,oBAAoB;AACnE,aAAO,KAAK,YAAY,SAAY,WAAW,YAAY,IAAI,WAAW,KAAK,OAAO;AAAA,IACxF;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,SAAS;AACX,QAAI,CAAC,KAAK,eAAe;AACvB,YAAM,gBAAgB,KAAK,WAAW,UAAU,oBAAoB;AACpE,aAAO,KAAK,aAAa,SAAY,WAAW,aAAa,IAAI,WAAW,KAAK,QAAQ;AAAA,IAC3F;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAI,gBAAgB;AAClB,WAAO,KAAK,gBAAgB,UAAU,KAAK,gBAAgB;AAAA,EAC7D;AAAA,EACA,IAAI,YAAY;AACd,WAAO,KAAK,YAAY,aAAa;AAAA,EACvC;AAAA,EACA,IAAI,aAAa;AACf,WAAO,KAAK,aAAa,aAAa;AAAA,EACxC;AAAA,EACA,IAAI,yBAAyB;AAC3B,WAAO,cAAc,KAAK,SAAS;AAAA,EACrC;AAAA,EACA,YAAY,KAEZ,UAAU,iBAAiB,UAAU,SAAS,UAAU,mBAAmB,kBAAkB,kBAAkB,2BAA2B,gBAAgB;AACxJ,UAAM;AACN,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,kBAAkB;AACvB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,4BAA4B;AACjC,SAAK,iBAAiB;AACtB,SAAK,gBAAgB;AACrB,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,iBAAiB;AACtB,SAAK,SAAS;AACd,SAAK,sBAAsB;AAC3B,SAAK,gBAAgB;AACrB,SAAK,aAAa;AAClB,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,cAAc,CAAC;AACpB,SAAK,cAAc,CAAC;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,eAAe;AACpB,SAAK,eAAe,IAAI,aAAa;AACrC,SAAK,YAAY,IAAI,aAAa;AAClC,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,oBAAoB;AACzB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,kBAAkB;AAAA,MACrB,WAAW;AAAA,MACX,WAAW;AAAA,IACb;AACA,SAAK,cAAc,IAAI,QAAQ;AAC/B,SAAK,eAAe,IAAI,QAAQ;AAEhC,SAAK,cAAc;AACnB,SAAK,MAAM;AAAA,EACb;AAAA,EACA,WAAW;AACT,SAAK,eAAe,QAAQ,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,eAAa;AAChF,WAAK,MAAM;AACX,WAAK,IAAI,cAAc;AAAA,IACzB,CAAC;AACD,SAAK,MAAM,KAAK,eAAe,KAAK,eAAe;AACnD,SAAK,cAAc;AACnB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,kBAAkB;AAAA,MACrB,WAAW,KAAK,UAAU,KAAK;AAAA,MAC/B,WAAW;AAAA,IACb;AACA,SAAK,kBAAkB,cAAc;AAAA,EACvC;AAAA,EACA,kBAAkB;AAChB,SAAK,kBAAkB;AAGvB,QAAI,KAAK,aAAa,UAAU,QAAQ;AACtC,iBAAW,MAAM;AACf,aAAK,aAAa,KAAK;AAAA,MACzB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,YAAY,SAAS;AACnB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,WAAW;AACb,YAAM,QAAQ,QAAQ,UAAU;AAChC,UAAI,OAAO;AACT,aAAK,KAAK;AAAA,MACZ,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,QAAI,eAAe,CAAC,YAAY,cAAc,GAAG;AAC/C,WAAK,gCAAgC;AAAA,IACvC;AAAA,EACF;AAAA,EACA,cAAc;AACZ,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,SAAS;AACvB,iBAAa,KAAK,wBAAwB;AAC1C,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,uBAAuB;AACrB,WAAO,KAAK,gBAAgB,IAAI;AAAA,EAClC;AAAA;AAAA,EAEA,kCAAkC;AAChC,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,oBAAoB;AACzB,WAAK,kBAAkB,aAAa;AACpC,mBAAa,KAAK,wBAAwB;AAC1C,WAAK,2BAA2B,WAAW,MAAM;AAC/C,aAAK,oBAAoB;AACzB,aAAK,kBAAkB,aAAa;AAAA,MACtC,GAAG,KAAK,qBAAqB,CAAC;AAAA,IAChC;AAAA,EACF;AAAA,EACA,MAAM,QAAQ;AACZ,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,gBAAgB,KAAK,KAAK;AAC/B,SAAK,mBAAmB;AACxB,SAAK,0BAA0B,OAAO,KAAK,UAAU;AACrD,SAAK,kBAAkB,cAAc;AACrC,eAAW,MAAM;AACf,WAAK,mBAAmB;AACxB,WAAK,aAAa;AAClB,WAAK,cAAc;AACnB,WAAK,aAAa,KAAK,MAAM;AAC7B,WAAK,aAAa,SAAS;AAC3B,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACtB,GAAG,KAAK,qBAAqB,CAAC;AAAA,EAChC;AAAA,EACA,OAAO;AACL,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,gBAAgB,KAAK,IAAI;AAC9B,SAAK,0BAA0B,IAAI,KAAK,UAAU;AAClD,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AACxB,SAAK,6BAA6B;AAClC,SAAK,UAAU;AACf,SAAK,kBAAkB,cAAc;AACrC,eAAW,MAAM;AACf,WAAK,cAAc;AACnB,WAAK,kBAAkB,cAAc;AACrC,WAAK,YAAY,KAAK;AAAA,IACxB,GAAG,KAAK,qBAAqB,CAAC;AAAA,EAChC;AAAA,EACA,sBAAsB;AACpB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,yBAAyB;AACvB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa;AACX,SAAK,UAAU,KAAK;AAAA,EACtB;AAAA,EACA,YAAY;AACV,QAAI,KAAK,kBAAkB,KAAK,QAAQ;AACtC,WAAK,UAAU,KAAK;AAAA,IACtB;AAAA,EACF;AAAA,EACA,oBAAoB;AAClB,SAAK,iBAAiB,QAAQ;AAC9B,QAAI,KAAK,qBAAqB,MAAM;AAClC,YAAM,gBAAgB,SAAS,OAAO;AAAA,QACpC,QAAQ,KAAK;AAAA,QACb,WAAW,CAAC;AAAA,UACV,SAAS;AAAA,UACT,UAAU;AAAA,QACZ,GAAG;AAAA,UACD,SAAS;AAAA,UACT,UAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,CAAC;AACD,YAAM,kBAAkB,IAAI,gBAAgB,KAAK,WAAW,MAAM,aAAa;AAC/E,WAAK,eAAe,KAAK,iBAAiB,sBAAsB,eAAe;AAC/E,WAAK,oBAAoB,KAAK,aAAa;AAI3C,aAAO,OAAO,KAAK,aAAa,UAAU,KAAK,UAAU,KAAK,eAAe;AAC7E,WAAK,aAAa,kBAAkB,cAAc;AAAA,IACpD;AAAA,EACF;AAAA,EACA,gBAAgB;AACd,QAAI,CAAC,KAAK,YAAY;AACpB,WAAK,SAAS,IAAI,eAAe,KAAK,gBAAgB,KAAK,gBAAgB;AAC3E,WAAK,aAAa,KAAK,QAAQ,OAAO,KAAK,iBAAiB,CAAC;AAC7D,0BAAoB,KAAK,YAAY,KAAK,QAAQ;AAAA,IACpD;AACA,QAAI,KAAK,cAAc,CAAC,KAAK,WAAW,YAAY,GAAG;AACrD,WAAK,WAAW,OAAO,KAAK,MAAM;AAClC,WAAK,WAAW,cAAc,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,WAAS;AAChF,YAAI,MAAM,YAAY,UAAU,KAAK,UAAU,KAAK,YAAY;AAC9D,eAAK,UAAU,KAAK;AAAA,QACtB;AAAA,MACF,CAAC;AACD,WAAK,WAAW,YAAY,EAAE,KAAK,UAAU,KAAK,QAAQ,CAAC,EAAE,UAAU,MAAM;AAC3E,aAAK,eAAe;AAAA,MACtB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,iBAAiB;AACf,SAAK,YAAY,QAAQ;AACzB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,mBAAmB;AACjB,WAAO,IAAI,cAAc;AAAA,MACvB,qBAAqB,KAAK;AAAA,MAC1B,kBAAkB,KAAK,QAAQ,SAAS,EAAE,OAAO;AAAA,MACjD,gBAAgB,KAAK,QAAQ,iBAAiB,MAAM;AAAA,IACtD,CAAC;AAAA,EACH;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,cAAc,KAAK,WAAW,gBAAgB;AACrD,WAAK,SAAS,SAAS,KAAK,WAAW,gBAAgB,kBAAkB,KAAK,SAAS,SAAS,MAAM;AAAA,IACxG;AAAA,EACF;AAAA,EACA,qBAAqB;AACnB,QAAI,KAAK,YAAY;AACnB,UAAI,KAAK,QAAQ;AACf,aAAK,WAAW,UAAU,EAAE,eAAe,OAAO;AAAA,MACpD,OAAO;AACL,aAAK,WAAW,UAAU,EAAE,eAAe,QAAQ;AAAA,MACrD;AAAA,IACF;AAAA,EACF;AAAA,EACA,+BAA+B;AAC7B,QAAI,KAAK,YAAY,CAAC,KAAK,0BAA0B;AACnD,WAAK,2BAA2B,KAAK,SAAS;AAE9C,UAAI,KAAK,4BAA4B,OAAO,KAAK,yBAAyB,SAAS,YAAY;AAC7F,aAAK,yBAAyB,KAAK;AAAA,MACrC;AAAA,IACF;AAAA,EACF;AAAA,EACA,YAAY;AACV,QAAI,CAAC,KAAK,aAAa,KAAK,cAAc,KAAK,WAAW,gBAAgB;AACxE,WAAK,YAAY,KAAK,iBAAiB,OAAO,KAAK,WAAW,cAAc;AAC5E,WAAK,UAAU,oBAAoB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,eAAe;AAEb,QAAI,KAAK,4BAA4B,OAAO,KAAK,yBAAyB,UAAU,YAAY;AAC9F,WAAK,yBAAyB,MAAM;AAAA,IACtC;AACA,QAAI,KAAK,WAAW;AAClB,WAAK,UAAU,QAAQ;AAAA,IACzB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,GAAG;AAChD,aAAO,KAAK,KAAK,oBAAsB,kBAAqB,iBAAiB,GAAM,kBAAkB,UAAU,CAAC,GAAM,kBAAqB,eAAe,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,OAAO,GAAM,kBAAqB,QAAQ,GAAM,kBAAqB,iBAAiB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,yBAAyB,GAAM,kBAAqB,gBAAgB,CAAC,CAAC;AAAA,IACxe;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,WAAW,CAAC;AAAA,MACzB,gBAAgB,SAAS,iCAAiC,IAAI,KAAK,UAAU;AAC3E,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,UAAU,0BAA0B,GAAG,WAAW;AAAA,QACtE;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,0BAA0B,GAAG;AAAA,QAChF;AAAA,MACF;AAAA,MACA,WAAW,SAAS,wBAAwB,IAAI,KAAK;AACnD,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AACrB,UAAG,YAAY,iBAAiB,CAAC;AAAA,QACnC;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,iBAAiB,GAAG;AACrE,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,mBAAmB,GAAG;AAAA,QACzE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,QACX,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,gBAAgB;AAAA,QAChB,QAAQ;AAAA,QACR,qBAAqB;AAAA,QACrB,eAAe;AAAA,QACf,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,UAAU;AAAA,QACV,aAAa;AAAA,QACb,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,aAAa;AAAA,QACb,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,UAAU;AAAA,QACV,WAAW;AAAA,QACX,WAAW;AAAA,QACX,WAAW;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACP,cAAc;AAAA,QACd,WAAW;AAAA,QACX,iBAAiB;AAAA,MACnB;AAAA,MACA,UAAU,CAAC,UAAU;AAAA,MACrB,YAAY;AAAA,MACZ,UAAU,CAAI,4BAA+B,sBAAyB,mBAAmB;AAAA,MACzF,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,kBAAkB,EAAE,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe,GAAG,CAAC,GAAG,mBAAmB,GAAG,SAAS,GAAG,CAAC,GAAG,oBAAoB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,GAAG,qBAAqB,GAAG,8BAA8B,GAAG,CAAC,GAAG,mBAAmB,GAAG,SAAS,GAAG,CAAC,mBAAmB,EAAE,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,mBAAmB,GAAG,SAAS,SAAS,GAAG,CAAC,GAAG,mBAAmB,GAAG,CAAC,GAAG,yBAAyB,GAAG,CAAC,cAAc,SAAS,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,GAAG,kBAAkB,GAAG,CAAC,cAAc,SAAS,GAAG,oBAAoB,GAAG,OAAO,GAAG,CAAC,GAAG,wBAAwB,GAAG,CAAC,WAAW,IAAI,GAAG,QAAQ,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,GAAG,oBAAoB,yBAAyB,GAAG,CAAC,GAAG,kBAAkB,CAAC;AAAA,MAChuB,UAAU,SAAS,2BAA2B,IAAI,KAAK;AACrD,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,GAAG,0CAA0C,IAAI,IAAI,eAAe,MAAM,GAAM,sBAAsB;AAAA,QACtH;AAAA,MACF;AAAA,MACA,cAAc,CAAC,wBAAwB,SAAS,gBAAmB,iCAAiC,cAAiB,iBAAiB,cAAiB,iBAAiB,gBAAgB;AAAA,MACxL,eAAe;AAAA,MACf,MAAM;AAAA,QACJ,WAAW,CAAC,gBAAgB;AAAA,MAC9B;AAAA,MACA,iBAAiB;AAAA,IACnB,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,kBAAkB,WAAW,kBAAkB,MAAM;AAChG,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,kBAAkB,WAAW,UAAU,MAAM;AACxF,WAAW,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,kBAAkB,WAAW,uBAAuB,MAAM;AACrG,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,iBAAiB,MAAM;AACjF,WAAW,CAAC,aAAa,CAAC,GAAG,kBAAkB,WAAW,cAAc,MAAM;AAC9E,WAAW,CAAC,WAAW,CAAC,GAAG,kBAAkB,WAAW,eAAe,MAAM;AAAA,CAC5E,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,UAAU;AAAA,MACV,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAiFV,qBAAqB;AAAA,MACrB,iBAAiB,wBAAwB;AAAA,MACzC,YAAY,CAAC,gBAAgB;AAAA,MAC7B,SAAS,CAAC,wBAAwB,SAAS,gBAAgB,cAAc,cAAc,gBAAgB;AAAA,MACvG,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,IACT,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,qBAAqB,CAAC;AAAA,MACpB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,eAAe,CAAC;AAAA,MACd,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,IACD,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,gBAAgB,CAAC;AAAA,MACf,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,QACvB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,kBAAkB,CAAC;AAAA,MACjB,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,QACtB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,CAAC;AAAA,IACD,yBAAyB,CAAC;AAAA,MACxB,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,QAC/B,QAAQ;AAAA,QACR,MAAM;AAAA,MACR,CAAC;AAAA,IACH,CAAC;AAAA,IACD,aAAa,CAAC;AAAA,EAChB,CAAC;AACH,GAAG;AACH,IAAM,0BAAN,MAA8B;AAAA,EAC5B,YAAY,SAAS,SAAS;AAC5B,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,eAAe,IAAI,QAAQ;AAEhC,UAGI,UAAK,SAFP;AAAA;AAAA,IAp5BN,IAs5BQ,IADC,4BACD,IADC;AAAA,MADH;AAAA;AAGF,SAAK,aAAa,KAAK,QAAQ,OAAO;AACtC,SAAK,YAAY,KAAK,WAAW,OAAO,IAAI,gBAAgB,iBAAiB,CAAC,EAAE;AAChF,SAAK,cAAc,eAAe;AAElC,SAAK,UAAU,6BAA6B;AAC5C,SAAK,UAAU,aAAa,KAAK,UAAU,KAAK,YAAY,CAAC,EAAE,UAAU,MAAM;AAC7E,WAAK,UAAU,KAAK;AAAA,IACtB,CAAC;AACD,SAAK,UAAU,UAAU,UAAU,MAAM;AACvC,UAAI,YAAY;AACd,mBAAW,EAAE,KAAK,cAAY;AAC5B,cAAI,aAAa,OAAO;AACtB,iBAAK,UAAU,MAAM;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH,OAAO;AACL,aAAK,UAAU,MAAM;AAAA,MACvB;AAAA,IACF,CAAC;AACD,SAAK,UAAU,WAAW,KAAK,UAAU,KAAK,YAAY,CAAC,EAAE,UAAU,MAAM;AAC3E,WAAK,WAAW,QAAQ;AACxB,WAAK,YAAY;AACjB,WAAK,aAAa,KAAK;AACvB,WAAK,aAAa,SAAS;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,cAAc;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,SAAS;AACrB,WAAO,OAAO,KAAK,WAAW,OAAO;AAAA,EACvC;AACF;AACA,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,YAAY,SAAS;AACnB,SAAK,UAAU;AAAA,EACjB;AAAA,EACA,OAAO,SAAS;AACd,WAAO,IAAI,wBAAwB,KAAK,SAAS,OAAO,EAAE,YAAY;AAAA,EACxE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,GAAG;AAC9C,aAAO,KAAK,KAAK,kBAAoB,SAAY,OAAO,CAAC;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,iBAAgB;AAAA,IAC3B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AAMH,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,GAAG;AAC7C,aAAO,KAAK,KAAK,iBAAgB;AAAA,IACnC;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,mBAAmB,wBAAwB;AAAA,MACrD,SAAS,CAAC,mBAAmB,wBAAwB;AAAA,IACvD,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC,eAAe;AAAA,MAC3B,SAAS,CAAC,iBAAiB;AAAA,IAC7B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,mBAAmB,wBAAwB;AAAA,MACrD,WAAW,CAAC,eAAe;AAAA,MAC3B,SAAS,CAAC,mBAAmB,wBAAwB;AAAA,IACvD,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}