<div class="flex flex-col bg-[var(--background-light-gray)]" style="height: calc(100vh - 65px);">
  <!-- Main Container -->
  <div class="flex-1 overflow-hidden flex flex-col">
    <!-- Header - Teams-style with compact vertical spacing -->
    <div
      class="sticky-header flex flex-row justify-between items-center pt-3 px-4 bg-[var(--background-light-gray)] border-b border-[var(--hover-blue-gray)] border-opacity-50">
      <!-- Left side with title and count -->
      <div class="flex items-center gap-2">
        <i class="ri-file-list-3-line text-[var(--primary-purple)] text-xl"></i>
        <h1 class="text-lg font-medium text-[var(--text-dark)]">AI Prompts</h1>
        <div
          class="inline-flex items-center justify-center px-1.5 py-0.5 rounded-md text-xs font-medium bg-[var(--hover-blue-gray)] text-[var(--text-dark)]">
          {{ filteredPrompts.length }}
        </div>
        <!-- <p class="text-xs text-gray-400 ml-2 hidden sm:block">Organize and manage your AI prompts</p> -->
      </div>

      <!-- Right side with controls -->
      <div class="flex items-center gap-2">
        <!-- Search Input - Teams-style -->
        <div class="relative w-full sm:w-56 flex items-center">
          <div class="absolute inset-y-0 left-0 flex items-center justify-center pl-2 pointer-events-none">
            <i class="ri-search-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
          <input type="text" placeholder="Search prompts..." [(ngModel)]="searchTerm" (ngModelChange)="filterPrompts()"
            class="w-full h-8 px-3 py-1 pl-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200" />
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2" *ngIf="searchTerm">
            <button (click)="clearSearch()"
              class="text-[var(--text-medium-gray)] bg-transparent hover:text-[var(--text-dark)] transition-colors focus:outline-none">
              <i class="ri-close-line text-sm"></i>
            </button>
          </div>
        </div>

        <!-- Filter Select - Teams-style -->
        <div class="relative w-auto flex items-center">
          <select [(ngModel)]="selectedFilter" (ngModelChange)="filterPrompts()"
            class="appearance-none w-auto h-8 px-3 py-1 pr-8 text-sm text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] rounded-md outline-none focus:ring-1 focus:ring-[var(--primary-purple)] transition-all duration-200 cursor-pointer">
            <option value="all">All Prompts</option>
            <option value="workspace">Workspace Prompts</option>
            <option value="agent">Agent Prompts</option>
          </select>
          <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-2 pointer-events-none">
            <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
          </div>
        </div>

        <!-- Add Button - Teams-style -->
        <button (click)="openAddEditModal()"
          class="h-8 px-3 py-1 bg-[var(--primary-purple)] text-white text-sm border-none font-medium rounded-md hover:bg-opacity-90 transition-all duration-200 flex items-center justify-center gap-1">
          <i class="ri-add-line"></i>
          <span>Add Prompt</span>
        </button>
      </div>
    </div>

    <!-- Prompts Table - With Proper Y-Axis Scrolling -->
    <div class="flex-1 overflow-y-auto px-4 pt-4">
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="relative min-h-[300px]">
        <app-spinner message="Loading prompts..." [overlay]="false"></app-spinner>
      </div>

      <div *ngIf="!isLoading"
        class="bg-[var(--background-white)] rounded-lg shadow-sm overflow-hidden border border-[var(--hover-blue-gray)] border-opacity-30">
        <div class="overflow-x-auto">
          <table class="w-full text-sm text-[var(--text-dark)] prompt-table">
            <!-- Table Header - Sticky -->
            <thead>
              <tr
                class="sticky top-0 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] font-medium text-sm border-b border-[var(--hover-blue-gray)] z-10">
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Short Message</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Prompt</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center">
                  <div class="flex items-center justify-center">
                    <span>Usage Count</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left" *ngIf="isAdmin">
                  <div class="flex items-center">
                    <span>User Email</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Workspace</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-left">
                  <div class="flex items-center">
                    <span>Agent</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center">
                  <div class="flex items-center justify-center">
                    <span>Type</span>
                  </div>
                </th>
                <th class="px-4 py-3 text-center">
                  <div class="flex items-center justify-center">
                    <span>Actions</span>
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let prompt of paginatedPrompts; let i = index"
                class="border-b border-[var(--hover-blue-gray)] hover:bg-[var(--background-light-gray)] "
                >
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span class="font-medium">{{ prompt.shortMessage }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div>{{ prompt.prompt }}</div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center justify-center">
                    <span
                      class="inline-flex items-center justify-center min-w-[30px] h-6 px-2 rounded-full bg-[var(--hover-blue-gray)] text-[var(--text-dark)] font-medium">
                      {{ prompt.usageCount }}
                    </span>
                  </div>
                </td>
                <td class="px-4 py-3" *ngIf="isAdmin">
                  <div class="flex items-center">
                    <span>{{ prompt.userEmail }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{ prompt.workspaceName || '-' }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center">
                    <span>{{ prompt.agentName || '-' }}</span>
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center justify-center">
                    @if (prompt.workspaceName) {
                    <span
                      class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--primary-purple)] text-white">
                      Workspace
                    </span>
                    } @else if (prompt.agentName) {
                    <span
                      class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-[var(--secondary-purple)] text-[var(--text-dark)]">
                      Agent
                    </span>
                    } @else {
                    <span
                      class="inline-flex items-center justify-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-400 text-white">
                      Unknown
                    </span>
                    }
                  </div>
                </td>
                <td class="px-4 py-3">
                  <div class="flex items-center justify-center space-x-3">
                    <button (click)="openAddEditModal(prompt)"
                      class="action-button w-8 h-8 rounded-md bg-[#F0F0F0] hover:bg-[#E0E0E0] transition-all duration-200 flex items-center justify-center border-none"
                      *ngIf="isAdmin || prompt.userEmail === authService.getUser().email" title="Edit Prompt">
                      <i class="ri-edit-line text-[var(--primary-purple)] text-base"></i>
                    </button>
                    <button (click)="deletePrompt(prompt)"
                      class="action-button w-8 h-8 rounded-md bg-[#FEE2E2] hover:bg-[#FECACA] transition-all duration-200 flex items-center justify-center border-none"
                      *ngIf="isAdmin || prompt.userEmail === authService.getUser().email" title="Delete Prompt">
                      <i class="ri-delete-bin-6-line text-red-500 text-base"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Empty State -->
        <div *ngIf="!isLoading && filteredPrompts.length === 0"
          class="flex flex-col items-center justify-center py-16 px-4">
          <div class="w-16 h-16 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mb-4">
            <i class="ri-file-list-3-line text-3xl text-[var(--text-medium-gray)]"></i>
          </div>
          <h3 class="text-lg font-medium text-[var(--text-dark)] mb-2">No prompts found</h3>
          <p class="text-[var(--text-medium-gray)] text-center max-w-md mb-6">
            No prompts match your current search criteria. Try adjusting your search or filter settings.
          </p>
          <button (click)="clearSearch()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 flex items-center gap-2">
            <i class="ri-refresh-line"></i>
            <span>Reset Filters</span>
          </button>
        </div>
      </div>

      <!-- Pagination Controls -->
      <div
        class="pagination-container flex flex-col sm:flex-row justify-between items-center mt-4 mb-4 px-4 py-3 bg-[var(--background-white)] rounded-md shadow-sm border border-[var(--hover-blue-gray)]"
        *ngIf="!isLoading && filteredPrompts.length > 0">
        <div class="text-sm text-[var(--text-medium-gray)] mb-4 sm:mb-0 flex items-center">
          <ng-container *ngIf="filteredPrompts.length > 0">
            <span>Showing</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ ((currentPage - 1) * pageSize) + 1 }}</span>
            <span>to</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ Math.min(currentPage * pageSize,
              filteredPrompts.length) }}</span>
            <span>of</span>
            <span class="font-medium text-[var(--text-dark)] mx-1">{{ filteredPrompts.length }}</span>
            <span>prompts</span>
          </ng-container>
          <ng-container *ngIf="filteredPrompts.length === 0">
            <span>No prompts to display</span>
          </ng-container>
        </div>

        <div class="flex items-center">
          <div class="hidden sm:flex items-center mr-6 space-x-2">
            <span class="text-sm text-[var(--text-medium-gray)]">Rows per page:</span>
            <div class="relative">
              <select [(ngModel)]="pageSize" (ngModelChange)="updatePagination()"
                class="appearance-none h-7 bg-[var(--background-white)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-md text-sm px-2 pr-7 py-0 text-center focus:outline-none focus:ring-1 focus:ring-[var(--primary-purple)]">
                <option [ngValue]="5" class="text-center">5</option>
                <option [ngValue]="10" class="text-center">10</option>
                <option [ngValue]="20" class="text-center">20</option>
                <option [ngValue]="50" class="text-center">50</option>
              </select>
              <div class="absolute inset-y-0 right-0 flex items-center justify-center pr-1 pointer-events-none">
                <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)] text-sm"></i>
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-1" *ngIf="totalPages > 0">
            <button (click)="goToPage(1)" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="First page">
              <i class="ri-skip-back-mini-line text-sm"></i>
            </button>

            <button (click)="previousPage()" [disabled]="currentPage === 1 || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Previous page">
              <i class="ri-arrow-left-s-line text-sm"></i>
            </button>

            <div class="flex items-center space-x-1">
              <button *ngIf="currentPage > 2 && totalPages > 3" (click)="goToPage(1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                1
              </button>

              <span *ngIf="currentPage > 3 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage > 1 && totalPages > 1" (click)="goToPage(currentPage - 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage - 1 }}
              </button>

              <button
                class="w-7 h-7 flex items-center justify-center rounded-md bg-[var(--primary-purple)] text-white font-medium border-none">
                {{ currentPage }}
              </button>

              <button *ngIf="currentPage < totalPages && totalPages > 1" (click)="goToPage(currentPage + 1)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ currentPage + 1 }}
              </button>

              <span *ngIf="currentPage < totalPages - 2 && totalPages > 4"
                class="w-7 h-7 flex items-center justify-center text-[var(--text-medium-gray)]">...</span>

              <button *ngIf="currentPage < totalPages - 1 && totalPages > 3" (click)="goToPage(totalPages)"
                class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200">
                {{ totalPages }}
              </button>
            </div>

            <button (click)="nextPage()" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Next page">
              <i class="ri-arrow-right-s-line text-sm"></i>
            </button>

            <button (click)="goToPage(totalPages)" [disabled]="currentPage === totalPages || totalPages <= 1"
              class="w-7 h-7 flex items-center justify-center rounded-md text-[var(--text-dark)] bg-[var(--background-white)] border border-[var(--hover-blue-gray)] disabled:opacity-50 disabled:cursor-not-allowed hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
              aria-label="Last page">
              <i class="ri-skip-forward-mini-line text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
