﻿using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ProjectApp.Core.Repositiories;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Repositories;
using ProjectApp.Infrastructure.Services;
using System.Data;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Memory;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Microsoft.KernelMemory;
using Microsoft.KernelMemory.MemoryDb.SQLServer;
using Microsoft.SemanticKernel.Services;
using Microsoft.Extensions.Options;
using ProjectApp.Core.Models;
using Microsoft.SemanticKernel.ChatCompletion;
using Hangfire;
using ProjectApp.Infrastructure.AIAgents;
using ProjectApp.Infrastructure.AIAgents.Tools;
using WebApi.Infrastructure.Repositories;
using ProjectApp.Core.Dtos;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using ProjectApp.Infrastructure.AIServices;
using OpenTelemetry;
using OpenTelemetry.Resources;
using OpenTelemetry.Trace;
using OpenTelemetry.Metrics;
using OpenTelemetry.Logs;

namespace ProjectApp.Infrastructure
{
    public static class InfrastructureServiceRegistration
    {
        public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Setup OpenTelemetry
            SetupOpenTelemetry(services);

            var connectionString = configuration.GetConnectionString("AdminConnection");

            // Register IDbConnection for Dapper
            services.AddScoped<IDbConnection>(provider => new SqlConnection(connectionString));

            // JWT Configuration
            var jwtSettings = configuration.GetSection("Jwt");
            services.Configure<JwtSettings>(jwtSettings);

            var key = Encoding.ASCII.GetBytes(jwtSettings["Key"]);

            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.RequireHttpsMetadata = false;
                options.SaveToken = true;
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidateAudience = true,
                    ValidIssuer = jwtSettings["Issuer"],
                    ValidAudience = jwtSettings["Audience"],
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero
                };
            });

            // Register HttpContextAccessor
            services.AddHttpContextAccessor();

            // Add repository services
            services.AddScoped<IExtractEmailFromAccessor, EmailExtractFromAccessor>();
            services.AddScoped<IUserAccountRepository, UserAccountRepository>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IProjectRepository, ProjectRepository>();
            services.AddScoped<ICommentRepository, CommentRepository>();
            services.AddScoped<IWorkspaceRepository, WorkspaceRepository>();
            services.AddScoped<IAssignWorkspaceRepository, AssignWorkspaceRepository>();
            services.AddScoped<IDocsRepository, DocsRepository>();
            services.AddScoped<ITaskRepository, TaskRespository>();
            services.AddScoped<IProjectCategoryRepository, ProjectCategoryRepository>();
            services.AddScoped<IMemoryRepository, MemoryRepository>();

            // Register AIAgentFactory with logger and memory cache
            services.AddScoped<AIAgentFactory>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<AIAgentFactory>>();
                var memoryCache = sp.GetRequiredService<IMemoryCache>();
                return new AIAgentFactory(
                    sp,
                    sp.GetRequiredService<IModelDetailsRepository>(),
                    sp.GetRequiredService<IApiCredentialsRepository>(),
                    sp.GetRequiredService<IAgentDefinitionRepository>(),
                    sp.GetRequiredService<IKernelMemory>(),
                    sp.GetRequiredService<IPluginRepository>(),
                    memoryCache,
                    logger
                );
            });
            services.AddScoped<IAgentDefinitionRepository, AgentDefinitionRepository>();
            services.AddScoped<IProjectMemoryRepository, ProjectMemoryRepository>();
            services.AddScoped<IFileRepository, FileRepository>();
            services.AddScoped<IChatHistoryRepository, ChatHistoryRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<IAgentLogRepository, AgentLogRepository>();
            services.AddScoped<IApiCredentialsRepository, ApiCredentialsRepository>();
            services.AddScoped<IChatModelRepository, ChatModelRepository>();
            services.AddScoped<IAgentEvaluationRepository, AgentEvaluationRepository>();
            services.AddScoped<AgentEvaluationService>();
            services.AddScoped<IChatRepository, ChatRepository>();
            services.AddScoped<IModelDetailsRepository, ModelDetailsRepository>();
            services.AddScoped<IUrlService, UrlService>();
            services.AddScoped<IPromptLibraryRepository, PromptLibraryRepository>();
            services.AddScoped<IPluginRepository, PluginRepository>();
            services.AddScoped<PluginScannerService>();
            services.AddScoped<OpenApiPluginService>();
            services.AddScoped<BlogService>(); services.AddScoped<IChatSourceService, ChatSourceService>();
            services.AddScoped<ISqlConnectionRepository, SqlConnectionRepository>();
            services.AddScoped<IAgentChatHistoryRepository, AgentChatHistoryRepository>();
            services.AddScoped<AgentOrchestratorService>();            // Add Daily Insight services
            services.AddScoped<IDailyInsightAgentRepository, DailyInsightAgentRepository>();

            // Add Database Agent services
            services.AddScoped<IDatabaseConnectionRepository, DatabaseConnectionRepository>();
            services.AddScoped<DatabaseSchemaService>();

            // Add Database Agent plugins
            services.AddScoped<DatabaseSqlGeneratorPlugin>();
            services.AddScoped<SqlExecutorPlugin>();
            services.AddScoped<DailyInsightService>();            // Add Email Receiver services
            services.Configure<EmailReceiverSettings>(configuration.GetSection("EmailReceiver"));
            services.AddScoped<EmailProcessingService>();
            services.AddScoped<EmailProcessingJob>();
            services.AddScoped<IEmailReceiverService, ImapEmailReceiverService>();
            services.AddScoped<IEmailProcessingResultRepository, EmailProcessingResultRepository>();
            services.AddScoped<IUserFavoritesRepository, UserFavoritesRepository>();

            // Retrieve the OpenAI API settings from configuration
            var openAIConfig = configuration.GetSection("OpenAI");
            //services.AddOpenAIChatCompletion(openAIConfig["ModelId"], openAIConfig["ApiKey"]);

#pragma warning disable
            // Register Semantic Kernel
            services.AddTransient<Kernel>(sp =>
            {
                var options = sp.GetRequiredService<IOptions<OpenAIOptions>>().Value;
                var kernel = Kernel.CreateBuilder()
                    .AddOpenAIChatCompletion(options.ChatModelId, options.ApiKey)
                    //.AddOpenAIChatCompletion("mistral-nemo", new Uri("https://localai.3dbotics.com/v1"), "")
                    //.AddOpenAITextEmbeddingGeneration(openAIConfig["EmbeddingModelId"], openAIConfig["ApiKey"])
                    .Build();

                return kernel;
            });

            // Chat completion service that kernels will use
            services.AddSingleton<IChatCompletionService>(sp =>
            {
                OpenAIOptions options = sp.GetRequiredService<IOptions<OpenAIOptions>>().Value;

                return new OpenAIChatCompletionService(options.ChatModelId, options.ApiKey);
            });



            // Add a static configuration provider
            services.AddSingleton<EmbeddingConfigurationProvider>(sp =>
            {
                using var scope = sp.CreateScope();
                var embeddingConfigRepository = scope.ServiceProvider.GetRequiredService<IModelDetailsRepository>();
                var options = scope.ServiceProvider.GetRequiredService<IOptions<OpenAIOptions>>().Value;

                var embeddingConfig = embeddingConfigRepository.GetActiveEmbeddingConfigAsync().GetAwaiter().GetResult();

                var modelName = embeddingConfig?.ModelName;
                modelName = modelName[(modelName.IndexOf('_') + 1)..] ?? options.EmbeddingModelId;
                var apiKey = embeddingConfig?.APIKey ?? options.ApiKey;

                return new EmbeddingConfigurationProvider
                {
                    EmbeddingModelId = modelName,
                    ApiKey = apiKey
                };
            });


            // Keep the repository as scoped
            //services.AddScoped<IEmbeddingConfigRepository, EmbeddingConfigRepository>();

            // Then modify your IKernelMemory registration to use the provider
            services.AddSingleton<IKernelMemory>(sp =>
            {
                var configProvider = sp.GetRequiredService<EmbeddingConfigurationProvider>();
                var options = sp.GetRequiredService<IOptions<OpenAIOptions>>().Value;

                IKernelMemoryBuilder kmBuilder = new KernelMemoryBuilder();

                var memory = kmBuilder.WithOpenAITextEmbeddingGeneration(new OpenAIConfig
                {
                    EmbeddingModel = configProvider.EmbeddingModelId,
                    APIKey = configProvider.ApiKey
                })
                .WithOpenAITextGeneration(new OpenAIConfig
                {
                    TextModel = options.ChatModelId,
                    APIKey = options.ApiKey
                })
                .WithSqlServerMemoryDb(new SqlServerConfig
                {
                    ConnectionString = connectionString,
                    EmbeddingsTableName = "KM_Embedding",
                    MemoryCollectionTableName = "KM_Collection",
                    MemoryTableName = "KM_Memory",
                    Schema = "dbo",
                    TagsTableName = "KM_Tag",
                })
                .Build<MemoryServerless>(new KernelMemoryBuilderBuildOptions { AllowMixingVolatileAndPersistentData = true });

                return memory;
            });

            services.AddSingleton<LightsPlugin>();
            services.AddTransient<WorkspaceCategoryPlugin>();
            services.AddTransient<CustomerPlugin>();
            services.AddTransient<KernelMemoryPlugin>();
            services.AddTransient<UserSkillMatchPlugin>();
            services.AddTransient<DocumentPlugin>();
            services.AddTransient<FileDescriptionPlugin>();
            services.AddTransient<DatabaseSqlGeneratorPlugin>();
            services.AddTransient<TestFileGeneratorPlugin>();
            services.AddTransient<FileTextExtractionPlugin>();
            services.AddTransient<InvoiceProcessingPlugin>();
            services.AddTransient<ProjectMemoryPlugin>();

            services.AddScoped<AIService>();

            // Add Email Settings
            services.Configure<EmailSettings>(configuration.GetSection("EmailSettings"));
            services.AddScoped<EmailService>();

            // Configure Hangfire jobs
            services.AddHangfire(config =>
            {
                config.UseSqlServerStorage(configuration.GetConnectionString("AdminConnection"));
            });

            services.AddHangfireServer();

            services.AddHttpClient();

            services.AddScoped<ProjectBackgroundService>();

            return services;
        }

        private static void SetupOpenTelemetry(IServiceCollection services)
        {
            // Create a resource builder for the service
            var resourceBuilder = ResourceBuilder
                .CreateDefault()
                .AddService("ProjectApp.Infrastructure");

            // Enable model diagnostics with sensitive data
            AppContext.SetSwitch("Microsoft.SemanticKernel.Experimental.GenAI.EnableOTelDiagnosticsSensitive", true);

            // Add OpenTelemetry tracer provider
            services.AddOpenTelemetry()
                .WithTracing(builder => builder
                    .SetResourceBuilder(resourceBuilder)
                    .AddSource("Microsoft.SemanticKernel*")
                    .AddConsoleExporter())
                .WithMetrics(builder => builder
                    .SetResourceBuilder(resourceBuilder)
                    .AddMeter("Microsoft.SemanticKernel*")
                    .AddConsoleExporter());

            // Configure logging
            services.AddLogging(builder =>
            {
                builder.AddOpenTelemetry(options =>
                {
                    options.SetResourceBuilder(resourceBuilder);
                    options.AddConsoleExporter();
                    options.IncludeFormattedMessage = true;
                    options.IncludeScopes = true;
                });
            });
        }
    }
}
