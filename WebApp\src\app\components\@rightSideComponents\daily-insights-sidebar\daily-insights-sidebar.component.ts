import { Component, inject, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TogglingService } from '../../../toggling.service';
import { ChatListService } from '../../../services/chat-list.service';
import { NzMessageService } from 'ng-zorro-antd/message';

@Component({
  selector: 'app-daily-insights-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './daily-insights-sidebar.component.html',
  styleUrls: ['./daily-insights-sidebar.component.css']
})
export class DailyInsightsSidebarComponent {
  // Inject services
  router = inject(Router);
  togglingService = inject(TogglingService);
  chatListService = inject(ChatListService);
  nzMessageService = inject(NzMessageService);

  // Input properties
  @Input() workspaceName: string = '';

  // Properties for date/time display
  lastClickedItem: string = '';
  lastClickedTime: string = '';

  /**
   * Update last clicked item and time
   */
  private updateLastClicked(itemName: string) {
    this.lastClickedItem = itemName;
    this.lastClickedTime = new Date().toLocaleString();
  }

  /**
   * Navigate to chat page
   */
  goToChat() {
    // Update last clicked item
    this.updateLastClicked('New Chat');

    // Reset chatId to 0 for new chat
    this.chatListService.chatId = 0;

    // Navigate based on workspace context
    if (this.workspaceName) {
      this.router.navigate(['workspaces', this.workspaceName, 'chat']);
    } else {
      this.router.navigate(['/chat']);
    }

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to workspaces page
   */
  goToWorkspace() {
    // Update last clicked item
    this.updateLastClicked('Workspaces');

    // Reset chatId
    this.chatListService.chatId = 0;

    // Navigate to workspaces
    this.router.navigate(['/workspaces']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to prompt library page
   */
  goToPromptLibrary() {
    // Update last clicked item
    this.updateLastClicked('Prompt Library');

    // Navigate to prompt library
    this.router.navigate(['/admin', 'prompt-library']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to project summary page
   */
  goToProjectSummary() {
    // Update last clicked item
    this.updateLastClicked('Project Summary');

    // Reset chatId
    this.chatListService.chatId = 0;

    // Navigate to project summary
    this.router.navigate(['/project-summary']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Navigate to task management page
   */
  goToTaskManagement() {
    // Update last clicked item
    this.updateLastClicked('Task Management');

    // Reset chatId
    this.chatListService.chatId = 0;

    // Navigate to task management
    this.router.navigate(['/task-management']);

    // Close sidebar after navigation
    if (this.togglingService.isNavbarOpen) {
      this.togglingService.toggleNavbar();
    }
  }

  /**
   * Refresh daily insights data
   */
  // refreshDailyInsights() {
  //   // Show loading indicator
  //   this.nzMessageService.loading('Refreshing daily insights...');

  //   // Show success message after a short delay (simulating refresh)
  //   // setTimeout(() => {
  //   //   this.nzMessageService.success('Daily insights refreshed');
  //   // }, 1000);
  // }
}
