import { Routes } from '@angular/router';
import { HeroComponent } from './pages/hero/hero.component';
import { AdminComponent } from './admin/admin.component';
import { LoginComponent } from './pages/login/login.component';
import { RegisterComponent } from './pages/register/register.component';
import { authGuard } from '../shared/services/auth.guard';
import { AiSettingsComponent } from './ai-settings/ai-settings.component';
import { WorkspacesComponent } from './workspaces/workspaces.component';
import { ViewWorkspaceComponent } from './workspaces/view-workspace/view-workspace.component';
import { ProjectMemoryComponent } from './workspaces/project-memory/project-memory.component';
import { DocumentsComponent } from './workspaces/documents/documents.component';
import { AgentsComponent } from './workspaces/agents/agents.component';
import { WorkspaceChatComponent } from './workspaces/workspace-chat/workspace-chat.component';
import { AddOrEditDocumentComponent } from './workspaces/documents/add-or-edit-document/add-or-edit-document.component';
import { DocumentListComponent } from './MyNotesProjects/document-list/document-list.component';
import { DocumentDetailsComponent } from './MyNotesProjects/document-details/document-details.component';
import { EditorComponent } from './MyNotesProjects/editor/editor.component';
import { AddOrEditAgentsComponent } from './workspaces/agents/add-or-edit-agents/add-or-edit-agents.component';
import { UsersManagementComponent } from './admin/user-management/user-management.component';
import { PromptsLibraryComponent } from './admin/prompts-library/prompts-library.component';
import { ModelsComponent } from './admin/models/models.component';
import { ConnectionsComponent } from './admin/conection/conection.component';
import { NotFoundComponent } from './pages/not-found/not-found.component';
import { DailyInsightComponent } from './pages/daily-insight/daily-insight.component';
import { PluginDetailsComponent } from './admin/plugins/plugin-details/plugin-details.component';
import { PluginsComponent } from './admin/plugins/plugins.component';

import { FileComponent } from './admin/settings/Files/file/file.component';
import { FileAddComponent } from './admin/settings/Files/file-add/file-add.component';
import { MemoryComponent } from './admin/Notes/memory.component';
import { AddOrEditMemoryComponent } from './admin/Notes/add-or-edit-memory/add-or-edit-memory.component';
import { AgentChatComponent } from './pages/agent-chat/agent-chat.component';
import { TestingComponent } from './testing/testing.component';
import { AgentEvaluationComponent } from './admin/evaluations/agent-evaluation.component';
import { RequestListComponent } from './my-request/request-list/request-list.component';
import { AddRequestComponent } from './my-request/add-request/add-request.component';
import { RequestDetailComponent } from './my-request/request-detail/request-detail.component';
import { ProjectSummaryComponent } from './pages/project-summary/project-summary.component';
import { TaskManagementComponent } from './pages/task-management/task-management.component';
// import { DatabaseConnectionsComponent } from './admin/database-connections/database-connections.component';

export const routes: Routes = [
  // Set Daily Insight as the default landing page
  { path: '', component: DailyInsightComponent, canActivate: [authGuard] },

  // Update chat routes to use 'chat' instead of 'home'
  // { path: 'chat', component: HeroComponent, canActivate: [authGuard] },
  // { path: 'chat/:id', component: HeroComponent, canActivate: [authGuard] },
  // agent chat
  { path: 'chat', component: TestingComponent },
  { path: 'chat/:type/:name', component: AgentChatComponent },

  {
    path: 'chat/:type/:name/project-memory',
    component: ProjectMemoryComponent,
  },
  {
    path: 'chat/:type/:name/documents',
    component: DocumentsComponent,
    canActivate: [authGuard],
  },
  {
    path: 'chat/:type/:name/documents/add-edit/:DocId',
    component: AddOrEditDocumentComponent,
    canActivate: [authGuard],
  },
  {
    path: 'chat/:type/:name/agents',
    component: AgentsComponent,
    canActivate: [authGuard],
  },
  {
    path: 'chat/:type/:name/agents/:agentName',
    component: AddOrEditAgentsComponent,
    canActivate: [authGuard],
  },

  // { path: 'agent-chat/:name', component: AgentChatComponent },
  // { path: 'workspace-chats/:name', component: AgentChatComponent },

  { path: 'notes', component: DocumentsComponent, canActivate: [authGuard] },
  {
    path: 'notes/add-edit/:DocId',
    component: AddOrEditDocumentComponent,
    canActivate: [authGuard],
  },

  // {
  //   path: 'settings',
  //   component: AiSettingsComponent, canActivate: [authGuard]
  // },

  {
    path: 'settings',
    component: AdminComponent,
    canActivate: [authGuard],
    children: [
      {
        path: 'user-management',
        component: UsersManagementComponent,
        canActivate: [authGuard],
      },
      {
        path: 'connections',
        component: ConnectionsComponent,
        canActivate: [authGuard],
      },
      {
        path: 'database-connections',
        loadComponent: () =>
          import(
            './admin/database-connections/database-connections.component'
          ).then((c) => c.DatabaseConnectionsComponent),
        canActivate: [authGuard],
      },
      {
        path: 'models',
        component: ModelsComponent,
        canActivate: [authGuard],
      },
      { path: 'agents', component: AgentsComponent, canActivate: [authGuard] },
      {
        path: 'agents/:agentName',
        component: AddOrEditAgentsComponent,
        canActivate: [authGuard],
      },
      {
        path: 'agent-analytics',
        loadComponent: () =>
          import('./admin/agent-analytics/agent-analytics.component').then(
            (c) => c.AgentAnalyticsComponent
          ),
        canActivate: [authGuard],
      },
      {
        path: 'agent-analytics/details/:agentId',
        loadComponent: () =>
          import(
            './admin/agent-analytics/agent-details/agent-details.component'
          ).then((c) => c.AgentDetailsComponent),
        canActivate: [authGuard],
      },
      {
        path: 'prompt-library',
        component: PromptsLibraryComponent,
        canActivate: [authGuard],
      },
      {
        path: 'plugins',
        component: PluginsComponent,
        canActivate: [authGuard],
      },
      {
        path: 'plugins/:pluginName',
        component: PluginDetailsComponent,
        canActivate: [authGuard],
      },
      {
        path: 'memory',
        component: MemoryComponent,
        canActivate: [authGuard],
      },
      {
        path: 'memory/:id',
        component: AddOrEditMemoryComponent,
        canActivate: [authGuard],
      },
      {
        path: 'files',
        component: FileComponent,
        canActivate: [authGuard],
      },
      {
        path: 'files/:id',
        component: FileAddComponent,
        canActivate: [authGuard],
      },
      {
        path: 'evaluations',
        component: AgentEvaluationComponent,
        canActivate: [authGuard],
      },
      {
        path: 'workspace',
        component: WorkspacesComponent,
        canActivate: [authGuard],
      },

      {
        path: '',
        redirectTo: 'prompt-library',
        pathMatch: 'full',
      },
    ],
  },
  // { path: 'admin/:adminTab/add-edit/:agentName', component: AdminComponent, canActivate: [authGuard] },
  {
    path: 'workspaces',
    component: WorkspacesComponent,
    canActivate: [authGuard],
  },
  {
    path: 'workspaces/:workspaceName',
    component: ViewWorkspaceComponent,
    children: [
      { path: 'chat', component: HeroComponent, canActivate: [authGuard] },
      { path: 'chat/:id', component: HeroComponent, canActivate: [authGuard] },
      {
        path: 'project-memory',
        component: ProjectMemoryComponent,
        canActivate: [authGuard],
      },
      {
        path: 'documents',
        component: DocumentsComponent,
        canActivate: [authGuard],
      },
      {
        path: 'documents/add-edit/:DocId',
        component: AddOrEditDocumentComponent,
        canActivate: [authGuard],
      },
      { path: 'agents', component: AgentsComponent, canActivate: [authGuard] },
      {
        path: 'agents/:agentName',
        component: AddOrEditAgentsComponent,
        canActivate: [authGuard],
      },

      { path: '', redirectTo: 'chat', pathMatch: 'full' },
    ],
  },
  {
    path: 'all-documents',
    component: DocumentListComponent,
    canActivate: [authGuard],
  },
  {
    path: 'documents/:journal',
    component: DocumentListComponent,
    canActivate: [authGuard],
  },
  { path: 'editor', component: EditorComponent, canActivate: [authGuard] },
  { path: 'editor/:id', component: EditorComponent, canActivate: [authGuard] },
  {
    path: 'document/:id',
    component: DocumentDetailsComponent,
    canActivate: [authGuard],
  },

  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },

  // Support legacy 'home' route
  { path: 'home/:id', redirectTo: 'chat/:id', pathMatch: 'full' },

  // My Request routes
  {
    path: 'my-request',
    component: RequestListComponent,
    canActivate: [authGuard],
  },
  {
    path: 'my-request/add-request',
    component: AddRequestComponent,
    canActivate: [authGuard],
  },
  {
    path: 'my-request/edit-request/:id',
    component: AddRequestComponent,
    canActivate: [authGuard],
  },
  {
    path: 'my-request/details/:id',
    component: RequestDetailComponent,
    canActivate: [authGuard],
  },

    // All Request routes
  {
    path: 'all-request',
    component: RequestListComponent,
    canActivate: [authGuard],
  },
  {
    path: 'all-request/add-request',
    component: AddRequestComponent,
    canActivate: [authGuard],
  },
  {
    path: 'all-request/edit-request/:id',
    component: AddRequestComponent,
    canActivate: [authGuard],
  },
  {
    path: 'all-request/details/:id',
    component: RequestDetailComponent,
    canActivate: [authGuard],
  },

  // Project Summary routes
  {
    path: 'project-summary',
    component: ProjectSummaryComponent,
    canActivate: [authGuard],
  },
  {
    path: 'project-summary/:id',
    component: ProjectSummaryComponent,
    canActivate: [authGuard],
  },

  // Task Management routes
  {
    path: 'task-management',
    component: TaskManagementComponent,
    canActivate: [authGuard],
  },
  {
    path: 'task-management/:id',
    component: TaskManagementComponent,
    canActivate: [authGuard],
  },

  // Wildcard route for 404 - must be the last route!
  { path: '**', component: NotFoundComponent },
];
