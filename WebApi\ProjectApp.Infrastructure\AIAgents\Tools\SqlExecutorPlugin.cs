using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using ProjectApp.Core.Repositories;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Text.Json;

namespace ProjectApp.Infrastructure.AIAgents.Tools
{
    public class SqlExecutorPlugin
    {
        private readonly IDatabaseConnectionRepository _connectionRepository;
        private readonly ILogger<SqlExecutorPlugin> _logger;

        public SqlExecutorPlugin(
            IDatabaseConnectionRepository connectionRepository,
            ILogger<SqlExecutorPlugin> logger)
        {
            _connectionRepository = connectionRepository;
            _logger = logger;
        }

        [KernelFunction]
        [Description("Execute SQL query against a database connection")]
        public async Task<string> ExecuteSqlQuery(
            [Description("The SQL query to execute")] string sqlQuery,
            [Description("Database connection name")] string connectionName,
            [Description("Query timeout in seconds (default: 30)")] int timeoutSeconds = 30)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.LogInformation("Executing SQL query on connection {ConnectionName}: {Query}",
                    connectionName, sqlQuery);

                // Get database connection
                var connection = await _connectionRepository.GetByNameAsync(connectionName);
                if (connection == null)
                {
                    return JsonSerializer.Serialize(new
                    {
                        success = false,
                        error = $"Database connection '{connectionName}' not found",
                        execution_time_ms = stopwatch.ElapsedMilliseconds
                    });
                }

                if (!connection.IsActive)
                {
                    return JsonSerializer.Serialize(new
                    {
                        success = false,
                        error = $"Database connection '{connectionName}' is not active",
                        execution_time_ms = stopwatch.ElapsedMilliseconds
                    });
                }

                // No validation - allow all SQL operations for maximum flexibility
                _logger.LogInformation("Executing SQL query: {Query}", sqlQuery);

                // Execute the query
                var result = await ExecuteQueryAsync(connection.ConnectionString, connection.DatabaseType, sqlQuery, timeoutSeconds);

                stopwatch.Stop();
                result.ExecutionTimeMs = stopwatch.ElapsedMilliseconds;

                // Generate appropriate step message based on operation type
                var operationType = GetOperationType(sqlQuery);
                result.StepMessage = GenerateStepMessage(operationType, result.RowCount, result.ExecutionTimeMs);

                _logger.LogInformation("SQL {OperationType} executed successfully. Rows affected/returned: {RowCount}, Time: {ExecutionTime}ms",
                    operationType, result.RowCount, result.ExecutionTimeMs);

                return JsonSerializer.Serialize(result);
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error executing SQL query on connection {ConnectionName}", connectionName);

                return JsonSerializer.Serialize(new
                {
                    success = false,
                    error = $"Query execution failed: {ex.Message}",
                    execution_time_ms = stopwatch.ElapsedMilliseconds
                });
            }
        }

        [KernelFunction]
        [Description("Get database connection status and basic information")]
        public async Task<string> GetConnectionInfo([Description("Database connection name")] string connectionName)
        {
            try
            {
                var connection = await _connectionRepository.GetByNameAsync(connectionName);
                if (connection == null)
                {
                    return JsonSerializer.Serialize(new
                    {
                        success = false,
                        error = $"Database connection '{connectionName}' not found"
                    });
                }

                return JsonSerializer.Serialize(new
                {
                    success = true,
                    connection_name = connection.Name,
                    database_type = connection.DatabaseType,
                    is_active = connection.IsActive,
                    schema_extracted = connection.IsSchemaExtracted,
                    last_schema_extraction = connection.LastSchemaExtraction,
                    description = connection.Description
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connection info for {ConnectionName}", connectionName);
                return JsonSerializer.Serialize(new
                {
                    success = false,
                    error = $"Failed to get connection info: {ex.Message}"
                });
            }
        }

        private async Task<QueryExecutionResult> ExecuteQueryAsync(string connectionString, string databaseType, string sqlQuery, int timeoutSeconds)
        {
            var result = new QueryExecutionResult();

            using var connection = new SqlConnection(connectionString);

            await connection.OpenAsync();

            var operationType = GetOperationType(sqlQuery);

            if (operationType == "SELECT")
            {
                // For SELECT queries, use QueryAsync to get data
                var queryResult = await connection.QueryAsync(sqlQuery);
                var dataList = queryResult.ToList();

                if (dataList.Any())
                {
                    // Convert to list of dictionaries for JSON serialization
                    result.Data = dataList.Select(row =>
                    {
                        var dict = new Dictionary<string, object>();
                        var dynamicRow = (IDictionary<string, object>)row;
                        foreach (var kvp in dynamicRow)
                        {
                            dict[kvp.Key] = kvp.Value;
                        }
                        return dict;
                    }).ToList();

                    // Get column names from first row
                    if (dataList.First() is IDictionary<string, object> firstRow)
                    {
                        result.ColumnNames = firstRow.Keys.ToList();
                    }
                }

                result.Success = true;
                result.RowCount = dataList.Count;
                result.Message = $"Query executed successfully. {result.RowCount} rows returned.";
            }
            else
            {
                // For INSERT/UPDATE/DELETE/MERGE queries, use ExecuteAsync to get rows affected
                var rowsAffected = await connection.ExecuteAsync(sqlQuery);

                result.Success = true;
                result.RowCount = rowsAffected;
                result.Message = $"{operationType} operation completed successfully. {rowsAffected} rows affected.";
                result.Data = new List<Dictionary<string, object>>(); // No data returned for non-SELECT operations
                result.ColumnNames = new List<string>();
            }

            return result;
        }

        private static IDbConnection CreateDatabaseConnection(string connectionString, string databaseType)
        {
            return databaseType.ToLower() switch
            {
                "sqlserver" => new SqlConnection(connectionString),
                _ => throw new NotSupportedException($"Database type '{databaseType}' is not supported")
            };
        }

        // Validation removed - allowing all SQL operations for maximum database agent flexibility

        private static string GetOperationType(string sqlQuery)
        {
            var query = sqlQuery.Trim().ToUpper();
            if (query.StartsWith("SELECT")) return "SELECT";
            if (query.StartsWith("INSERT")) return "INSERT";
            if (query.StartsWith("UPDATE")) return "UPDATE";
            if (query.StartsWith("DELETE")) return "DELETE";
            if (query.StartsWith("MERGE")) return "MERGE";
            return "QUERY";
        }

        private static string GenerateStepMessage(string operationType, int rowCount, double executionTimeMs)
        {
            return operationType switch
            {
                "SELECT" => $"✅ **Query executed successfully!**\n📊 **Results:** {rowCount} rows returned in {executionTimeMs:F1}ms\n",
                "INSERT" => $"✅ **Insert operation completed!**\n📝 **Results:** {rowCount} row(s) inserted in {executionTimeMs:F1}ms\n",
                "UPDATE" => $"✅ **Update operation completed!**\n📝 **Results:** {rowCount} row(s) updated in {executionTimeMs:F1}ms\n",
                "DELETE" => $"✅ **Delete operation completed!**\n📝 **Results:** {rowCount} row(s) deleted in {executionTimeMs:F1}ms\n",
                "MERGE" => $"✅ **Merge operation completed!**\n📝 **Results:** {rowCount} row(s) affected in {executionTimeMs:F1}ms\n",
                _ => $"✅ **Operation completed!**\n📝 **Results:** {rowCount} row(s) affected in {executionTimeMs:F1}ms\n"
            };
        }

        private class QueryExecutionResult
        {
            public bool Success { get; set; }
            public string Message { get; set; } = string.Empty;
            public List<Dictionary<string, object>> Data { get; set; } = new();
            public List<string> ColumnNames { get; set; } = new();
            public int RowCount { get; set; }
            public double ExecutionTimeMs { get; set; }
            public string Error { get; set; }
            public string StepMessage { get; set; } = string.Empty;
        }

        // QueryValidationResult class removed - no validation needed
    }
}
