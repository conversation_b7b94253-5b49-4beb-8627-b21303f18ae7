<div class="rounded-[var(--border-radius-large)] bg-[var(--background-white)] shadow-[var(--box-shadow)]">
  <form #promptForm="ngForm" (ngSubmit)="onSubmit()" class="space-y-6">
    <!-- Short Message Field -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-text-line mr-2 text-[var(--primary-purple)]"></i>Short Message
      </label>
      <input type="text" name="shortMessage" [(ngModel)]="promptData.shortMessage" required #shortMessage="ngModel"
        class="w-full p-3 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:placeholder-gray-400"
        placeholder="Enter a short name or description">
      <div *ngIf="shortMessage.invalid && shortMessage.touched" class="text-red-500 text-xs mt-1 flex items-center">
        <i class="ri-error-warning-line mr-1"></i>Please enter a short message
      </div>
    </div>

    <!-- Prompt Field -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-file-text-line mr-2 text-[var(--primary-purple)]"></i>Prompt
      </label>
      <textarea name="prompt" [(ngModel)]="promptData.prompt" required #prompt="ngModel"
        class="w-full p-3 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 min-h-[150px] resize-none dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:placeholder-gray-400"
        placeholder="Enter your prompt">
      </textarea>
      <div *ngIf="prompt.invalid && prompt.touched" class="text-red-500 text-xs mt-1 flex items-center">
        <i class="ri-error-warning-line mr-1"></i>Please enter a prompt
      </div>
    </div>

    <!-- Workspace Select -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-folder-line mr-2 text-[var(--primary-purple)]"></i>Workspace
      </label>

      <div class="relative">
        <select name="workspaceName" [(ngModel)]="promptData.workspaceName" #workspace="ngModel"
          (ngModelChange)="promptData.agentName = ''"
          class="w-full p-3 pr-10 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 appearance-none dark:bg-gray-700 dark:text-white dark:border-gray-600">
          <option value="">Select a workspace</option>
          <option *ngFor="let workspace of workspaces" [value]="workspace.title">
            {{ workspace.title }}
          </option>
        </select>
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
        </div>
      </div>
    </div>

    <!-- Agent Select -->
    <div class="mb-4">
      <label class="font-[var(--font-weight-medium)] text-[var(--text-dark)] block mb-2">
        <i class="ri-robot-line mr-2 text-[var(--primary-purple)]"></i>Agent
      </label>

      <div class="relative">
        <select name="agentName" [(ngModel)]="promptData.agentName" #agent="ngModel"
          (ngModelChange)="promptData.workspaceName = ''"
          class="w-full p-3 pr-10 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] bg-[var(--background-white)] text-[var(--text-dark)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent transition-all duration-200 appearance-none dark:bg-gray-700 dark:text-white dark:border-gray-600">
          <option value="">Select an agent</option>
          <option *ngFor="let agent of agents" [value]="agent.agentName">
            {{ agent.agentName }}
          </option>
        </select>
        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <i class="ri-arrow-down-s-line text-[var(--text-medium-gray)]"></i>
        </div>
      </div>
    </div>

    <div class="mb-4 p-3 bg-gray-50 border dark:bg-gray-700 border-blue-200 rounded-md">
      <p class="text-sm text-gray-700 dark:text-gray-200">
        <i class="ri-information-line mr-1"></i>
        Select either a workspace or an agent for this prompt. You cannot select both.
      </p>
    </div>

    <!-- Buttons -->
    <div class="flex justify-end space-x-4 pt-4 border-t border-[var(--hover-blue-gray)]">
      <button type="button" (click)="onCancel()"
        class="bg-[var(--hover-blue-gray)] text-[var(--text-dark)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] transition-all duration-300 outline-none border-none cursor-pointer hover:shadow-md flex items-center gap-2">
        <i class="ri-close-line"></i>
        Cancel
      </button>
      <button type="submit" [disabled]="!promptForm.form.valid"
        class="bg-[var(--primary-purple)] text-[var(--background-white)] px-4 py-2 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-[var(--text-dark)] transition-all duration-300 outline-none border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 hover:shadow-md">
        <i class="ri-save-line"></i>
        {{ promptData.id ? 'Update' : 'Add' }}
      </button>
    </div>
  </form>
</div>
