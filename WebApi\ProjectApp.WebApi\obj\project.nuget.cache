{"version": 2, "dgSpecHash": "TwgjPTIMz7o=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\source\\repos\\project-app\\WebApi\\ProjectApp.WebApi\\ProjectApp.WebApi.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\automapper\\12.0.1\\automapper.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\automapper.extensions.microsoft.dependencyinjection\\12.0.1\\automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.core\\3.7.402.24\\awssdk.core.3.7.402.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\awssdk.s3\\3.7.415.23\\awssdk.s3.3.7.415.23.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.ai.contentsafety\\1.0.0\\azure.ai.contentsafety.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.ai.formrecognizer\\4.1.0\\azure.ai.formrecognizer.4.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.ai.openai\\2.2.0-beta.4\\azure.ai.openai.2.2.0-beta.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.core\\1.44.1\\azure.core.1.44.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.identity\\1.13.2\\azure.identity.1.13.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.search.documents\\11.6.0\\azure.search.documents.11.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.blobs\\12.24.0\\azure.storage.blobs.12.24.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.common\\12.23.0\\azure.storage.common.12.23.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\azure.storage.queues\\12.22.0\\azure.storage.queues.12.22.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bcrypt.net-core\\1.6.0\\bcrypt.net-core.1.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.4.0\\bouncycastle.cryptography.2.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\closedxml\\0.104.2\\closedxml.0.104.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\closedxml.parser\\1.2.0\\closedxml.parser.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.highperformance\\8.4.0\\communitytoolkit.highperformance.8.4.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dapper\\2.1.66\\dapper.2.1.66.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\dnsclient\\1.6.1\\dnsclient.1.6.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml\\3.3.0\\documentformat.openxml.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\documentformat.openxml.framework\\3.3.0\\documentformat.openxml.framework.3.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.clients.elasticsearch\\8.12.1\\elastic.clients.elasticsearch.8.12.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\elastic.transport\\0.4.18\\elastic.transport.0.4.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\excelnumberformat\\1.1.0\\excelnumberformat.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation\\11.5.1\\fluentvalidation.11.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.aspnetcore\\11.3.0\\fluentvalidation.aspnetcore.11.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\fluentvalidation.dependencyinjectionextensions\\11.5.1\\fluentvalidation.dependencyinjectionextensions.11.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.27.1\\google.protobuf.3.27.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hangfire\\1.8.18\\hangfire.1.8.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hangfire.aspnetcore\\1.8.18\\hangfire.aspnetcore.1.8.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hangfire.core\\1.8.18\\hangfire.core.1.8.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hangfire.netcore\\1.8.18\\hangfire.netcore.1.8.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\hangfire.sqlserver\\1.8.18\\hangfire.sqlserver.1.8.18.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\htmlagilitypack\\1.12.0\\htmlagilitypack.1.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jetbrains.annotations\\2021.2.0\\jetbrains.annotations.2021.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\json.more.net\\1.9.0\\json.more.net.1.9.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonpointer.net\\3.0.3\\jsonpointer.net.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\jsonschema.net\\5.4.2\\jsonschema.net.5.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\llamasharp\\0.23.0\\llamasharp.0.23.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mailkit\\4.8.0\\mailkit.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr\\12.4.1\\mediatr.12.4.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mediatr.contracts\\2.0.1\\mediatr.contracts.2.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.aspnetcore.authentication.jwtbearer\\8.0.2\\microsoft.aspnetcore.authentication.jwtbearer.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\9.0.3\\microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.cryptography\\8.0.0\\microsoft.bcl.cryptography.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.hashcode\\1.1.1\\microsoft.bcl.hashcode.1.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient\\6.0.1\\microsoft.data.sqlclient.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlclient.sni.runtime\\6.0.2\\microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ai\\9.3.0-preview.1.25161.3\\microsoft.extensions.ai.9.3.0-preview.1.25161.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.ai.abstractions\\9.3.0-preview.1.25161.3\\microsoft.extensions.ai.abstractions.9.3.0-preview.1.25161.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.apidescription.server\\6.0.5\\microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\9.0.3\\microsoft.extensions.caching.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.1\\microsoft.extensions.caching.memory.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.3\\microsoft.extensions.configuration.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.3\\microsoft.extensions.configuration.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.binder\\9.0.3\\microsoft.extensions.configuration.binder.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.commandline\\9.0.3\\microsoft.extensions.configuration.commandline.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.environmentvariables\\9.0.3\\microsoft.extensions.configuration.environmentvariables.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\9.0.3\\microsoft.extensions.configuration.fileextensions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\9.0.3\\microsoft.extensions.configuration.json.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.usersecrets\\9.0.3\\microsoft.extensions.configuration.usersecrets.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.3\\microsoft.extensions.dependencyinjection.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.3\\microsoft.extensions.dependencyinjection.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\9.0.0\\microsoft.extensions.dependencymodel.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics\\9.0.3\\microsoft.extensions.diagnostics.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.diagnostics.abstractions\\9.0.3\\microsoft.extensions.diagnostics.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\9.0.3\\microsoft.extensions.fileproviders.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\9.0.3\\microsoft.extensions.fileproviders.physical.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\9.0.3\\microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting\\9.0.3\\microsoft.extensions.hosting.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.hosting.abstractions\\9.0.3\\microsoft.extensions.hosting.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.http\\9.0.3\\microsoft.extensions.http.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.3\\microsoft.extensions.logging.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.3\\microsoft.extensions.logging.abstractions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.configuration\\9.0.3\\microsoft.extensions.logging.configuration.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.console\\9.0.3\\microsoft.extensions.logging.console.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.3\\microsoft.extensions.logging.debug.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventlog\\9.0.3\\microsoft.extensions.logging.eventlog.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.eventsource\\9.0.3\\microsoft.extensions.logging.eventsource.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.3\\microsoft.extensions.options.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options.configurationextensions\\9.0.3\\microsoft.extensions.options.configurationextensions.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.3\\microsoft.extensions.primitives.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.vectordata.abstractions\\9.0.0-preview.1.25161.1\\microsoft.extensions.vectordata.abstractions.9.0.0-preview.1.25161.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client\\4.67.2\\microsoft.identity.client.4.67.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identity.client.extensions.msal\\4.67.2\\microsoft.identity.client.extensions.msal.4.67.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.abstractions\\7.5.0\\microsoft.identitymodel.abstractions.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.jsonwebtokens\\7.5.0\\microsoft.identitymodel.jsonwebtokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.logging\\7.5.0\\microsoft.identitymodel.logging.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols\\7.5.0\\microsoft.identitymodel.protocols.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.protocols.openidconnect\\7.5.0\\microsoft.identitymodel.protocols.openidconnect.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.identitymodel.tokens\\7.5.0\\microsoft.identitymodel.tokens.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory\\0.98.250324.1\\microsoft.kernelmemory.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.abstractions\\0.98.250324.1\\microsoft.kernelmemory.abstractions.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.anthropic\\0.98.250324.1\\microsoft.kernelmemory.ai.anthropic.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.azureopenai\\0.98.250324.1\\microsoft.kernelmemory.ai.azureopenai.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.llamasharp\\0.98.250324.1\\microsoft.kernelmemory.ai.llamasharp.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.ollama\\0.98.250324.1\\microsoft.kernelmemory.ai.ollama.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.onnx\\0.98.250324.1\\microsoft.kernelmemory.ai.onnx.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.openai\\0.98.250324.1\\microsoft.kernelmemory.ai.openai.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.ai.tiktoken\\0.98.250324.1\\microsoft.kernelmemory.ai.tiktoken.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.chunkers\\0.98.250324.1\\microsoft.kernelmemory.chunkers.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.core\\0.98.250324.1\\microsoft.kernelmemory.core.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.dataformats.azureaidocintel\\0.98.250324.1\\microsoft.kernelmemory.dataformats.azureaidocintel.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.documentstorage.awss3\\0.98.250324.1\\microsoft.kernelmemory.documentstorage.awss3.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.documentstorage.azureblobs\\0.98.250324.1\\microsoft.kernelmemory.documentstorage.azureblobs.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.azureaisearch\\0.98.250324.1\\microsoft.kernelmemory.memorydb.azureaisearch.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.elasticsearch\\0.98.250324.1\\microsoft.kernelmemory.memorydb.elasticsearch.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.postgres\\0.98.250324.1\\microsoft.kernelmemory.memorydb.postgres.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.qdrant\\0.98.250324.1\\microsoft.kernelmemory.memorydb.qdrant.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.redis\\0.98.250324.1\\microsoft.kernelmemory.memorydb.redis.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.memorydb.sqlserver\\0.98.250324.1\\microsoft.kernelmemory.memorydb.sqlserver.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.mongodbatlas\\0.98.250324.1\\microsoft.kernelmemory.mongodbatlas.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.orchestration.azurequeues\\0.98.250324.1\\microsoft.kernelmemory.orchestration.azurequeues.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.orchestration.rabbitmq\\0.98.250324.1\\microsoft.kernelmemory.orchestration.rabbitmq.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.safety.azureaicontentsafety\\0.98.250324.1\\microsoft.kernelmemory.safety.azureaicontentsafety.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.semantickernelplugin\\0.98.250324.1\\microsoft.kernelmemory.semantickernelplugin.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.kernelmemory.webclient\\0.98.250324.1\\microsoft.kernelmemory.webclient.0.98.250324.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.onnxruntime\\1.20.1\\microsoft.ml.onnxruntime.1.20.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.onnxruntime.managed\\1.20.1\\microsoft.ml.onnxruntime.managed.1.20.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.onnxruntimegenai\\0.6.0\\microsoft.ml.onnxruntimegenai.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.onnxruntimegenai.managed\\0.6.0\\microsoft.ml.onnxruntimegenai.managed.0.6.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers\\1.0.2\\microsoft.ml.tokenizers.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.cl100kbase\\1.0.2\\microsoft.ml.tokenizers.data.cl100kbase.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.o200kbase\\1.0.2\\microsoft.ml.tokenizers.data.o200kbase.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.ml.tokenizers.data.p50kbase\\1.0.2\\microsoft.ml.tokenizers.data.p50kbase.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.compilers.toolset\\4.12.0\\microsoft.net.compilers.toolset.4.12.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi\\1.6.22\\microsoft.openapi.1.6.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.openapi.readers\\1.6.22\\microsoft.openapi.readers.1.6.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel\\1.44.0\\microsoft.semantickernel.1.44.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.abstractions\\1.44.0\\microsoft.semantickernel.abstractions.1.44.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.agents.abstractions\\1.44.0-preview\\microsoft.semantickernel.agents.abstractions.1.44.0-preview.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.agents.core\\1.44.0-preview\\microsoft.semantickernel.agents.core.1.44.0-preview.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.connectors.azureopenai\\1.44.0\\microsoft.semantickernel.connectors.azureopenai.1.44.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.connectors.google\\1.44.0-alpha\\microsoft.semantickernel.connectors.google.1.44.0-alpha.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.connectors.ollama\\1.44.0-alpha\\microsoft.semantickernel.connectors.ollama.1.44.0-alpha.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.connectors.openai\\1.44.0\\microsoft.semantickernel.connectors.openai.1.44.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.core\\1.44.0\\microsoft.semantickernel.core.1.44.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.semantickernel.plugins.openapi\\1.40.1\\microsoft.semantickernel.plugins.openapi.1.40.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mimekit\\4.8.0\\mimekit.4.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\modelcontextprotocol\\0.1.0-preview.6\\modelcontextprotocol.0.1.0-preview.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.bson\\3.2.1\\mongodb.bson.3.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mongodb.driver\\3.2.1\\mongodb.driver.3.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nettopologysuite\\2.5.0\\nettopologysuite.2.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\8.0.3\\npgsql.8.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nredisstack\\0.13.2\\nredisstack.0.13.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\ollamasharp\\5.1.7\\ollamasharp.5.1.7.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openai\\2.2.0-beta.4\\openai.2.2.0-beta.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry\\1.11.2\\opentelemetry.1.11.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api\\1.11.2\\opentelemetry.api.1.11.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.api.providerbuilderextensions\\1.11.2\\opentelemetry.api.providerbuilderextensions.1.11.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.exporter.console\\1.11.2\\opentelemetry.exporter.console.1.11.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\opentelemetry.extensions.hosting\\1.11.2\\opentelemetry.extensions.hosting.1.11.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pdfpig\\0.1.10\\pdfpig.0.1.10.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pgvector\\0.3.1\\pgvector.0.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pipelines.sockets.unofficial\\2.2.8\\pipelines.sockets.unofficial.2.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\polly.core\\8.5.2\\polly.core.8.5.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rabbitmq.client\\7.1.2\\rabbitmq.client.7.1.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rbush\\4.0.0\\rbush.4.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog\\4.2.0\\serilog.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.aspnetcore\\9.0.0\\serilog.aspnetcore.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.hosting\\9.0.0\\serilog.extensions.hosting.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.extensions.logging\\9.0.0\\serilog.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.formatting.compact\\3.0.0\\serilog.formatting.compact.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.settings.configuration\\9.0.0\\serilog.settings.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.console\\6.0.0\\serilog.sinks.console.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.debug\\3.0.0\\serilog.sinks.debug.3.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.file\\6.0.0\\serilog.sinks.file.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\serilog.sinks.mssqlserver\\8.1.0\\serilog.sinks.mssqlserver.8.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpcompress\\0.30.1\\sharpcompress.0.30.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpyaml\\2.1.1\\sharpyaml.2.1.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sixlabors.fonts\\1.0.0\\sixlabors.fonts.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\snappier\\1.0.0\\snappier.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stackexchange.redis\\2.8.16\\stackexchange.redis.2.8.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore\\7.2.0\\swashbuckle.aspnetcore.7.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swagger\\7.2.0\\swashbuckle.aspnetcore.swagger.7.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggergen\\7.2.0\\swashbuckle.aspnetcore.swaggergen.7.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\swashbuckle.aspnetcore.swaggerui\\7.2.0\\swashbuckle.aspnetcore.swaggerui.7.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.clientmodel\\1.4.0-beta.1\\system.clientmodel.1.4.0-beta.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\9.0.3\\system.configuration.configurationmanager.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.diagnosticsource\\9.0.3\\system.diagnostics.diagnosticsource.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.diagnostics.eventlog\\9.0.3\\system.diagnostics.eventlog.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.formats.asn1\\8.0.1\\system.formats.asn1.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.identitymodel.tokens.jwt\\7.5.0\\system.identitymodel.tokens.jwt.7.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.hashing\\6.0.0\\system.io.hashing.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.packaging\\8.0.1\\system.io.packaging.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\9.0.3\\system.io.pipelines.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.linq.async\\6.0.1\\system.linq.async.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory.data\\9.0.3\\system.memory.data.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.net.serversentevents\\10.0.0-preview.2.25163.2\\system.net.serversentevents.10.0.0-preview.2.25163.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.tensors\\9.0.3\\system.numerics.tensors.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.caching\\9.0.3\\system.runtime.caching.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\5.0.0\\system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\system.security.cryptography.pkcs.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.protecteddata\\9.0.3\\system.security.cryptography.protecteddata.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\9.0.3\\system.text.encodings.web.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\9.0.3\\system.text.json.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\9.0.3\\system.threading.channels.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.ratelimiting\\8.0.0\\system.threading.ratelimiting.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\zstdsharp.port\\0.7.3\\zstdsharp.port.0.7.3.nupkg.sha512"], "logs": []}