namespace ProjectApp.Core.Models
{
    public class DatabaseSchema
    {
        public Guid Id { get; set; }
        
        public Guid DatabaseConnectionId { get; set; }
        
        public string DatabaseName { get; set; } = string.Empty;
        
        public string SchemaName { get; set; } = string.Empty;
        
        public string TableName { get; set; } = string.Empty;
        
        public string ColumnName { get; set; } = string.Empty;
        
        public string DataType { get; set; } = string.Empty;
        
        public bool IsNullable { get; set; }
        
        public bool IsPrimaryKey { get; set; }
        
        public bool IsForeignKey { get; set; }
        
        public string? ForeignKeyTable { get; set; }
        
        public string? ForeignKeyColumn { get; set; }
        
        public int? MaxLength { get; set; }
        
        public int? Precision { get; set; }
        
        public int? Scale { get; set; }
        
        public string? DefaultValue { get; set; }
        
        public string? Description { get; set; }
        
        public DateTime ExtractedAt { get; set; } = DateTime.UtcNow;
        
        // Navigation property
        public DatabaseConnection? DatabaseConnection { get; set; }
    }
    
    public class DatabaseTable
    {
        public string TableName { get; set; } = string.Empty;
        public string SchemaName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public List<DatabaseColumn> Columns { get; set; } = new();
        public List<DatabaseRelationship> Relationships { get; set; } = new();
    }
    
    public class DatabaseColumn
    {
        public string ColumnName { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public bool IsNullable { get; set; }
        public bool IsPrimaryKey { get; set; }
        public bool IsForeignKey { get; set; }
        public int? MaxLength { get; set; }
        public int? Precision { get; set; }
        public int? Scale { get; set; }
        public string? DefaultValue { get; set; }
        public string? Description { get; set; }
    }
    
    public class DatabaseRelationship
    {
        public string ForeignKeyColumn { get; set; } = string.Empty;
        public string ReferencedTable { get; set; } = string.Empty;
        public string ReferencedColumn { get; set; } = string.Empty;
        public string RelationshipType { get; set; } = string.Empty; // OneToOne, OneToMany, ManyToMany
    }
}
