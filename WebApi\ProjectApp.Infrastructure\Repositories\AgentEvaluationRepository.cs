using Dapper;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Models;
using ProjectApp.Core.Repositories;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace ProjectApp.Infrastructure.Repositories
{
    public class AgentEvaluationRepository : IAgentEvaluationRepository
    {
        private readonly IDbConnection _dbConnection;

        public AgentEvaluationRepository(IDbConnection dbConnection)
        {
            _dbConnection = dbConnection;
        }

        public async Task<IEnumerable<AgentEvaluation>> GetAllAsync()
        {
            var sql = "SELECT * FROM AgentEvaluations ORDER BY CreatedAt DESC";
            return await _dbConnection.QueryAsync<AgentEvaluation>(sql);
        }

        public async Task<IEnumerable<AgentEvaluation>> GetByAgentNameAsync(string agentName)
        {
            var sql = "SELECT * FROM AgentEvaluations WHERE AgentName = @AgentName ORDER BY CreatedAt DESC";
            return await _dbConnection.QueryAsync<AgentEvaluation>(sql, new { AgentName = agentName });
        }

        public async Task<AgentEvaluation> GetByIdAsync(Guid id)
        {
            var sql = "SELECT * FROM AgentEvaluations WHERE Id = @Id";
            return await _dbConnection.QueryFirstOrDefaultAsync<AgentEvaluation>(sql, new { Id = id });
        }

        public async Task<Guid> CreateAsync(AgentEvaluation agentEvaluation)
        {
            var sql = @"INSERT INTO AgentEvaluations 
                      (Id, AgentName, Prompt, ExpectedOutput, Output, Score, CreatedAt, EvaluatedAt) 
                      VALUES 
                      (@Id, @AgentName, @Prompt, @ExpectedOutput, @Output, @Score, @CreatedAt, @EvaluatedAt)";
            
            await _dbConnection.ExecuteAsync(sql, agentEvaluation);
            return agentEvaluation.Id;
        }

        public async Task<bool> UpdateAsync(AgentEvaluation agentEvaluation)
        {
            var sql = @"UPDATE AgentEvaluations 
                      SET Prompt = @Prompt,
                          Output = @Output, 
                          Score = @Score,
                          ExpectedOutput = @ExpectedOutput,
                          EvaluatedAt = @EvaluatedAt
                      WHERE Id = @Id";
            
            var result = await _dbConnection.ExecuteAsync(sql, agentEvaluation);
            return result > 0;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var sql = "DELETE FROM AgentEvaluations WHERE Id = @Id";
            var result = await _dbConnection.ExecuteAsync(sql, new { Id = id });
            return result > 0;
        }

        public async Task<double> GetAverageScoreByAgentNameAsync(string agentName)
        {
            var sql = "SELECT AVG(Score) FROM AgentEvaluations WHERE AgentName = @AgentName AND Score IS NOT NULL";
            var result = await _dbConnection.ExecuteScalarAsync<double?>(sql, new { AgentName = agentName });
            return result ?? 0;
        }
    }
}
