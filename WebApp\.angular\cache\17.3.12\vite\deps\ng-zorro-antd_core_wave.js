import {
  NZ_WAVE_GLOBAL_CONFIG,
  NZ_WAVE_GLOBAL_DEFAULT_CONFIG,
  NzWaveDirective,
  NzWaveModule,
  Nz<PERSON>ave<PERSON><PERSON><PERSON>,
  provideNzWave
} from "./chunk-DDWHBHM2.js";
import "./chunk-G2EUFFDC.js";
import "./chunk-EU5ZW54Q.js";
import "./chunk-A6VXVJH4.js";
import "./chunk-M2RH4J7J.js";
import "./chunk-V5C6TIEW.js";
import "./chunk-F5QSOM2P.js";
import "./chunk-M644BQ5H.js";
import "./chunk-QI6NZCQM.js";
import "./chunk-CRSXJIOC.js";
import "./chunk-FBTKCNEF.js";
import "./chunk-AQGUTHVG.js";
import "./chunk-EIB7IA3J.js";
export {
  NZ_WAVE_GLOBAL_CONFIG,
  NZ_WAVE_GLOBAL_DEFAULT_CONFIG,
  NzWaveDirective,
  NzWaveModule,
  Nz<PERSON>ave<PERSON><PERSON><PERSON>,
  provideNzW<PERSON>
};
//# sourceMappingURL=ng-zorro-antd_core_wave.js.map
