using ProjectApp.Core.Dtos;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ProjectApp.Core.Repositories
{
    public interface IUserFavoritesRepository
    {
        Task<ResponseMessage> ToggleFavorite(string userEmail, string itemKey, string itemType);
        Task<List<UserFavoriteDto>> GetFavorites(string userEmail);
        Task<ResponseMessage> IsFavorite(string userEmail, string itemKey, string itemType);
    }
}
