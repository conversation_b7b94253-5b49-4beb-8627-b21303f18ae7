using Microsoft.AspNetCore.Mvc;
using ProjectApp.Core.Dtos;
using ProjectApp.Core.Repositories;
using ProjectApp.Infrastructure.Services;
using ProjectApp.Infrastructure;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ProjectApp.Core.Models;

namespace ProjectApp.WebApi.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PluginController : ControllerBase
    {
        private readonly IPluginRepository _pluginRepository;
        private readonly PluginScannerService _pluginScannerService;
        private readonly OpenApiPluginService _openApiPluginService;
        private readonly AIService _aiService;

        public PluginController(
            IPluginRepository pluginRepository,
            PluginScannerService pluginScannerService,
            OpenApiPluginService openApiPluginService,
            AIService aiService)
        {
            _pluginRepository = pluginRepository;
            _pluginScannerService = pluginScannerService;
            _openApiPluginService = openApiPluginService;
            _aiService = aiService;
        }

        [HttpGet("GetAll")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetAll()
        {
            var plugins = await _pluginRepository.GetAllPluginsAsync();
            return Ok(plugins);
        }

        [HttpGet("GetByName/{pluginName}")]
        public async Task<ActionResult<PluginResponseDto>> GetByName(string pluginName)
        {
            var plugin = await _pluginRepository.GetPluginByNameAsync(pluginName);
            if (plugin == null)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            return Ok(plugin);
        }
        [HttpPost("Create")]
        public async Task<ActionResult<PluginResponseDto>> Create(PluginRequestDto plugin)
        {
            var result = await _pluginRepository.CreatePluginAsync(plugin);

            // Automatically add navigation entry for the new plugin
            try
            {
                var navigationEntry = new NavigationEntryDto
                {
                    Title = plugin.PluginName,
                    Description = $"Plugin: {plugin.PluginName} ({plugin.Type})",
                    Route = $"/plugins/{plugin.PluginName}",
                    NavigationType = "Plugin",
                    Icon = "ri-plug-line"
                };

                await _aiService.AddNavigationEntry(navigationEntry);
            }
            catch (Exception ex)
            {
                // Log but don't fail the plugin creation if navigation entry fails
                Console.WriteLine($"Warning: Failed to add navigation entry for plugin {plugin.PluginName}: {ex.Message}");
            }

            return Ok(result);
        }

        [HttpPut("Update")]
        public async Task<ActionResult<PluginResponseDto>> Update(PluginRequestDto plugin)
        {
            if (!plugin.Id.HasValue)
                return BadRequest(new { IsError = true, Message = "Plugin ID is required for updates" });

            var result = await _pluginRepository.UpdatePluginAsync(plugin);
            if (result == null)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            return Ok(result);
        }
        [HttpDelete("Delete/{id}")]
        public async Task<ActionResult> Delete(Guid id)
        {
            // Get plugin name before deletion for navigation cleanup
            string pluginName = "";
            try
            {
                var plugin = await _pluginRepository.GetPluginByIdAsync(id);
                pluginName = plugin?.PluginName ?? "";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Warning: Could not retrieve plugin name for navigation cleanup: {ex.Message}");
            }

            var result = await _pluginRepository.DeletePluginAsync(id);
            if (!result)
                return NotFound(new { IsError = true, Message = "Plugin not found" });

            // Try to remove navigation entry for deleted plugin
            if (!string.IsNullOrEmpty(pluginName))
            {
                try
                {
                    await _aiService.DeleteSingleMemory("Navigation-Plugin-" + id, "Navigation");
                    Console.WriteLine($"Plugin {pluginName} deleted - navigation entry should be removed");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Warning: Failed to remove navigation entry for deleted plugin {pluginName}: {ex.Message}");
                }
            }

            return Ok(new { IsError = false, Message = "Plugin deleted successfully" });
        }
        [HttpGet("SyncPlugins")]
        public async Task<ActionResult<List<PluginResponseDto>>> SyncPlugins()
        {
            try
            {
                var results = await _pluginScannerService.SyncPluginsAsync();

                // Add navigation entries for newly synced plugins
                foreach (var plugin in results)
                {
                    try
                    {
                        var navigationEntry = new NavigationEntryDto
                        {
                            Title = plugin.PluginName,
                            Description = $"Plugin: {plugin.PluginName} ({plugin.Type})",
                            Route = $"/plugins/{plugin.PluginName}",
                            NavigationType = "Plugin",
                            Icon = "ri-plug-line"
                        };

                        await _aiService.AddNavigationEntry(navigationEntry);
                    }
                    catch (Exception ex)
                    {
                        // Log but don't fail the sync if navigation entry fails
                        Console.WriteLine($"Warning: Failed to add navigation entry for synced plugin {plugin.PluginName}: {ex.Message}");
                    }
                }

                return Ok(results);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error syncing plugins: {ex.Message}" });
            }
        }
        [HttpPost("CreateFromOpenApi")]
        public async Task<ActionResult<PluginResponseDto>> CreateFromOpenApi([FromBody] OpenApiPluginRequestDto request)
        {
            if (string.IsNullOrEmpty(request.Url))
                return BadRequest(new { IsError = true, Message = "OpenAPI URL is required" });

            if (string.IsNullOrEmpty(request.PluginName))
                return BadRequest(new { IsError = true, Message = "Plugin name is required" });

            try
            {
                var result = await _openApiPluginService.CreateFromOpenApiUrl(request.Url, request.PluginName);

                // Automatically add navigation entry for the new OpenAPI plugin
                try
                {
                    var navigationEntry = new NavigationEntryDto
                    {
                        Title = request.PluginName,
                        Description = $"OpenAPI Plugin: {request.PluginName}",
                        Route = $"/plugins/{request.PluginName}",
                        NavigationType = "Plugin",
                        Icon = "ri-plug-line"
                    };

                    await _aiService.AddNavigationEntry(navigationEntry);
                }
                catch (Exception ex)
                {
                    // Log but don't fail the plugin creation if navigation entry fails
                    Console.WriteLine($"Warning: Failed to add navigation entry for OpenAPI plugin {request.PluginName}: {ex.Message}");
                }

                return Ok(new
                {
                    IsError = false,
                    Message = "Successfully created plugin from OpenAPI",
                    Plugin = result
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error creating plugin from OpenAPI: {ex.Message}" });
            }
        }

        [HttpPost("ResyncOpenApiPlugin/{pluginName}")]
        public async Task<ActionResult<PluginResponseDto>> ResyncOpenApiPlugin(string pluginName)
        {
            try
            {
                var result = await _openApiPluginService.ResyncOpenApiPlugin(pluginName);
                return Ok(new
                {
                    IsError = false,
                    Message = "Successfully re-synced OpenAPI plugin",
                    Plugin = result
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error re-syncing OpenAPI plugin: {ex.Message}" });
            }
        }

        [HttpGet("GetAllPluginNames")]
        public async Task<ActionResult<ResponseMessageList>> GetAllPluginNames()
        {
            var pluginNames = await _pluginRepository.GetAllPluginsNameAsync();
            return Ok(pluginNames);
        }
        [HttpGet("GetAllOpenAiPlugins")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetAllOpenAiPlugins()
        {
            var openAiPlugins = await _pluginRepository.GetAllOpenAiPluginsAsync();
            return Ok(openAiPlugins);
        }

        [HttpGet("GetByAgentName/{agentName}")]
        public async Task<ActionResult<List<PluginResponseDto>>> GetByAgentName(string agentName)
        {
            try
            {
                var plugins = await _pluginRepository.GetPluginsByAgentNameAsync(agentName);
                return Ok(plugins);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error retrieving plugins for agent: {ex.Message}" });
            }
        }

        [HttpGet("CountByAgentName/{agentName}")]
        public async Task<ActionResult<int>> CountByAgentName(string agentName)
        {
            try
            {
                var count = await _pluginRepository.CountPluginsByAgentNameAsync(agentName);
                return Ok(count);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { IsError = true, Message = $"Error counting plugins for agent: {ex.Message}" });
            }
        }
    }
    public class OpenApiPluginRequestDto
    {
        public string Url { get; set; } = string.Empty;
        public string PluginName { get; set; } = string.Empty;
    }
}