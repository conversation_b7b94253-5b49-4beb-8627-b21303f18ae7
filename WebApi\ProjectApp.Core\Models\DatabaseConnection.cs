using System.ComponentModel.DataAnnotations;

namespace ProjectApp.Core.Models
{
    public class DatabaseConnection
    {
        public Guid Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string DatabaseType { get; set; } = string.Empty; // SqlServer, MySQL, PostgreSQL, etc.
        
        [Required]
        public string ConnectionString { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedAt { get; set; }
        
        [StringLength(100)]
        public string? CreatedBy { get; set; }
        
        [StringLength(100)]
        public string? UpdatedBy { get; set; }
        
        // Schema extraction status
        public bool IsSchemaExtracted { get; set; } = false;
        
        public DateTime? LastSchemaExtraction { get; set; }
        
        public string? SchemaExtractionStatus { get; set; } // Success, Failed, InProgress
        
        public string? SchemaExtractionError { get; set; }
    }
}
